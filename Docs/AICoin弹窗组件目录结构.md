# AICoin-iOS 弹窗组件目录结构

AICoin-iOS项目提供了一套完整的自定义弹窗系统，基于`AICBaseCustomPresentationViewController`基类构建。以下是弹窗组件的目录结构，展示了各个弹窗类之间的继承关系和用途。

## 基础转场组件

```
AICBaseCustomPresentationViewController  // 所有自定义弹窗的基类
├── AICPresentationController            // 自定义呈现控制器
└── AICDismissInteractiveTransition      // 交互式消失转场
```

## 警告框组件（居中显示）

```
AICBaseCustomPresentationViewController
└── AICBaseAlertController                // 基础警告控制器
    ├── AICCustomViewAlertController      // 自定义视图警告控制器
    │   └── AICInfoAlertController        // 信息警告控制器（标题+消息+按钮）
    │       └── NewVersionAlertViewController  // 版本更新弹窗
    │           └── AgreementAlertViewController  // 协议提示弹窗
    └── AICAlertViewController            // 项目中常用的警告控制器
        ├── TickerPipExceedMaxAlertVC     // 悬浮窗超出最大数量提示弹窗
        ├── CandleChartMessageAlertViewController  // K线图表消息提示弹窗
        └── 其他业务相关警告控制器
```

## 操作表组件（底部弹出）

```
AICBaseCustomPresentationViewController
└── AICBaseActionSheetController          // 基础操作表控制器
    ├── AICCustomViewActionSheetController  // 自定义视图操作表控制器
    │   └── AICInfoActionSheetController    // 信息操作表控制器（标题+描述+按钮）
    └── 其他业务相关操作表控制器
```

## 特定功能弹窗

```
AICBaseCustomPresentationViewController
├── TickerFuturesSearchViewController     // 合约搜索弹窗
├── CustomTimeProAlertViewController      // 自定义时间PRO提示弹窗
├── RealtimePerformanceReportViewController  // 实时表现报告弹窗
├── AIMobilePresentViewController         // AI移动端展示弹窗
└── 其他业务相关弹窗
```

## 气泡弹窗

```
UIViewController
└── AICPopoverViewController              // 气泡弹窗基类
    ├── AICPopoverListViewController      // 列表气泡弹窗
    │   └── AICPopoverViewForBottomWarningViewController  // 底部预警气泡弹窗
    └── PCTipPopoverContentViewController  // PC端提示气泡弹窗
```

## 传统弹窗（旧版）

```
UIView
└── AICActionSheet                        // 旧版操作表
```

## 弹窗组件使用频率

在项目中，以下弹窗组件使用频率较高：

1. **AICAlertViewController** - 最常用的警告控制器，用于显示提示信息和操作按钮
2. **AICCustomViewAlertController** - 用于显示完全自定义内容的警告弹窗
3. **AICBaseActionSheetController** - 用于从底部弹出的菜单选项
4. **AICInfoAlertController** - 用于显示标准格式的信息提示

## 弹窗组件特点

1. **自定义转场动画** - 所有弹窗都使用自定义转场动画，提供统一的视觉体验
2. **主题适配** - 支持日间/夜间主题切换
3. **交互式关闭** - 支持点击背景或滑动手势关闭弹窗
4. **键盘适配** - 自动处理键盘显示/隐藏时的布局调整
5. **屏幕旋转适配** - 自动适配屏幕方向变化
6. **设备适配** - 在不同尺寸的设备上保持一致的体验

## 弹窗样式

AICoin弹窗系统支持两种主要样式：

1. **Alert样式** - 居中显示的弹窗，适用于重要信息提示和确认操作
2. **ActionSheet样式** - 从底部滑出的菜单，适用于提供多个操作选项

每种样式都可以通过自定义视图控制器进一步定制，以满足特定的业务需求。
