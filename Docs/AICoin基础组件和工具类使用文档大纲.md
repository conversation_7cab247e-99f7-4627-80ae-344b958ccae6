# AICoin-iOS 基础组件和工具类使用文档大纲

## 目录

1. [概述](#1-概述)
2. [主题管理](#2-主题管理)
3. [多语言支持](#3-多语言支持)
4. [布局与UI组件](#4-布局与ui组件)
5. [数据存储与序列化](#5-数据存储与序列化)
6. [网络组件](#6-网络组件)
7. [基础UI组件](#7-基础ui组件)
8. [工具类](#8-工具类)
9. [数据绑定](#9-数据绑定)
10. [最佳实践](#10-最佳实践)

## 1. 概述
- 项目架构简介
- 文档目的和使用方法
- 基础组件和工具类的分类

## 2. 主题管理
- 主题系统概述
  - 白天/黑夜两种主题模式
  - K线独立主题管理
  - SSBThemeManager全局管理
- 主题资源管理
  - 颜色管理（BaseThemeProtocol）
  - 图片资源（模块化管理）
- 使用方法
  - 获取主题图片：`UIImage.模块.image(name: "图片名")`
  - 获取主题颜色：`UIColor.模块.current.颜色属性`
  - 主题切换与监听
- 自定义主题实现
  - 创建主题协议和实现类
  - 扩展UIColor

## 3. 多语言支持
- 多语言系统概述
  - 支持简中、繁中和英文
  - SystemConfig管理语言状态
  - 切换时替换rootViewController
- 使用方法
  - 获取本地化文本：`"文本".模块.localized`
  - 语言切换API
  - 获取当前语言
- 添加新的本地化文本
  - Localizable.strings文件配置
  - 模块化本地化扩展

## 4. 布局与UI组件
- 布局工具
  - SnapKit（推荐使用）
  - Masonry（仅用于OC旧UI）
  - Bees（简洁但可能不再维护）
  - 安全区域适配
- 空视图
  - DZNEmptyDataSet集成
  - BaseEmptyDelegateModel默认实现
  - 自定义空视图
- 弹窗组件
  - AICBaseAlertController
  - 与系统UIAlertController的区别
  - 自定义弹窗实现
- 下拉刷新组件
  - AICRefreshNormalHeader
  - AICRefreshAutoNormalFooter
  - MJRefresh封装
- 图片加载
  - YYWebImageManager
  - 异步加载与缓存
  - 使用方法

## 5. 数据存储与序列化
- WCDB数据库
  - 特性与优势
  - 数据模型定义（TableCodable）
  - 基本操作（增删改查）
  - 高级查询
- 序列化工具
  - SwiftyJSON（需指定映射关系及数据类型）
  - YYModel（利用OC特性，Swift类需声明@objcMembers）
  - DataDecodable（继承Swift 4.2 Decodable协议，不支持继承类）
  - 三种方式的对比与选择
- 缓存管理
  - AICCacheManager
  - 内存与磁盘缓存

## 6. 网络组件
- HTTP请求
  - AICHttpManager基础用法
  - AICBaseHttpManager扩展功能
  - 请求参数与响应处理
  - 文件上传
- WebSocket通信
  - WebSocketHelper基础用法
  - TickerSubscribeDataManager行情订阅
  - 实时数据处理
- 网络缓存
  - AICNetworkCache
  - 缓存策略与失效机制

## 7. 基础UI组件
- 基础控制器
  - AICBaseViewController
  - AICBaseTableViewController
  - AICBaseNavigationController
  - 生命周期管理
- 基础视图
  - AICBaseTableView
  - AICBaseCollectionView
  - AICBaseLabel
  - AICBaseButton
  - AICBaseTextView
- 导航组件
  - AICBaseNavigationBar
  - 导航栏配置与样式

## 8. 工具类
- 字符串处理
  - String+Extension
  - 本地化扩展
  - 格式化与验证
- 日期处理
  - 日期格式化
  - 时间戳转换
- 数字格式化
  - 货币格式
  - 百分比格式
- 图片处理
  - UIImage+AICTools
  - 图片缓存与优化
- 视图扩展
  - UIView+AICTools
  - 常用UI操作封装
- 线程管理
  - AICSwiftTool
  - 主线程/后台线程切换
  - 延迟执行
- 常量定义
  - AICGlobalConstant
  - 全局配置与常量

## 9. 数据绑定
- ReactiveObjC
  - 响应式编程基础
  - 信号创建与订阅
  - 常用操作符
  - 实际应用场景
- ObservableValue
  - 属性观察者模式
  - @Observable属性包装器
  - 订阅与销毁
  - 与ReactiveObjC的对比

## 10. 最佳实践
- 组件选择指南
- 性能优化建议
- 常见问题与解决方案
- 代码风格与规范
