# AICoin-iOS 多语言支持系统文档

## 目录

1. [多语言系统概述](#1-多语言系统概述)
2. [SystemConfig 语言管理](#2-systemconfig-语言管理)
3. [本地化文件结构](#3-本地化文件结构)
4. [使用方法](#4-使用方法)
## 1. 多语言系统概述

AICoin-iOS应用支持简体中文、繁体中文和英文三种语言，为全球用户提供本地化的使用体验。多语言系统具有以下特点：

### 1.1 支持的语言

- **简体中文**：面向中国大陆用户
- **繁体中文**：面向港澳台及海外华人用户
- **英文**：面向国际用户

### 1.2 语言管理方式

- **集中管理**：通过`InternalToolKit`中的`SystemConfig`类统一管理语言状态
- **全局切换**：切换语言时会替换应用的`rootViewController`为新的`AICTabbarController`实例
- **持久化存储**：语言设置保存在`UserDefaults`中，应用重启后仍然保持

### 1.3 本地化资源

- **字符串资源**：各模块在对应的`.strings`文件中配置多语言文本
- **模块化管理**：每个模块管理自己的本地化资源
- **扩展方法**：通过String扩展提供便捷的本地化方法

## 2. SystemConfig 语言管理

`SystemConfig`是AICoin应用的语言管理核心类，位于`InternalToolKit`框架中，负责语言状态的存储、获取和切换。

### 2.1 核心功能

- 存储和获取当前语言设置
- 提供语言切换方法
- 判断当前界面语言类型
- 管理语言资源

### 2.2 主要API

```swift
// 获取当前语言类型
let language = SystemConfig.userInterfaceLanguage()

// 判断是否为中文界面（简体或繁体）
let isChinese = SystemConfig.isChineseUserInterface()

```

## 3. 本地化文件结构

AICoin应用的本地化资源主要通过`.strings`文件进行管理，按照语言和模块进行组织。

### 3.1 项目结构

```
AICoin/
├── Resources/
│   ├── en.lproj/                # 英文资源
│   │   ├── Localizable.strings  # 通用字符串
│   │   ├── InfoPlist.strings    # Info.plist本地化
│   │   └── ModuleLocalizable.strings  # 模块特定字符串
│   ├── zh-Hans.lproj/           # 简体中文资源
│   │   ├── Localizable.strings
│   │   ├── InfoPlist.strings
│   │   └── ModuleLocalizable.strings
│   └── zh-Hant.lproj/           # 繁体中文资源
│       ├── Localizable.strings
│       ├── InfoPlist.strings
│       └── ModuleLocalizable.strings
└── Module/
    └── ModuleName/              # 各业务模块
        └── Resources/           # 模块特定资源
            ├── en.lproj/
            ├── zh-Hans.lproj/
            └── zh-Hant.lproj/
```

### 3.2 Localizable.strings 文件

`Localizable.strings`文件是本地化字符串的主要存储位置，采用键值对的形式定义：

```
// 繁体中文 (zh-Hant.lproj/Localizable.strings)
"跟单交易" = "跟單交易";
"全仓" = "全倉";
"逐仓" = "逐倉";
"做多" = "做多";

// 英文 (en.lproj/Localizable.strings)
"跟单交易" = "Copy Trading";
"全仓" = " Cross ";
"逐仓" = " Isolated ";
"做多" = "Long";
```

### 3.3 模块特定本地化文件

对于特定模块的本地化字符串，可以创建模块专用的`.strings`文件：

```
// 行情模块 (Ticker/Resources/zh-Hant.lproj/Ticker-Localizable.strings)
"项目对" = "項目對";
"市值" = "市值";
"抱歉，暂时未找到相关搜索内容！" = "抱歉，暫時未找到相關搜索內容！";
"联系我们" = "聯繫我們";

// 行情模块 (Ticker/Resources/en.lproj/Ticker-Localizable.strings)
"项目对" = "Pairs";
"市值" = "Cap";
"抱歉，暂时未找到相关搜索内容！" = "Sorry, no relevant records!";
"联系我们" = "Contact Us";
```

## 4. 使用方法

### 4.1 获取本地化文本

根据AICoin编码规范，获取本地化文本的方式如下：

```swift
// 基本用法
let localizedString = "文本".模块.localized

// 示例：获取基础模块的本地化文本
let cancelText = "取消".base.localized

// 示例：获取行情模块的本地化文本
let marketListText = "市场列表".ticker.localized

// 在Objective-C中使用
NSString *localizedString = [@"文本" baseLocalizedForOC];
```

### 4.2 格式化本地化文本

对于包含参数的本地化文本，可以使用格式化方法：

```swift
// 定义带参数的本地化字符串
// Localizable.strings
"welcome_user" = "欢迎，%@";
"items_count" = "共有 %d 个项目";

// 使用带参数的本地化字符串
let username = "张三"
let welcomeText = String(format: "welcome_user".base.localized, username)
// 结果: "欢迎，张三"

let count = 5
let itemsText = String(format: "items_count".base.localized, count)
// 结果: "共有 5 个项目"
```

### 4.3 判断当前语言

```swift
// 判断是否为中文界面（简体或繁体）
if SystemConfig.isChineseUserInterface() {
    // 中文界面特定处理
} else {
    // 非中文界面处理
}

// 获取当前语言类型
let currentLanguage = SystemConfig.userInterfaceLanguage()
switch currentLanguage {
case .simplifiedChinese:
    print("当前是简体中文")
case .traditionalChinese:
    print("当前是繁体中文")
case .english:
    print("当前是英文")
}
```