# AICoin-iOS 数据存储与序列化文档

## 目录

1. [WCDB数据库](#1-wcdb数据库)
2. [序列化方案](#2-序列化方案)
3. [缓存管理](#3-缓存管理)
4. [最佳实践](#4-最佳实践)

## 1. WCDB数据库

AICoin-iOS项目使用WCDB.Swift进行数据持久化，它是微信团队开发的高性能、ACID兼容的移动数据库框架，支持一句代码即可将数据取出并组合为对象，支持语言集成查询和多线程高并发。

### 1.1 数据库模型定义

在WCDB中，数据库模型需要实现`TableCodable`协议，并定义表结构映射。

#### 1.1.1 基本模型定义

```swift
import WCDBSwift

// 行情币种数据库模型
final class TickerCurrencyDBModel: AICTickerBaseModel, TableCodable {
    
    // 模型属性
    var key: String = ""         // 币种唯一标识
    var enName: String = ""      // 英文名称
    var cnName: String = ""      // 中文名称
    var logo: String = ""        // 图标URL
    var rank: Int = 0            // 排名
    var coinShow: String = ""    // 显示名称
    
    // 表结构映射
    enum CodingKeys: String, CodingTableKey {
        case key
        case enName = "en_name"
        case cnName = "cn_name"
        case logo
        case rank
        case coinShow = "coin_show"
        
        // 定义Root类型
        typealias Root = TickerCurrencyDBModel
        
        // 对象关系映射
        static let objectRelationalMapping = TableBinding(CodingKeys.self)
        
        // 定义主键和索引
        static var columnConstraintBindings: [CodingKeys: ColumnConstraintBinding]? {
            return [
                key: ColumnConstraintBinding(isPrimary: true)
            ]
        }
    }
}
```

#### 1.1.2 复杂数据类型处理

对于复杂数据类型（如字典、数组），WCDB不能直接存储，需要转换为字符串或其他基本类型：

```swift
// 行情消息数据库模型
final class TickerMessageDBModel: TableCodable {
    var messageID: Int = 0
    var content: String = ""
    var extraDataJSON: String = "{}"  // 存储JSON字符串
    var timestamp: Int = 0
    
    enum CodingKeys: String, CodingTableKey {
        typealias Root = TickerMessageDBModel
        static let objectRelationalMapping = TableBinding(CodingKeys.self)
        
        case messageID
        case content
        case extraDataJSON = "extra_data"
        case timestamp
    }
    
    // 字典数据的计算属性
    var extraData: [String: Any] {
        get {
            if let jsonData = extraDataJSON.data(using: .utf8),
               let dict = try? JSONSerialization.jsonObject(with: jsonData) as? [String: Any] {
                return dict
            }
            return [:]
        }
        set {
            if let jsonData = try? JSONSerialization.data(withJSONObject: newValue),
               let jsonString = String(data: jsonData, encoding: .utf8) {
                extraDataJSON = jsonString
            }
        }
    }
}
```

### 1.2 数据库操作

#### 1.2.1 数据库初始化

```swift
// 行情数据库管理器
class TickerDBManager {
    // 单例
    static let shared = TickerDBManager()
    private init() {
        setupDatabase()
    }
    
    // 数据库和表名
    private let database: Database
    private let currencyTableName = "ticker_currency"
    private let marketTableName = "ticker_market"
    
    // 初始化数据库
    private func setupDatabase() {
        let dbPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first! + "/ticker.db"
        database = Database(withPath: dbPath)
        
        do {
            // 创建币种表
            try database.create(table: currencyTableName, of: TickerCurrencyDBModel.self)
            
            // 创建市场表
            try database.create(table: marketTableName, of: TickerMarketDBModel.self)
            
            // 创建索引
            try database.create(index: ["key"], on: currencyTableName, named: "currency_key_index")
            try database.create(index: ["market_list_key"], on: marketTableName, named: "market_key_index")
        } catch {
            aic_log("创建表失败: \(error)")
        }
    }
}
```

#### 1.2.2 CRUD操作

```swift
// 插入或更新币种数据
func insertOrUpdateCurrency(_ model: TickerCurrencyDBModel) -> Bool {
    do {
        try database.insertOrReplace(objects: model, intoTable: currencyTableName)
        return true
    } catch {
        aic_log("插入币种失败: \(error)")
        return false
    }
}

// 查询币种数据
func getCurrency(key: String) -> TickerCurrencyDBModel? {
    do {
        return try database.getObject(fromTable: currencyTableName, 
                                     where: TickerCurrencyDBModel.Properties.key == key)
    } catch {
        aic_log("查询币种失败: \(error)")
        return nil
    }
}

// 查询所有币种
func getAllCurrencies() -> [TickerCurrencyDBModel] {
    do {
        return try database.getObjects(fromTable: currencyTableName)
    } catch {
        aic_log("查询所有币种失败: \(error)")
        return []
    }
}

// 删除币种
func deleteCurrency(key: String) -> Bool {
    do {
        try database.delete(fromTable: currencyTableName, 
                           where: TickerCurrencyDBModel.Properties.key == key)
        return true
    } catch {
        aic_log("删除币种失败: \(error)")
        return false
    }
}
```

#### 1.2.3 高级查询

```swift
// 分页查询市场数据
func getMarkets(page: Int, pageSize: Int) -> [TickerMarketDBModel] {
    do {
        let offset = (page - 1) * pageSize
        return try database.getObjects(
            fromTable: marketTableName,
            orderBy: [TickerMarketDBModel.Properties.rank.asOrder(by: .ascending)],
            limit: pageSize,
            offset: offset
        )
    } catch {
        aic_log("分页查询市场失败: \(error)")
        return []
    }
}

// 条件查询
func searchMarkets(keyword: String) -> [TickerMarketDBModel] {
    do {
        // 使用LIKE进行模糊查询
        return try database.getObjects(
            fromTable: marketTableName,
            where: TickerMarketDBModel.Properties.coinShow.like("%\(keyword)%") 
                || TickerMarketDBModel.Properties.platformShow.like("%\(keyword)%")
        )
    } catch {
        aic_log("搜索市场失败: \(error)")
        return []
    }
}

// 复杂条件查询
func getTopMarkets(platform: String, limit: Int) -> [TickerMarketDBModel] {
    do {
        return try database.getObjects(
            fromTable: marketTableName,
            where: TickerMarketDBModel.Properties.platform == platform,
            orderBy: [TickerMarketDBModel.Properties.volume24h.asOrder(by: .descending)],
            limit: limit
        )
    } catch {
        aic_log("查询热门市场失败: \(error)")
        return []
    }
}
```

#### 1.2.4 事务处理

```swift
// 批量插入市场数据
func batchInsertMarkets(_ markets: [TickerMarketDBModel]) -> Bool {
    do {
        // 使用事务处理批量操作
        try database.run(transaction: {
            for market in markets {
                try database.insertOrReplace(objects: market, intoTable: marketTableName)
            }
        })
        return true
    } catch {
        aic_log("批量插入市场失败: \(error)")
        return false
    }
}

// 清空并重建表
func rebuildMarketTable(with markets: [TickerMarketDBModel]) -> Bool {
    do {
        try database.run(transaction: {
            // 删除所有数据
            try database.delete(fromTable: marketTableName)
            
            // 插入新数据
            for market in markets {
                try database.insert(objects: market, intoTable: marketTableName)
            }
        })
        return true
    } catch {
        aic_log("重建市场表失败: \(error)")
        return false
    }
}
```

### 1.3 Ticker模块示例

以下是行情模块中使用WCDB的实际示例：

```swift
// 行情模块视图控制器
class TickerMarketListViewController: AICBaseViewController {
    
    private let viewModel = TickerMarketListViewModel()
    private lazy var tableView = UITableView()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadData()
    }
    
    private func loadData() {
        // 先从数据库加载缓存数据
        viewModel.loadCachedMarkets { [weak self] success in
            if success {
                self?.tableView.reloadData()
            }
            
            // 然后请求网络数据
            self?.viewModel.refreshMarketList { [weak self] success in
                self?.tableView.mj_header?.endRefreshing()
                if success {
                    self?.tableView.reloadData()
                }
            }
        }
    }
}

// 行情列表视图模型
class TickerMarketListViewModel {
    
    private(set) var markets: [TickerMarketListModel] = []
    
    // 加载缓存数据
    func loadCachedMarkets(completion: @escaping (Bool) -> Void) {
        AICSwiftTool.runonGlobalQueueAsync {
            // 从数据库加载数据
            let dbMarkets = TickerDBManager.shared.getAllMarkets()
            
            // 转换为视图模型
            let marketModels = dbMarkets.map { dbModel -> TickerMarketListModel in
                let model = TickerMarketListModel()
                model.marketListKey = dbModel.marketListKey
                model.coinShow = dbModel.coinShow
                model.platformShow = dbModel.platformShow
                model.logo = dbModel.logo
                // 设置其他属性...
                return model
            }
            
            AICSwiftTool.runonMainQueue {
                self.markets = marketModels
                completion(!marketModels.isEmpty)
            }
        }
    }
    
    // 刷新市场列表
    func refreshMarketList(completion: @escaping (Bool) -> Void) {
        AICTickerRequestOperation.shared.requestMarketList { [weak self] (models, error) in
            guard let self = self, let models = models else {
                completion(false)
                return
            }
            
            self.markets = models
            
            // 将数据保存到数据库
            AICSwiftTool.runonGlobalQueueAsync {
                // 转换为数据库模型
                let dbModels = models.map { model -> TickerMarketDBModel in
                    let dbModel = TickerMarketDBModel()
                    dbModel.marketListKey = model.marketListKey
                    dbModel.coinShow = model.coinShow
                    dbModel.platformShow = model.platformShow
                    dbModel.logo = model.logo
                    // 设置其他属性...
                    return dbModel
                }
                
                // 批量保存到数据库
                _ = TickerDBManager.shared.batchInsertMarkets(dbModels)
            }
            
            completion(true)
        }
    }
}
```

## 2. 序列化方案

AICoin-iOS项目中使用了三种不同的序列化方案，各有优缺点。

### 2.1 SwiftyJSON

SwiftyJSON是一个轻量级的JSON解析库，需要指定映射关系及数据类型，实现比较啰嗦。

#### 2.1.1 基本用法

```swift
// 解析JSON
func parseTickerData(_ data: Any) -> TickerModel? {
    let json = JSON(data)
    
    // 检查数据有效性
    guard json["success"].boolValue else {
        let errorCode = json["errorCode"].intValue
        let errorMessage = json["error"].stringValue
        aic_log("API错误: \(errorCode) - \(errorMessage)")
        return nil
    }
    
    // 解析数据
    let tickerData = json["data"]
    
    let ticker = TickerModel()
    ticker.tickerId = tickerData["id"].stringValue
    ticker.name = tickerData["name"].stringValue
    ticker.symbol = tickerData["symbol"].stringValue
    ticker.price = tickerData["price"].doubleValue
    ticker.change24h = tickerData["change_24h"].doubleValue
    ticker.volume24h = tickerData["volume_24h"].doubleValue
    
    // 解析嵌套数据
    if let marketData = tickerData["market"].dictionary {
        ticker.marketName = marketData["name"]?.stringValue ?? ""
        ticker.marketLogo = marketData["logo"]?.stringValue ?? ""
    }
    
    // 解析数组
    ticker.priceHistory = tickerData["price_history"].arrayValue.map { $0.doubleValue }
    
    return ticker
}
```

#### 2.1.2 Ticker模块示例

```swift
// 行情请求操作
class AICTickerRequestOperation {
    
    static let shared = AICTickerRequestOperation()
    
    func requestMarketList(completion: @escaping ([TickerMarketListModel]?, Error?) -> Void) {
        AICHttpManager.shared.post("/api/v1/ticker/market/list", parameters: nil, progress: nil, success: { task, response in
            let json = JSON(response ?? "")
            
            if json["success"].boolValue {
                let dataArray = json["data"].arrayValue
                var models: [TickerMarketListModel] = []
                
                for itemJson in dataArray {
                    let model = TickerMarketListModel()
                    model.marketListKey = itemJson["market_list_key"].stringValue
                    model.coinShow = itemJson["coin_show"].stringValue
                    model.platformShow = itemJson["platform_show"].stringValue
                    model.logo = itemJson["logo"].stringValue
                    model.lastPrice = itemJson["last_price"].doubleValue
                    model.degree24h = itemJson["degree_24h"].doubleValue
                    
                    models.append(model)
                }
                
                completion(models, nil)
            } else {
                let errorCode = json["errorCode"].intValue
                let errorMessage = json["error"].stringValue
                let error = NSError(domain: "APIError", code: errorCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])
                completion(nil, error)
            }
        }, failure: { task, error in
            completion(nil, error)
        })
    }
}
```

### 2.2 YYModel

YYModel利用了OC的特性进行自动映射，Swift类使用需声明@objcMembers。

#### 2.2.1 基本用法

```swift
// 定义模型
@objcMembers
class TickerDetailModel: NSObject {
    var tickerId: String = ""
    var name: String = ""
    var symbol: String = ""
    var price: NSNumber = 0
    var change24h: NSNumber = 0
    var volume24h: NSNumber = 0
    var marketCap: NSNumber = 0
    var marketInfo: MarketInfo?
    var priceHistory: [NSNumber] = []
    
    // 自定义映射关系
    class func modelCustomPropertyMapper() -> [String: String] {
        return [
            "tickerId": "id",
            "change24h": "change_24h",
            "volume24h": "volume_24h",
            "marketCap": "market_cap",
            "marketInfo": "market",
            "priceHistory": "price_history"
        ]
    }
}

@objcMembers
class MarketInfo: NSObject {
    var name: String = ""
    var logo: String = ""
}

// 使用YYModel解析
func parseTickerDetail(_ data: [String: Any]) -> TickerDetailModel? {
    return TickerDetailModel.model(withJSON: data)
}
```

#### 2.2.2 Ticker模块示例

```swift
// 行情详情请求
func requestTickerDetail(tickerId: String, completion: @escaping (TickerDetailModel?, Error?) -> Void) {
    let params = ["ticker_id": tickerId]
    
    AICHttpManager.shared.post("/api/v1/ticker/detail", parameters: params, progress: nil, success: { task, response in
        if let responseDict = response as? [String: Any],
           let success = responseDict["success"] as? Bool, success,
           let data = responseDict["data"] as? [String: Any] {
            
            // 使用YYModel解析
            let model = TickerDetailModel.model(withJSON: data)
            completion(model, nil)
        } else {
            let error = NSError(domain: "APIError", code: -1, userInfo: [NSLocalizedDescriptionKey: "数据解析失败"])
            completion(nil, error)
        }
    }, failure: { task, error in
        completion(nil, error)
    })
}
```

### 2.3 DataDecodable

DataDecodable继承Swift 4.2 Decodable协议，完善类型转换，Swift类方便调用，但不支持继承的类。

#### 2.3.1 基本用法

```swift
// 定义协议
protocol DataDecodable: Decodable {
    init(from data: Any) throws
}

// 默认实现
extension DataDecodable {
    init(from data: Any) throws {
        let jsonData: Data
        
        if let dictionary = data as? [String: Any] {
            jsonData = try JSONSerialization.data(withJSONObject: dictionary)
        } else if let array = data as? [Any] {
            jsonData = try JSONSerialization.data(withJSONObject: array)
        } else if let string = data as? String, let stringData = string.data(using: .utf8) {
            jsonData = stringData
        } else {
            throw DecodingError.dataCorrupted(DecodingError.Context(
                codingPath: [],
                debugDescription: "Invalid data type"
            ))
        }
        
        self = try JSONDecoder().decode(Self.self, from: jsonData)
    }
}

// 定义模型
struct TickerAlertModel: DataDecodable {
    let alertId: String
    let tickerId: String
    let price: Double
    let direction: String
    let createdAt: Int
    
    enum CodingKeys: String, CodingKey {
        case alertId = "alert_id"
        case tickerId = "ticker_id"
        case price
        case direction
        case createdAt = "created_at"
    }
}

// 使用DataDecodable解析
func parseAlertData(_ data: Any) -> TickerAlertModel? {
    do {
        return try TickerAlertModel(from: data)
    } catch {
        aic_log("解析预警数据失败: \(error)")
        return nil
    }
}
```

#### 2.3.2 Ticker模块示例

```swift
// 行情预警请求
func requestTickerAlerts(completion: @escaping ([TickerAlertModel]?, Error?) -> Void) {
    AICBaseHttpManager.post("user/alerts", parameters: ["userid": UserManager.share.safeUserID]) { response in
        if response.success, let data = response.data {
            do {
                // 解析数组数据
                if let alertsData = data["alerts"] as? [[String: Any]] {
                    let alerts = try alertsData.map { try TickerAlertModel(from: $0) }
                    completion(alerts, nil)
                } else {
                    throw NSError(domain: "ParseError", code: -1, userInfo: [NSLocalizedDescriptionKey: "无效的预警数据"])
                }
            } catch {
                completion(nil, error)
            }
        } else {
            completion(nil, response.error)
        }
    }
}
```

## 3. 缓存管理

AICoin-iOS项目使用`AICCacheManager`进行缓存管理，支持内存缓存和磁盘缓存。

### 3.1 缓存管理器

`AICCacheManager`是一个单例类，提供了缓存的存取、清理等功能。

```swift
// 获取缓存管理器单例
let cacheManager = AICCacheManager.share

// 缓存对象
cacheManager.setObject(userInfo, forKey: "currentUser")

// 获取缓存对象
let cachedUser = cacheManager.object(forKey: "currentUser") as? UserInfo

// 缓存图片
cacheManager.setImage(image, forKey: "userAvatar")

// 获取缓存图片
let cachedImage = cacheManager.image(forKey: "userAvatar")

// 清除特定缓存
cacheManager.removeObject(forKey: "currentUser")

// 清除所有缓存
cacheManager.removeAllObjects()
```

### 3.2 网络缓存

项目使用`AICNetworkCache`进行网络响应数据的缓存：

```swift
// 缓存网络响应数据
AICNetworkCache.shared.cache(key: cacheKey, value: model, expireSeconds: 60 * 5)  // 缓存5分钟

// 获取缓存数据
if let cachedModel: TickerMarketListModel = AICNetworkCache.shared.object(forKey: cacheKey) {
    // 使用缓存数据
    self.updateUI(with: cachedModel)
}

// 清除缓存
AICNetworkCache.shared.removeObject(forKey: cacheKey)
AICNetworkCache.shared.removeAllObjects()  // 清除所有缓存
```

### 3.3 Ticker模块示例

```swift
// 行情列表缓存示例
class TickerMarketListViewModel {
    
    private let cacheKey = "ticker_market_list"
    private(set) var markets: [TickerMarketListModel] = []
    
    // 加载数据（优先使用缓存）
    func loadData(completion: @escaping (Bool) -> Void) {
        // 尝试从缓存加载
        if let cachedMarkets: [TickerMarketListModel] = AICNetworkCache.shared.object(forKey: cacheKey) {
            self.markets = cachedMarkets
            completion(true)
            
            // 后台刷新最新数据
            refreshDataInBackground()
            return
        }
        
        // 缓存不存在，请求网络数据
        refreshData(completion: completion)
    }
    
    // 刷新数据
    func refreshData(completion: @escaping (Bool) -> Void) {
        AICTickerRequestOperation.shared.requestMarketList { [weak self] (models, error) in
            guard let self = self, let models = models else {
                completion(false)
                return
            }
            
            self.markets = models
            
            // 缓存数据，有效期10分钟
            AICNetworkCache.shared.cache(key: self.cacheKey, value: models, expireSeconds: 10 * 60)
            
            completion(true)
        }
    }
    
    // 后台刷新数据
    private func refreshDataInBackground() {
        AICSwiftTool.runonGlobalQueueAsync {
            self.refreshData { _ in }
        }
    }
}
```

## 4. 最佳实践

### 4.1 WCDB最佳实践

1. **模型设计**
   - 使用适当的数据类型，避免滥用字符串类型
   - 为频繁查询的字段创建索引
   - 使用主键确保数据唯一性

2. **性能优化**
   - 使用事务处理批量操作
   - 避免在主线程执行数据库操作
   - 合理设计表结构，避免过度复杂的查询

3. **错误处理**
   - 始终使用try-catch处理数据库操作
   - 记录数据库错误，便于调试
   - 提供数据恢复机制

### 4.2 序列化最佳实践

1. **选择合适的序列化方案**
   - SwiftyJSON：适用于灵活的JSON解析，但代码较啰嗦
   - YYModel：适用于与OC混编的项目，自动映射省时省力
   - DataDecodable：适用于纯Swift项目，类型安全且简洁

2. **错误处理**
   - 验证JSON数据的有效性
   - 提供默认值处理缺失字段
   - 记录解析错误，便于调试

3. **性能考虑**
   - 避免重复解析相同数据
   - 大型数据集考虑异步解析
   - 缓存解析结果减少重复工作

### 4.3 缓存最佳实践

1. **缓存策略**
   - 设置合理的缓存过期时间
   - 根据数据重要性选择内存缓存或磁盘缓存
   - 定期清理过期缓存

2. **缓存键设计**
   - 使用有意义的缓存键名
   - 避免键名冲突
   - 考虑添加版本信息到键名

3. **缓存监控**
   - 监控缓存大小，避免过度占用内存
   - 在内存警告时清理非必要缓存
   - 提供手动清理缓存的选项
