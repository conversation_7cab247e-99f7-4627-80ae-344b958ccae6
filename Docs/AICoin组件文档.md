# AICoin iOS 自定义组件文档

本文档整理了 AICoin iOS 项目中 `AICoin/Module/Base/Vendor/Custom` 目录下所有自定义封装的组件，包括各组件的功能说明、主要特性和使用方法。

## 组件目录

1. [页面组件](#页面组件)
   - [AICPageView - 多页面视图管理](#aicpageview)
   - [PageController - 页面控制器](#pagecontroller)
2. [UI控件](#ui控件)
   - [AICSegmentedControl - 分段控制器](#aicsegmentedcontrol)
   - [AICSwitch - 自定义开关](#aicswitch)
   - [DotView - 圆点视图](#dotview)
   - [PageControl - 页面指示器](#pagecontrol)
3. [刷新组件](#刷新组件)
   - [Refresh - 下拉刷新](#refresh)
   - [VORefresh - 自定义刷新动画](#vorefresh)
4. [选择器组件](#选择器组件)
   - [PickerView - 选择器视图](#pickerview)
   - [ImagePickerController - 图片选择器](#imagepickercontroller)
5. [提示组件](#提示组件)
   - [TextImageToastView - Toast提示](#textimagetoastview)
   - [MaskGuideView - 遮罩引导视图](#maskguideview)
6. [弹窗组件](#弹窗组件)
   - [CustomPresentation - 自定义弹窗系统](#custompresentation)
7. [主题与多语言](#主题与多语言)
   - [ThemeManager - 主题管理](#thememanager)
   - [NSBundle+Language - 多语言支持](#nsbundle-language)
8. [图片相关](#图片相关)
   - [YYImage - 高性能图片框架](#yyimage)
   - [YYPhotoBrowseView - 图片浏览器](#yyphotobrowseview)
9. [工具组件](#工具组件)
   - [AICRunLoop - RunLoop管理](#aicrunloop)
   - [AICLock - 线程锁](#aiclock)
   - [AICFitPlus - 适配工具](#aicfitplus)
   - [ObservationBase - 观察者模式](#observationbase)
10. [统计与推送](#统计与推送)
    - [EventStatistics - 事件统计](#eventstatistics)
    - [MobScreen - 移动分析](#mobscreen)
    - [JPushTag - 极光推送标签](#jpushtag)
    - [TPNSTag - 腾讯推送标签](#tpnstag)

---

## 页面组件

### AICPageView

**功能描述：** 一个功能完整的多页面滑动视图组件，支持顶部标题栏和内容页面的联动切换。

**主要特性：**
- 支持顶部标题栏（AICTopBarView）
- 支持页面滑动切换（AICPageScrollView）
- 支持标题宽度自适应
- 支持圆形和线条两种指示器样式
- 支持临时视图和真实视图的懒加载

**核心类：**
- `AICPageView` - 主视图类
- `AICTopBarView` - 顶部标题栏
- `AICPageScrollView` - 内容滚动视图
- `AICTopBarItem` - 标题项

**使用示例：**
```swift
// 创建PageView
let pageView = AICPageView(frame: view.bounds)
pageView.pageViewDelegate = self
pageView.pageViewDataSource = self
pageView.pageViewUIDelegate = self

// 设置起始页面
pageView.startAtPage = 0

// 设置样式
pageView.changeToLineStyle() // 线条样式
// pageView.changeToCircleStyle() // 圆形样式

// 初始化
pageView.setup()
```

**协议实现：**
```swift
// AICPageViewDelegate
func numberOfTitlesInAICPageView(_ pageView: AICPageView) -> Int {
    return titles.count
}

// AICPageViewDataSource
func aicPageView(_ pageView: AICPageView, titleForIndex index: Int) -> [String] {
    return [titles[index]]
}

func realviewForPageView(_ pageView: AICPageView, atIndex index: Int) -> UIView {
    // 返回真实的内容视图
    return contentViews[index]
}
```

### PageController

**功能描述：** 页面控制器组件，提供页面切换的基础功能。

---

## UI控件

### AICSegmentedControl

**功能描述：** 自定义的分段控制器，支持样式定制和主题适配。

**主要特性：**
- 支持自定义选中/未选中颜色
- 支持自定义字体
- 支持边框和圆角设置
- 支持分隔线显示
- 支持动态更新标题

**使用示例：**
```swift
// 创建分段控制器
let items = ["选项1", "选项2", "选项3"]
let segmentedControl = AICSegmentedControl(frame: CGRect(x: 0, y: 0, width: 200, height: 40), items: items)

// 设置代理
segmentedControl.delegate = self

// 自定义颜色
segmentedControl.setColors(
    selectedColor: UIColor.blue,
    unSelectedColor: UIColor.clear,
    selectedTextColor: UIColor.white,
    unSelectedTextColor: UIColor.gray
)

// 设置边框
segmentedControl.setBorder(width: 1.0, cornerRadius: 5.0)

// 设置字体
segmentedControl.defaultFont = UIFont.systemFont(ofSize: 14)
segmentedControl.highlightFont = UIFont.boldSystemFont(ofSize: 14)

// 更新标题
segmentedControl.updateTitle("新标题", index: 0)
```

**代理方法：**
```swift
func segmentedControl(segmentedControl: AICSegmentedControl, didSelectedAt index: Int) {
    print("选择了第 \(index) 项")
}
```

### AICSwitch

**功能描述：** 基于 SevenSwitch 的自定义开关控件，支持主题适配。

**主要特性：**
- 自动适配主题颜色
- 支持阴影效果
- 固定尺寸设计（37x13）
- 扩大点击区域

**使用示例：**
```swift
let aicSwitch = AICSwitch()
aicSwitch.addTarget(self, action: #selector(switchValueChanged(_:)), for: .valueChanged)

// 设置开关状态
aicSwitch.on = true

// 获取开关状态
let isOn = aicSwitch.isOn

// 手动更新颜色（主题切换时）
aicSwitch.updateColor()
```

### DotView

**功能描述：** 圆点视图组件，常用于未读消息提示。

### PageControl

**功能描述：** 页面指示器组件，用于显示当前页面位置。

---

## 刷新组件

### Refresh

**功能描述：** 基于 MJRefresh 的封装，提供统一的下拉刷新和上拉加载样式。

**主要类：**
- `AICRefreshNormalHeader` - 下拉刷新头部
- `AICRefreshAutoNormalFooter` - 上拉加载底部

**使用示例：**
```swift
// 添加下拉刷新
tableView.aic_header = AICRefreshNormalHeader(refreshingBlock: { [weak self] in
    self?.loadData()
})

// 添加上拉加载
tableView.aic_footer = AICRefreshAutoNormalFooter(refreshingBlock: { [weak self] in
    self?.loadMoreData()
})

// 结束刷新
tableView.aic_header?.endRefreshing()
tableView.aic_footer?.endRefreshing()
```

### VORefresh

**功能描述：** 自定义的刷新控件，提供独特的环形进度动画效果。

**主要特性：**
- 环形进度条动画
- 支持自定义颜色和大小
- 平滑的过渡动画

**使用示例：**
```swift
// 添加VORefresh
scrollView.vo_addRefreshView { [weak self] in
    // 执行刷新操作
    self?.refreshData()
}

// 开始刷新
scrollView.vo_beginRefreshing()

// 结束刷新
scrollView.vo_endRefreshing()
```

---

## 选择器组件

### PickerView

**功能描述：** 通用的选择器视图组件，支持从底部弹出。

**主要类：**
- `BasePickerView` - 基础选择器视图
- `BasePickerPackageView` - 选择器包装视图

**使用示例：**
```swift
let pickerView = BasePickerView()
pickerView.dataSource = ["选项1", "选项2", "选项3"]
pickerView.selectedHandler = { index, value in
    print("选择了: \(value)")
}
pickerView.show()
```

### ImagePickerController

**功能描述：** 图片选择器控制器，继承自系统的 UIImagePickerController。

**主要特性：**
- 统一的样式设置
- 支持相机和相册
- 自动处理权限请求

**使用示例：**
```swift
let imagePicker = AICImagePickerController()
imagePicker.delegate = self
imagePicker.sourceType = .photoLibrary
present(imagePicker, animated: true)
```

---

## 提示组件

### TextImageToastView

**功能描述：** 图文混合的 Toast 提示视图，支持图片+文字的提示展示。

**主要特性：**
- 支持图片和文字组合
- 自动适配主题
- 固定尺寸（90x90）
- 圆角和阴影效果

**使用示例：**
```swift
// 创建Toast
let toast = TextImageToastView(
    message: "操作成功", 
    image: UIImage(named: "success_icon")
)

// 显示Toast
toast.center = view.center
view.addSubview(toast)

// 自动隐藏
DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
    toast.removeFromSuperview()
}
```

### MaskGuideView

**功能描述：** 遮罩引导视图，用于新手引导和功能介绍。

**主要特性：**
- 支持高亮区域
- 支持多步骤引导
- 支持自定义提示内容

---

## 弹窗组件

### CustomPresentation

**功能描述：** 完整的自定义弹窗系统，支持 Alert 和 ActionSheet 两种样式。

详细使用请参考 [AICoin组件速查表 - 弹窗组件](./AICoin组件速查表.md#弹窗组件)

---

## 图片相关

### YYImage

**功能描述：** 高性能的图片处理框架，支持 WebP、APNG、GIF 等格式。

### YYPhotoBrowseView

**功能描述：** 功能完整的图片浏览器组件。

**主要特性：**
- 支持手势缩放
- 支持图片预加载
- 支持保存图片
- 支持转场动画

**使用示例：**
```swift
let browser = YYPhotoBrowseView()
browser.imageURLs = imageURLs
browser.currentIndex = selectedIndex
browser.show()
```

---

## 工具组件

### AICRunLoop

**功能描述：** RunLoop 管理工具，用于优化性能和处理定时任务。

### AICLock

**功能描述：** 基于信号量的线程锁实现。

**使用示例：**
```swift
let lock = AICLock()

// 加锁
lock.lock()

// 执行临界区代码
// ...

// 解锁
lock.unlock()
```

### AICFitPlus

**功能描述：** 屏幕适配工具，处理不同设备的布局适配。

### ObservationBase

**功能描述：** 观察者模式基础实现，用于事件监听和通知。

---

## 统计与推送

### EventStatistics

**功能描述：** 事件统计组件，用于用户行为分析。

### MobScreen

**功能描述：** 移动端屏幕分析工具。

### JPushTag

**功能描述：** 极光推送标签管理组件。

### TPNSTag

**功能描述：** 腾讯移动推送标签管理组件。

---

## 最佳实践

1. **主题适配**：所有 UI 组件都应该支持主题切换，使用 `DynamicHelper` 或 `UIColor.baseTheme` 获取颜色。

2. **性能优化**：
   - 使用 `AICPageView` 时启用懒加载
   - 图片浏览使用 `YYPhotoBrowseView` 以获得更好的性能
   - 大量数据刷新时使用 `VORefresh` 的环形动画更流畅

3. **用户体验**：
   - Toast 提示使用 `TextImageToastView`，保持统一的视觉风格
   - 新功能引导使用 `MaskGuideView`
   - 弹窗使用 `CustomPresentation` 系统，保持交互一致性

4. **代码规范**：
   - 继承相应的基类以获得统一的行为
   - 实现必要的协议方法
   - 注意内存管理，使用 weak 引用避免循环引用

## 更新日志

- 2024-01 - 初始版本，整理现有组件
- 待更新 - 补充更多使用示例和最佳实践 