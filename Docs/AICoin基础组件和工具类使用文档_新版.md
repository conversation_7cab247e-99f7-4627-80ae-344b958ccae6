# AICoin-iOS 基础组件和工具类使用文档

## 目录

1. [概述](#1-概述)
   - [1.1 项目架构简介](#11-项目架构简介)
   - [1.2 文档目的和使用方法](#12-文档目的和使用方法)
   - [1.3 基础组件和工具类的分类](#13-基础组件和工具类的分类)
2. [主题管理](#2-主题管理)
3. [多语言支持](#3-多语言支持)
4. [布局与UI组件](#4-布局与ui组件)
5. [数据存储与序列化](#5-数据存储与序列化)
6. [网络组件](#6-网络组件)
7. [基础UI组件](#7-基础ui组件)
8. [工具类](#8-工具类)
9. [数据绑定](#9-数据绑定)
10. [最佳实践](#10-最佳实践)

## 1. 概述

### 1.1 项目架构简介

AICoin-iOS是一款专注于数字货币行情、资讯和交易的移动应用，采用模块化架构设计，主要由以下几个核心模块组成：

- **基础模块（Base）**：提供基础UI组件、网络请求、数据存储、工具类等底层支持
- **首页模块（HomePage）**：展示市场热门币种和概览，提供快速入口和推荐内容
- **行情模块（Ticker）**：提供各种数字货币的实时行情、K线图表和市场数据
- **自选模块**：用户自定义关注的数字货币列表和分组管理
- **资讯内容模块（Content）**：提供新闻、快讯、动态等内容服务
- **个人中心模块（Me）**：用户账户管理、设置和个性化功能
- **K线图表模块（CandleChart）**：专业的K线图表展示和分析工具
- **登录模块（Login）**：用户认证和账户管理
- **交易模块（Trade）**：数字货币交易功能

项目采用混合开发模式，主要使用Swift和Objective-C两种语言，并通过桥接文件（AICoin-Swift.h）实现两种语言的互操作。项目整体遵循MVC架构模式，同时在部分模块中引入MVVM设计模式提高代码可维护性。

#### 1.1.1 目录结构

项目主要目录结构如下：

```
AICoin/
├── Module/                 # 业务模块
│   ├── Base/               # 基础组件
│   ├── HomePage/           # 首页模块
│   ├── Ticker/             # 行情模块
│   ├── 自选/               # 自选模块
│   ├── Content/            # 资讯内容模块
│   ├── Me/                 # 个人中心模块
│   ├── CandleChart/        # K线图表模块
│   ├── Login/              # 登录模块
│   └── trade/              # 交易模块
├── Resources/              # 资源文件
├── Supporting Files/       # 配置文件
└── Vendor/                 # 第三方依赖
```

#### 1.1.2 技术栈

项目使用的主要技术栈包括：

- **UI框架**：UIKit
- **网络请求**：AFNetworking/AICHttpManager
- **WebSocket**：SocketRocket
- **数据库**：WCDBSwift
- **布局**：SnapKit、Masonry、Bees
- **图片加载**：YYWebImageManager
- **序列化**：SwiftyJSON、YYModel、DataDecodable
- **数据绑定**：ReactiveObjC、ObservableValue
- **下拉刷新**：MJRefresh/AICRefresh
- **空视图**：DZNEmptyDataSet

### 1.2 文档目的和使用方法

本文档旨在帮助开发人员快速了解和使用AICoin-iOS项目中的基础组件和工具类，提高开发效率，保持代码风格一致性，减少重复开发工作。通过本文档，开发人员可以：

- 了解项目中可用的基础组件和工具类
- 学习各组件的使用方法和最佳实践
- 避免重复造轮子，提高开发效率
- 保持代码风格一致，提高代码质量

#### 1.2.1 使用方法

1. **快速查找**：文档按功能模块分类，开发者可根据需求快速定位到相应章节
2. **代码示例**：每个组件和工具类都提供了实际使用示例，可直接复制使用
3. **最佳实践**：遵循文档中推荐的最佳实践，确保代码质量和性能
4. **版本兼容**：注意查看组件的版本兼容性说明，确保在不同iOS版本中正常工作

#### 1.2.2 适用人群

- **新加入项目的iOS开发人员**：快速了解项目架构和可用组件
- **需要使用特定组件的现有团队成员**：查找特定组件的使用方法
- **进行代码审查的技术负责人**：确保代码符合项目规范和最佳实践

### 1.3 基础组件和工具类的分类

AICoin-iOS项目中的基础组件和工具类可分为以下几大类：

#### 1.3.1 主题管理

App主题分为白天、黑夜两种（K线独自处理，可不跟随App设置），由SSBThemeManager管理这个状态。目前方案是用过全局通知去刷新整个App，有些子页面会重新加载数据。主题包括了颜色和图片，由每个模块各自管理。

- **主题管理器**：SSBThemeManager
- **颜色管理**：BaseThemeProtocol及其实现类
- **图片资源**：模块下的xcassets对应的白天、黑夜文件夹配置
- **使用方法**：
  ```swift
  // 获取图片
  image = UIImage.模块.image(name: name)
  // 获取颜色
  color = UIColor.模块.current.color
  ```

#### 1.3.2 多语言支持

App语言分简中，繁中和英文，由InternalToolKit中的SystemConfig管理状态，切换时会替换window的rootViewController成新的AICTabbarController。

- **语言管理**：SystemConfig
- **本地化文件**：模块对应的strings文件
- **使用方法**：
  ```swift
  // 获取本地化文本
  let string: String = "文本".模块.localized
  ```

#### 1.3.3 布局与UI组件

为方便维护，不再使用storyboard或xib去写UI。布局使用自动布局适配不同设备，目前App用到自动布局库有Masonry、Bees、SnapKit。

- **布局工具**：
  - SnapKit（推荐使用）
  - Masonry（仅用于OC旧UI）
  - Bees（简洁但可能不再维护）
- **空视图**：DZNEmptyDataSet，默认AICTableView的空视图是BaseEmptyDelegateModel
- **弹窗**：AICBaseAlertController（不同于系统UIAlertController，不会创建新window）
- **下拉组件**：AICRefreshNormalHeader、AICRefreshAutoNormalFooter
- **图片加载**：YYWebImageManager

#### 1.3.4 数据存储与序列化

- **数据库**：WCDBSwift，支持一句代码即可将数据取出并组合为object，语言集成查询和多线程高并发
- **序列化**：
  - SwiftyJSON：需指定映射关系及数据类型，实现比较啰嗦
  - YYModel：利用OC的特性，swift类使用需声明@objcMembers
  - DataDecodable：继承swift4.2 decodable协议，完善类型转换，swift类方便调用，但不支持继承的类
- **缓存管理**：AICCacheManager

#### 1.3.5 网络组件

- **HTTP请求**：AICHttpManager、AICBaseHttpManager
- **WebSocket通信**：WebSocketHelper、TickerSubscribeDataManager
- **网络缓存**：AICNetworkCache

#### 1.3.6 基础UI组件

- **基础控制器**：AICBaseViewController、AICBaseTableViewController等
- **基础视图**：AICBaseTableView、AICBaseLabel等
- **导航组件**：AICBaseNavigationBar、AICBaseNavigationController

#### 1.3.7 工具类

- **字符串处理**：String+Extension
- **日期处理**
- **数字格式化**
- **图片处理**：UIImage+AICTools
- **视图扩展**：UIView+AICTools
- **线程管理**：AICSwiftTool
- **常量定义**：AICGlobalConstant

#### 1.3.8 数据绑定

- **ReactiveObjC**：响应式编程三方框架，RAC用信号（RACSignal）来代替和处理各种变量的变化和传递
- **ObservableValue**：通过didSet的属性观察方法通知所有观察者数据变化，Observable是对此封装的属性包装器

通过合理使用这些基础组件和工具类，可以显著提高开发效率，保持代码风格一致，并确保应用的稳定性和可维护性。
