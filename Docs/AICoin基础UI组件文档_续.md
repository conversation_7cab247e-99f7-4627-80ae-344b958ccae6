# AICoin-iOS 基础UI组件文档（续）

## 3. 导航组件

AICoin-iOS项目提供了一系列导航相关的组件，用于构建统一的导航体验。

### 3.1 AICBaseNavigationBar

`AICBaseNavigationBar`是自定义导航栏的基类，提供了统一的样式和行为，可以替代系统导航栏使用。

#### 3.1.1 基本用法

```swift
// 创建自定义导航栏
let navigationBar = AICBaseNavigationBar()
navigationBar.title = "行情详情".ticker.localized
navigationBar.leftButtonImage = UIImage.base.image(name: "nav_back")
navigationBar.rightButtonTitle = "更多".base.localized

// 设置点击事件
navigationBar.leftButtonAction = { [weak self] in
    self?.navigationController?.popViewController(animated: true)
}

navigationBar.rightButtonAction = { [weak self] in
    self?.showMoreOptions()
}

// 添加到视图
view.addSubview(navigationBar)
navigationBar.snp.makeConstraints { make in
    make.top.equalTo(view.snp_safeTop)
    make.left.right.equalToSuperview()
    make.height.equalTo(44)
}

// 内容视图需要下移
contentView.snp.makeConstraints { make in
    make.top.equalTo(navigationBar.snp.bottom)
    make.left.right.bottom.equalToSuperview()
}
```

#### 3.1.2 自定义样式

`AICBaseNavigationBar`支持自定义样式：

```swift
// 设置标题样式
navigationBar.titleFont = UIFont.systemFont(ofSize: 18, weight: .medium)
navigationBar.titleColor = UIColor.base.current.navigationTitleColor

// 设置背景色
navigationBar.backgroundColor = UIColor.base.current.navigationBarColor

// 设置按钮样式
navigationBar.leftButtonTintColor = UIColor.base.current.navigationTintColor
navigationBar.rightButtonTintColor = UIColor.base.current.navigationTintColor

// 设置分割线
navigationBar.showBottomLine = true
navigationBar.bottomLineColor = UIColor.base.current.separatorColor
```

#### 3.1.3 自定义视图

`AICBaseNavigationBar`支持添加自定义视图：

```swift
// 创建自定义标题视图
let titleView = UIView(frame: CGRect(x: 0, y: 0, width: 200, height: 44))

let titleLabel = UILabel()
titleLabel.text = "BTC/USDT"
titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .bold)
titleLabel.textColor = UIColor.base.current.textColor
titleLabel.textAlignment = .center

let subtitleLabel = UILabel()
subtitleLabel.text = "Binance"
subtitleLabel.font = UIFont.systemFont(ofSize: 12)
subtitleLabel.textColor = UIColor.base.current.subtitleColor
subtitleLabel.textAlignment = .center

titleView.addSubview(titleLabel)
titleView.addSubview(subtitleLabel)

titleLabel.snp.makeConstraints { make in
    make.top.equalToSuperview().offset(4)
    make.centerX.equalToSuperview()
    make.height.equalTo(20)
}

subtitleLabel.snp.makeConstraints { make in
    make.top.equalTo(titleLabel.snp.bottom)
    make.centerX.equalToSuperview()
    make.height.equalTo(16)
}

// 设置自定义标题视图
navigationBar.customTitleView = titleView
```

### 3.2 AICTabBar

`AICTabBar`是自定义标签栏的基类，提供了统一的样式和行为。

#### 3.2.1 基本用法

```swift
// 创建自定义标签栏
let tabBar = AICTabBar()

// 配置标签项
let homeItem = AICTabBarItem(title: "首页".base.localized, 
                            image: UIImage.base.image(name: "tab_home_normal"), 
                            selectedImage: UIImage.base.image(name: "tab_home_selected"))

let marketItem = AICTabBarItem(title: "行情".ticker.localized, 
                              image: UIImage.ticker.image(name: "tab_market_normal"), 
                              selectedImage: UIImage.ticker.image(name: "tab_market_selected"))

let meItem = AICTabBarItem(title: "我的".base.localized, 
                          image: UIImage.base.image(name: "tab_me_normal"), 
                          selectedImage: UIImage.base.image(name: "tab_me_selected"))

tabBar.items = [homeItem, marketItem, meItem]
tabBar.selectedIndex = 0

// 设置点击事件
tabBar.itemTapAction = { [weak self] index in
    self?.switchToViewController(at: index)
}

// 添加到视图
view.addSubview(tabBar)
tabBar.snp.makeConstraints { make in
    make.left.right.equalToSuperview()
    make.bottom.equalTo(view.snp_safeBottom)
    make.height.equalTo(49)
}
```

#### 3.2.2 自定义样式

`AICTabBar`支持自定义样式：

```swift
// 设置背景色
tabBar.backgroundColor = UIColor.base.current.tabBarBackgroundColor

// 设置文字颜色
tabBar.normalTitleColor = UIColor.base.current.tabBarNormalColor
tabBar.selectedTitleColor = UIColor.base.current.tabBarSelectedColor

// 设置文字字体
tabBar.titleFont = UIFont.systemFont(ofSize: 10)

// 设置分割线
tabBar.showTopLine = true
tabBar.topLineColor = UIColor.base.current.separatorColor
```

### 3.3 AICTabBarController

`AICTabBarController`是自定义标签栏控制器，继承自`UITabBarController`，提供了统一的样式和行为。

#### 3.3.1 基本用法

```swift
// 创建标签栏控制器
let tabBarController = AICTabBarController()

// 创建子视图控制器
let homeVC = HomeViewController()
homeVC.tabBarItem = UITabBarItem(title: "首页".base.localized, 
                                image: UIImage.base.image(name: "tab_home_normal"), 
                                selectedImage: UIImage.base.image(name: "tab_home_selected"))

let marketVC = TickerViewController()
marketVC.tabBarItem = UITabBarItem(title: "行情".ticker.localized, 
                                  image: UIImage.ticker.image(name: "tab_market_normal"), 
                                  selectedImage: UIImage.ticker.image(name: "tab_market_selected"))

let meVC = MeViewController()
meVC.tabBarItem = UITabBarItem(title: "我的".base.localized, 
                              image: UIImage.base.image(name: "tab_me_normal"), 
                              selectedImage: UIImage.base.image(name: "tab_me_selected"))

// 创建导航控制器
let homeNav = AICBaseNavigationController(rootViewController: homeVC)
let marketNav = AICBaseNavigationController(rootViewController: marketVC)
let meNav = AICBaseNavigationController(rootViewController: meVC)

// 设置子视图控制器
tabBarController.viewControllers = [homeNav, marketNav, meNav]
tabBarController.selectedIndex = 0

// 设置为根视图控制器
window.rootViewController = tabBarController
```

#### 3.3.2 自定义样式

`AICTabBarController`支持自定义样式：

```swift
// 设置标签栏外观
let tabBar = tabBarController.tabBar
tabBar.tintColor = UIColor.base.current.tabBarSelectedColor
tabBar.unselectedItemTintColor = UIColor.base.current.tabBarNormalColor
tabBar.backgroundColor = UIColor.base.current.tabBarBackgroundColor

// 设置标签栏阴影
tabBar.shadowImage = UIImage()
tabBar.backgroundImage = UIImage()

// 设置标签栏分割线
let lineView = UIView()
lineView.backgroundColor = UIColor.base.current.separatorColor
tabBar.addSubview(lineView)
lineView.snp.makeConstraints { make in
    make.top.left.right.equalToSuperview()
    make.height.equalTo(0.5)
}
```

### 3.4 AICSegmentedControl

`AICSegmentedControl`是自定义分段控制器，提供了统一的样式和行为。

#### 3.4.1 基本用法

```swift
// 创建分段控制器
let segmentedControl = AICSegmentedControl(items: ["日K".ticker.localized, "周K".ticker.localized, "月K".ticker.localized])
segmentedControl.selectedSegmentIndex = 0

// 设置点击事件
segmentedControl.addTarget(self, action: #selector(segmentChanged(_:)), for: .valueChanged)

// 添加到视图
view.addSubview(segmentedControl)
segmentedControl.snp.makeConstraints { make in
    make.top.equalTo(navigationBar.snp.bottom).offset(10)
    make.left.equalToSuperview().offset(15)
    make.right.equalToSuperview().offset(-15)
    make.height.equalTo(36)
}

// 处理点击事件
@objc private func segmentChanged(_ sender: AICSegmentedControl) {
    let index = sender.selectedSegmentIndex
    switch index {
    case 0:
        loadDailyKLine()
    case 1:
        loadWeeklyKLine()
    case 2:
        loadMonthlyKLine()
    default:
        break
    }
}
```

#### 3.4.2 自定义样式

`AICSegmentedControl`支持自定义样式：

```swift
// 设置颜色
segmentedControl.tintColor = UIColor.base.current.segmentedTintColor
segmentedControl.backgroundColor = UIColor.base.current.segmentedBackgroundColor

// 设置选中样式
segmentedControl.setTitleTextAttributes([
    NSAttributedString.Key.foregroundColor: UIColor.base.current.segmentedSelectedTextColor,
    NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .medium)
], for: .selected)

// 设置未选中样式
segmentedControl.setTitleTextAttributes([
    NSAttributedString.Key.foregroundColor: UIColor.base.current.segmentedNormalTextColor,
    NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14)
], for: .normal)

// 设置圆角
segmentedControl.layer.cornerRadius = 5
segmentedControl.clipsToBounds = true
```

## 4. 最佳实践

### 4.1 控制器最佳实践

1. **继承基础类**：所有视图控制器必须继承自`AICBaseViewController`或其子类
2. **生命周期管理**：在适当的生命周期方法中进行初始化和清理
3. **代码组织**：使用MARK注释分隔不同功能的代码
4. **内存管理**：使用weak self避免循环引用
5. **主题适配**：监听主题变化通知并更新UI

```swift
// 推荐的控制器结构
class TickerDetailViewController: AICBaseViewController {
    
    // MARK: - Properties
    private let marketKey: String
    private var marketModel: TickerMarketDetailModel?
    
    // MARK: - UI Components
    private lazy var priceLabel: AICBaseLabel = {
        let label = AICBaseLabel()
        label.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        label.textColor = UIColor.base.current.textColor
        return label
    }()
    
    // MARK: - Initialization
    init(marketKey: String) {
        self.marketKey = marketKey
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupNotifications()
        loadData()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // 视图即将显示时的操作
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        // 取消网络请求
    }
    
    deinit {
        // 移除通知监听
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        title = "行情详情".ticker.localized
        view.backgroundColor = UIColor.base.current.backgroundColor
        
        // 添加子视图
        view.addSubview(priceLabel)
        
        // 设置约束
        priceLabel.snp.makeConstraints { make in
            make.top.equalTo(view.snp_safeTop).offset(20)
            make.centerX.equalToSuperview()
            make.height.equalTo(30)
        }
    }
    
    // MARK: - Notifications
    private func setupNotifications() {
        // 监听主题变化
        NotificationCenter.default.addObserver(self, 
                                              selector: #selector(themeDidChange), 
                                              name: NSNotification.Name(SSBThemeManagerShouldChangeTheme), 
                                              object: nil)
    }
    
    @objc private func themeDidChange() {
        // 更新UI颜色
        view.backgroundColor = UIColor.base.current.backgroundColor
        priceLabel.textColor = UIColor.base.current.textColor
    }
    
    // MARK: - Data
    private func loadData() {
        // 加载数据
        TickerNetworkManager.shared.requestMarketDetail(marketKey: marketKey) { [weak self] (model, error) in
            guard let self = self else { return }
            
            if let model = model {
                self.marketModel = model
                self.updateUI()
            } else {
                // 处理错误
                self.showToast(message: "加载失败，请重试")
            }
        }
    }
    
    // MARK: - UI Update
    private func updateUI() {
        guard let model = marketModel else { return }
        
        // 更新价格标签
        priceLabel.text = model.lastPrice
        
        // 根据涨跌幅设置颜色
        let change = model.degree24h.doubleValue
        priceLabel.textColor = change >= 0 ? UIColor.base.current.upColor : UIColor.base.current.downColor
    }
}
```

### 4.2 视图最佳实践

1. **继承基础类**：使用项目提供的基础视图类
2. **懒加载**：使用懒加载初始化视图组件
3. **主题适配**：支持主题切换
4. **复用视图**：创建可复用的自定义视图
5. **约束布局**：使用SnapKit进行自动布局

```swift
// 自定义可复用视图
class TickerPriceView: UIView {
    
    // MARK: - UI Components
    private lazy var priceLabel: AICBaseLabel = {
        let label = AICBaseLabel()
        label.font = UIFont.systemFont(ofSize: 20, weight: .bold)
        label.textColor = UIColor.base.current.textColor
        return label
    }()
    
    private lazy var changeLabel: AICBaseLabel = {
        let label = AICBaseLabel()
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = UIColor.base.current.upColor
        return label
    }()
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupNotifications()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setupNotifications()
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        // 添加子视图
        addSubview(priceLabel)
        addSubview(changeLabel)
        
        // 设置约束
        priceLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalToSuperview()
            make.right.equalToSuperview()
            make.height.equalTo(24)
        }
        
        changeLabel.snp.makeConstraints { make in
            make.top.equalTo(priceLabel.snp.bottom).offset(4)
            make.left.equalToSuperview()
            make.right.equalToSuperview()
            make.height.equalTo(18)
        }
    }
    
    // MARK: - Notifications
    private func setupNotifications() {
        // 监听主题变化
        NotificationCenter.default.addObserver(self, 
                                              selector: #selector(themeDidChange), 
                                              name: NSNotification.Name(SSBThemeManagerShouldChangeTheme), 
                                              object: nil)
    }
    
    @objc private func themeDidChange() {
        // 更新UI颜色
        updateTheme()
    }
    
    // MARK: - Public Methods
    func configure(price: String, change: Double) {
        priceLabel.text = price
        
        let changeText = change >= 0 ? "+\(change)%" : "\(change)%"
        changeLabel.text = changeText
        
        // 设置涨跌颜色
        changeLabel.textColor = change >= 0 ? UIColor.base.current.upColor : UIColor.base.current.downColor
    }
    
    // MARK: - Private Methods
    private func updateTheme() {
        priceLabel.textColor = UIColor.base.current.textColor
        
        // 保持涨跌颜色
        if let changeText = changeLabel.text, let change = Double(changeText.replacingOccurrences(of: "%", with: "").replacingOccurrences(of: "+", with: "")) {
            changeLabel.textColor = change >= 0 ? UIColor.base.current.upColor : UIColor.base.current.downColor
        }
    }
}
```

### 4.3 导航最佳实践

1. **统一导航栏**：使用`AICBaseNavigationBar`或`AICBaseNavigationController`
2. **返回按钮**：提供一致的返回按钮行为
3. **标题设置**：使用适当的标题和字体
4. **导航转场**：使用标准的导航转场方法
5. **深度管理**：避免过深的导航层级

```swift
// 导航控制器获取
let rootNavController = UIViewController.aic_getRootNavigationController()

// 页面跳转
func navigateToDetail(marketKey: String) {
    let detailVC = TickerDetailViewController(marketKey: marketKey)
    navigationController?.pushViewController(detailVC, animated: true)
}

// 返回上一页
@objc private func backAction() {
    navigationController?.popViewController(animated: true)
}

// 返回根页面
@objc private func homeAction() {
    navigationController?.popToRootViewController(animated: true)
}

// 模态展示
func presentSettings() {
    let settingsVC = SettingsViewController()
    let navController = AICBaseNavigationController(rootViewController: settingsVC)
    navController.modalPresentationStyle = .fullScreen
    present(navController, animated: true, completion: nil)
}

// 模态关闭
@objc private func dismissAction() {
    dismiss(animated: true, completion: nil)
}
```
