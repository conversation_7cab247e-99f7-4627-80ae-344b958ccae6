# 9. 数据绑定

AICoin-iOS项目使用ObservableValue作为主要的数据绑定方案。本文档将详细介绍ObservableValue的使用方法和示例代码。

## 9.1 ObservableValue数据绑定

ObservableValue是AICoin-iOS项目中使用的一种轻量级数据绑定方案，通过属性观察方法通知所有观察者数据变化。它简洁直观，特别适合UI更新和数据同步场景。

### 9.1.1 基本概念

ObservableValue是一个泛型类，可以包装任何类型的值，并在值发生变化时通知所有订阅者。它的核心功能包括：

1. 创建可观察的值
2. 订阅值的变化
3. 更新值并自动通知订阅者
4. 管理订阅的生命周期

### 9.1.2 创建可观察值

有两种方式可以创建ObservableValue：

#### 直接创建ObservableValue实例

```swift
// 创建一个包含初始值的ObservableValue
let price = ObservableValue<Double>(default: 0.0)
let marketModels = ObservableValue<[TickerMarketListModel]>(default: [])
```

#### 使用@ObservableObj属性包装器

```swift
// 使用属性包装器创建可观察属性
@ObservableObj
var price: Double = 0.0

@ObservableObj
var isSelected: Bool = false
```

### 9.1.3 订阅数据变化

订阅ObservableValue的变化，以便在值发生变化时执行相应的操作：

```swift
// 直接创建的ObservableValue订阅
price.subscribe { [weak self] newPrice in
    // 处理价格变化
    self?.updatePriceLabel(with: newPrice)
}.disposed(by: disposeBag)

// 使用属性包装器创建的ObservableValue订阅
$price.subscribe { [weak self] newPrice in
    // 处理价格变化
    self?.updatePriceLabel(with: newPrice)
}.disposed(by: disposeBag)
```

ObservableValue提供了两种订阅方法：
- `subscribe`: 仅在值发生变化时通知订阅者
- `subscribeImmediately`: 订阅后立即通知一次，之后在值发生变化时再通知

```swift
// 订阅并立即接收当前值
marketModels.subscribeImmediately { [weak self] models in
    // 立即处理当前数据，之后在数据变化时也会调用
    self?.refreshUI(with: models)
}.disposed(by: disposeBag)
```

### 9.1.4 更新数据

更新ObservableValue的值，会自动通知所有订阅者：

```swift
// 直接创建的ObservableValue更新
price.value = 42.0  // 所有订阅者都会收到通知

// 使用属性包装器创建的ObservableValue更新
price = 42.0  // 所有订阅者都会收到通知
```

### 9.1.5 管理订阅生命周期

为了避免内存泄漏，需要正确管理订阅的生命周期。AICoin-iOS项目使用DisposeBag来管理订阅：

```swift
// 创建DisposeBag
private let disposeBag = DisposeBag()

// 将订阅添加到DisposeBag中
marketModels.subscribe { [weak self] models in
    self?.refreshUI(with: models)
}.disposed(by: disposeBag)

// 当DisposeBag被释放或调用dispose()方法时，所有订阅都会被取消
```

在视图控制器的deinit方法中，DisposeBag会自动释放，从而取消所有订阅。也可以手动调用dispose()方法取消订阅：

```swift
// 手动取消所有订阅
disposeBag.dispose()
```

### 9.1.6 实际应用示例

#### 示例1：行情价格更新

```swift
// ViewModel中定义可观察属性
class TickerViewModel {
    let marketModels = ObservableValue<[TickerMarketListModel]>(default: [])

    func fetchData() {
        // 获取数据后更新ObservableValue
        AICTickerRequestOperation.requestMarketList { [weak self] models in
            self?.marketModels.value = models
        }
    }
}

// ViewController中订阅数据变化
class TickerViewController: UIViewController {
    private let viewModel = TickerViewModel()
    private let disposeBag = DisposeBag()

    override func viewDidLoad() {
        super.viewDidLoad()

        // 订阅市场数据变化
        viewModel.marketModels.subscribe { [weak self] models in
            self?.tableView.reloadData()
        }.disposed(by: disposeBag)

        // 获取数据
        viewModel.fetchData()
    }
}
```

#### 示例2：悬浮窗数据绑定

```swift
// 悬浮窗设置数据模型
class TickerPipSettingCacheModel {
    static let shared = TickerPipSettingCacheModel()

    // 唯一数据源
    lazy var marketModels: ObservableValue<[TickerMarketListModel]> = ObservableValue(default: AICTickerDataBase.share.fetchTickerMarketListModelWithMarketListKeys(keys))

    private let disposeBag = DisposeBag()

    // 绑定更改
    init() {
        marketModels.subscribe { [weak self] models in
            let keys = models.map { $0.marketListKey }
            self?.keys = keys
        }.disposed(by: disposeBag)
    }
}

// 悬浮窗视图中订阅数据变化
class FloatingWindow: UIWindow {
    private let viewModel = TickerPipSettingCacheModel.shared
    private let disposeBag = DisposeBag()

    override init(frame: CGRect) {
        super.init(frame: frame)

        // 订阅市场数据变化
        viewModel.marketModels.subscribe { [weak self] models in
            self?.transform(num: models.count)
            self?.refreshListView(by: models)
        }.disposed(by: disposeBag)
    }
}
```

### 9.1.7 与KVO的结合使用

在某些情况下，AICoin-iOS项目也使用KVO结合DisposeBag来实现数据绑定：

```swift
// 使用KVO监听属性变化
let dis = viewModel.addObserverBlockReturnDis(forKeyPath: subKey, block: { [weak self] (_model, _old_value, _new_value) in
    guard let self = self else {
        return
    }

    let value = viewModel.getData(key: cacheKey)
    let str = type.formatter(with: value)

    AICSwiftTool.runonMainQueue {
        self.subValue2Label.text = str
    }
})

// 将KVO订阅添加到DisposeBag中
self.disposesBag?.insert(dis)
```

### 9.1.8 优势

1. **简洁直观**：ObservableValue的API设计简洁，使用直观
2. **类型安全**：使用泛型实现，提供类型安全的数据绑定
3. **内存管理**：通过DisposeBag和weak引用避免内存泄漏
4. **轻量级**：不需要引入大型第三方库，减少项目依赖
5. **易于集成**：可以与现有代码无缝集成，不需要大规模重构

ObservableValue是AICoin-iOS项目中推荐的数据绑定方案，适用于UI更新和数据同步场景。它提供了简单而强大的响应式编程能力，满足项目的大部分数据绑定需求。
