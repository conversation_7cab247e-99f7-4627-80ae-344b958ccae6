# AICoin-iOS 主题管理系统文档

## 目录

1. [主题系统概述](#1-主题系统概述)
2. [主题管理器 SSBThemeManager](#2-主题管理器-ssbthememanager)
3. [主题资源管理](#3-主题资源管理)
4. [使用方法](#4-使用方法)

## 1. 主题系统概述

AICoin-iOS应用支持白天和黑夜两种主题模式，为用户提供不同场景下的最佳视觉体验。主题系统具有以下特点：

### 1.1 主题模式

- **白天模式（DaytimeTheme）**：明亮的背景和深色文本，适合日间使用
- **黑夜模式（NighttimeTheme）**：深色背景和浅色文本，减少夜间使用时的视觉疲劳

### 1.2 主题管理方式

- **全局管理**：通过`SSBThemeManager`单例进行全局主题状态管理
- **模块化资源**：每个模块各自管理自己的主题资源（颜色和图片）
- **全局通知机制**：主题切换时通过通知机制刷新整个应用界面

### 1.3 K线独立主题

K线图表模块有独立的主题管理系统，可以不跟随应用的主题设置，这主要是考虑到专业用户可能有特定的K线图表主题偏好。

## 2. 主题管理器 SSBThemeManager

`SSBThemeManager`是AICoin应用的主题管理核心类，负责主题状态的存储、切换和通知。

### 2.1 主要功能

- 存储和获取当前主题状态
- 提供主题切换方法
- 发送主题变更通知
- 管理主题资源

### 2.2 主要方法

```swift
// 获取当前主题名称
let currentTheme = SSBThemeManager.shared().currentThemeName  // .day 或 .night

// 判断是否为夜间主题
let isNightTheme = aic_isNightTheme()

// 切换主题（带动画效果）
SSBThemeManager.shared().changeTheme(at: view) {
    // 主题切换完成后的回调
}

// 简单切换主题（无动画，无Toast提示）
SSBThemeManager.shared().simpleChangeTheme()

// 获取主题颜色（使用全局函数）
let color = colorForKey("colorKey")  // 使用全局函数获取颜色
```

## 3. 主题资源管理

AICoin应用的主题资源主要包括颜色和图片两部分，采用模块化管理方式。

### 3.1 颜色管理

颜色管理通过`BaseThemeProtocol`协议和实现类进行配置，每个模块定义自己的主题颜色协议和实现类。颜色管理的具体实现方式请参考[AICoin-iOS 基础组件和工具类使用文档](AICoin基础组件和工具类使用文档.md#2-2-2-获取主题颜色)。

#### 3.1.1 内置主题颜色

项目中已经实现了多个模块的主题颜色：

- **BaseTheme**：基础模块颜色
- **MomentTheme**：动态模块颜色
- **HomeTheme**：主页模块颜色

### 3.2 图片资源管理

图片资源通过Assets.xcassets中的特定文件夹进行管理，每个模块在自己的Assets中配置白天和黑夜两套图片资源。

#### 3.2.1 图片资源结构

```
ModuleAssets.xcassets/
├── BaseDay/              # 白天模式图片
│   ├── nav_back.imageset
│   ├── tab_home.imageset
│   └── ...
└── BaseNight/            # 黑夜模式图片
    ├── nav_back.imageset
    ├── tab_home.imageset
    └── ...
```

#### 3.2.2 图片资源协议

```swift
protocol AICImageProtocol {
    static func imageForDay(name: String) -> UIImage?
    static func imageForNight(name: String) -> UIImage?
    static func image(name: String) -> UIImage?
}

class BaseImage: AICImageProtocol {
    class func imageForDay(name: String) -> UIImage? {
        return UIImage(named: "BaseDay/\(name)")
    }

    class func imageForNight(name: String) -> UIImage? {
        return UIImage(named: "BaseNight/\(name)")
    }

    class func image(name: String) -> UIImage? {
        if aic_isNightTheme(),
           let img = self.imageForNight(name: name) {
            return img
        }
        return self.imageForDay(name: name)
    }
}

extension UIImage {
    static let base = BaseImage()
}
```

## 4. 使用方法

### 4.1 获取主题颜色

根据AICoin编码规范，获取主题颜色的方式如下：

```swift
// 基本用法
let color = UIColor.模块.current.颜色属性

// 示例：获取基础模块的副标题颜色
let subtitleColor = UIColor.baseTheme.current.cellSubtitleColor

// 示例：获取登录模块的文本颜色
let textColor = UIColor.login.current.textColor

// 使用UIColor扩展方法获取主题颜色
let themeColor = UIColor.themeColor(day: 0xFFFFFF, night: 0x121212)

// 使用UIColor扩展方法获取主题颜色（使用UIColor对象）
let themeColor = UIColor.themeColor(dayColor: .white, nightColor: .black)

// 获取K线图表专用主题颜色
let klineColor = UIColor.themeColorFollowKLine(day: 0xFFFFFF, night: 0x121212)
```

### 4.2 获取主题图片

```swift
// 基本用法
let image = UIImage.模块.image(name: "图片名称")

// 示例：获取基础模块的返回按钮图片
let backImage = UIImage.base.image(name: "nav_back")

// 示例：获取登录模块的图标
let logoImage = UIImage.login.image(name: "login_logo")
```

### 4.3 在代码中判断当前主题

```swift
// 判断是否为夜间主题
if aic_isNightTheme() {
    // 夜间主题下的特殊处理
} else {
    // 日间主题下的特殊处理
}

// 根据主题设置不同的值
let cornerRadius = aic_isNightTheme() ? 8.0 : 4.0
```