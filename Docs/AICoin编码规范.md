# AICoin iOS项目编码规范

本文档定义了AICoin iOS项目的编码规范，你需要遵循以下规范，并根据实际情况进行调整。

## 一、命名规范


### 1. 命名风格

- **类名**：使用大驼峰命名法（UpperCamelCase）
  ```swift
  class AICTickerViewController: AICBaseViewController { }
  ```

- **变量和方法**：使用小驼峰命名法（lowerCamelCase）
  ```swift
  var tickerName: String
  func requestTickerData() { }
  ```

- **常量**：使用小驼峰命名法
  ```swift
  let maxRetryCount = 3
  static let shared = AICHttpManager()
  ```

- **枚举**：枚举类型使用大驼峰命名法，枚举值使用小驼峰命名法
  ```swift
  enum AICNetworkEnvironment {
      case production
      case testing
      case proProduction
      case proTesting
  }
  ```

### 2. 语义化命名

命名应当清晰表达含义，避免使用缩写：

```swift
// 推荐
func fetchUserInfo() { }

// 不推荐
func getData() { }
```

## 二、代码组织

### 1. 文件组织结构

项目使用模块化组织方式，按照功能模块划分目录结构：

```
AICoin-iOS/
├── AICoin/                # 主项目目录
│   ├── Module/            # 业务模块
│   │   ├── Base/          # 基础组件
│   │   ├── CandleChart/   # K线图表模块
│   │   ├── Content/       # 内容模块（资讯、快讯、动态）
│   │   ├── HomePage/      # 首页模块
│   │   ├── Login/         # 登录模块
│   │   ├── Me/            # 个人中心模块
│   │   ├── Ticker/        # 行情模块
│   │   ├── Trade/         # 交易模块
│   │   └── 自选/          # 自选模块
│   ├── Resources/         # 资源文件
│   │   ├── Images/        # 图片资源
│   │   ├── Fonts/         # 字体资源
│   │   └── Sounds/        # 音频资源
│   ├── Supporting Files/  # 配置文件
│   │   ├── Pro/           # 正式环境配置
│   │   ├── Test/          # 测试环境配置
│   │   └── OverSea/       # 海外版配置
│   └── Vendor/            # 第三方库
├── AICoinWigetKit/        # 小组件模块
├── Docs/                  # 文档
└── Pods/                  # CocoaPods依赖
```

#### 模块内部结构

项目中的模块内部结构主要采用MVC架构，部分新模块采用MVVM架构。根据模块的不同，内部结构可能有所差异：

##### MVC架构模块

```
Module/Ticker/
├── Model/                 # 数据模型
│   ├── TickerModel.swift
│   └── DB/                # 数据库相关
├── NetManager/            # 网络请求管理
│   └── TickerNetworkManager.swift
├── 行情列表/              # 功能子模块
│   ├── HTTP控制器/        # 控制器
│   └── View/              # 视图组件
├── 行情详情/              # 功能子模块
│   ├── Model/             # 子模块模型
│   ├── View/              # 子模块视图
│   └── 报价/              # 子功能
│       ├── View/          # 子功能视图
│       └── LatestStrategy/ # 子功能策略
├── Resource/              # 模块资源
│   └── TickerImage.xcassets/ # 图片资源
└── Tools/                 # 工具类
    └── Category/          # 分类扩展
```

##### MVVM架构模块

```
Module/自选/地址/          # 自选-地址模块（MVVM架构）
├── Model/                 # 数据模型
│   └── AddressModel.swift
├── View/                  # 视图组件
│   └── AddressCell.swift
├── ViewModel/             # 视图模型
│   └── AddressViewModel.swift
└── AddressViewController.swift # 视图控制器

Module/HomePage/指标胜率+Pro版K线/ # 首页-指标模块（MVVM架构）
├── Pro版K线/              # 子功能
│   ├── Model/             # 数据模型
│   ├── View/              # 视图组件
│   └── ViewModel/         # 视图模型
│       ├── BigOrderContainerViewModel.swift
│       └── BigOrderItemViewModel.swift
└── 指标胜率/              # 子功能
    ├── Model/             # 数据模型
    │   └── StrategySquareListViewModel.swift
    ├── View/              # 视图组件
    └── Controller/        # 控制器
```

新开发的功能模块应优先采用MVVM架构，以提高代码的可维护性和可测试性。

### 2. 文件命名规范

文件名应清晰表达其内容和用途，遵循以下规则：

#### 控制器文件

控制器文件使用`功能+ViewController`格式命名：

```
TickerViewController.swift
TickerDetailViewController.swift
LoginViewController.swift
```

#### 视图文件

视图文件使用`功能+View`或`功能+Cell`格式命名：

```
TickerHeaderView.swift
TickerCell.swift
PriceChartView.swift
```

#### 模型文件

模型文件使用`功能+Model`格式命名：

```
TickerModel.swift
UserModel.swift
TradeModel.swift
```

#### 视图模型文件

视图模型文件使用`功能+ViewModel`格式命名：

```
TickerViewModel.swift
LoginViewModel.swift
TradeViewModel.swift
```

#### 扩展文件

扩展文件使用`类型+Feature`格式命名：

```
UIView+Extension.swift
String+Localized.swift
Date+Format.swift
```

#### 工具类文件

工具类文件使用`功能+Helper/Manager/Tool`格式命名：

```
NetworkManager.swift
DateHelper.swift
CacheManager.swift
ImageTool.swift
```

### 3. 视图控制器基类

所有视图控制器必须继承自`AICBaseViewController`：

```swift
// 推荐
class AICHomeViewController: AICBaseViewController {
    // 实现代码
}

// 不推荐
class HomeViewController: UIViewController {
    // 实现代码
}
```

### 4. 类内部组织

类内部代码按以下顺序组织：

```swift
class AICExampleViewController: AICBaseViewController {
    // MARK: - Properties
    // 首先是属性声明

    // MARK: - Life Cycle
    // 然后是生命周期方法

    // MARK: - UI Setup
    // UI相关的设置方法

    // MARK: - Data
    // 数据处理方法

    // MARK: - Actions
    // 用户交互事件处理

    // MARK: - Private Methods
    // 私有辅助方法
}
```

### 5. 扩展使用

使用扩展分离不同功能的代码：

```swift
// MARK: - UITableViewDataSource
extension AICHomeViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return dataArray.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        // 配置并返回单元格
    }
}

// MARK: - UITableViewDelegate
extension AICHomeViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        // 处理选择事件
    }
}
```

### 6. 获取根控制器

获取根控制器应使用`UIViewController.aic_getRootNavigationController()`方法：

```swift
// 推荐
let rootNavController = UIViewController.aic_getRootNavigationController()
rootNavController.pushViewController(viewController, animated: true)

// 不推荐
let appDelegate = UIApplication.shared.delegate as? AppDelegate
let rootNavController = appDelegate?.window?.rootViewController as? UINavigationController
```

## 三、网络请求规范

### 1. 使用共享实例

所有网络请求都应通过`AICHttpManager.shared`发起：

```swift
AICHttpManager.shared.post("/api/v1/endpoint",
                           parameters: params,
                           progress: nil,
                           success: { task, response in
    // 处理成功响应
}, failure: { task, error in
    // 处理错误
})
```

### 2. 请求封装

为每个业务模块创建专门的网络请求管理类：

```swift
class TickerNetworkManager {
    static let shared = TickerNetworkManager()

    func requestTickerList(completion: @escaping ([TickerModel]?, Error?) -> Void) {
        AICHttpManager.shared.post("/api/v1/ticker/list",
                                  parameters: nil,
                                  progress: nil,
                                  success: { task, response in
            let json = JSON(response ?? "")
            if json["success"].boolValue {
                let dataArray = json["data"].arrayValue
                let models = dataArray.compactMap { TickerModel.model(withJSON: $0) }
                completion(models, nil)
            } else {
                completion(nil, NSError(domain: "APIError",
                                      code: json["errorCode"].intValue,
                                      userInfo: nil))
            }
        }, failure: { task, error in
            completion(nil, error)
        })
    }
}
```

### 3. 响应处理

使用SwiftyJSON处理响应数据：

```swift
let json = JSON(response ?? "")
if json["success"].boolValue {
    // 请求成功
    let data = json["data"]
    // 处理数据
} else {
    // 请求失败
    let errorCode = json["errorCode"].intValue
    let errorMessage = json["error"].stringValue
    // 处理错误
}
```

### 4. 弱引用避免内存泄漏

在闭包中使用`[weak self]`避免循环引用：

```swift
AICHttpManager.shared.post("/api/endpoint", parameters: params, progress: nil, success: { [weak self] task, response in
    guard let self = self else { return }
    // 使用self处理响应
}, failure: { [weak self] task, error in
    guard let self = self else { return }
    // 使用self处理错误
})
```

### 5. 取消请求

在视图控制器的`viewWillDisappear`方法中取消未完成的网络请求：

```swift
class MyViewController: AICBaseViewController {
    var dataTask: URLSessionDataTask?

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        dataTask?.cancel()
    }

    func loadData() {
        dataTask?.cancel() // 取消之前的请求

        dataTask = AICHttpManager.shared.post("/api/endpoint", parameters: nil, progress: nil, success: { [weak self] task, response in
            // 处理响应
        }, failure: { [weak self] task, error in
            // 处理错误
        })
    }
}
```

## 四、内存管理

### 1. 避免循环引用

在闭包中使用`[weak self]`或`[unowned self]`避免循环引用：

```swift
// 使用[weak self]
someAsyncOperation { [weak self] result in
    guard let self = self else { return }
    self.updateUI(with: result)
}

// 短生命周期闭包可使用[unowned self]
UIView.animate(withDuration: 0.3) { [unowned self] in
    self.view.alpha = 0
}
```

### 2. 代理使用弱引用

代理属性应声明为弱引用：

```swift
weak var delegate: MyProtocol?
```

## 五、多语言支持

### 1. 字符串本地化

所有展示给用户的字符串必须支持多语言，使用`.base.localized`进行本地化：

```swift
// 推荐
titleLabel.text = "行情".base.localized

// 不推荐
titleLabel.text = "行情"
```

### 2. 格式化字符串

包含变量的字符串使用格式化方式：

```swift
// 推荐
let priceText = String(format: "price_format".base.localized, price)

// 不推荐
let priceText = "价格：\(price)"
```

### 3. 本地化文件

每添加新字符串，必须同时在`Localizable.strings`文件中添加对应的键值对：

```
// Localizable.strings (English)
"行情" = "market_title";
"价格：%ld" = "Price: %ld";

// Localizable.strings (Chinese, Simplified)
"行情" = "行情";
"价格：%ld" = "价格：%ld";

// Localizable.strings (Chinese, Traditional)
"行情" = "行情";
"价格：%ld" = "價格：%ld";
```

## 六、参数命名

### 1. 统一命名风格

同一概念在不同地方应使用相同的命名：

```swift
// 推荐
struct Ticker {
    let tickerId: String
    let tickerName: String
}

class TickerViewController {
    var tickerId: String
    var tickerName: String
}

// 不推荐
struct Ticker {
    let id: String
    let name: String
}

class TickerViewController {
    var tickerId: String
    var symbol: String // 应使用tickerName保持一致
}
```

### 2. 方法参数

方法参数应具有描述性，明确表达用途：

```swift
// 推荐
func configure(with ticker: TickerModel) { }
func load(fromURL url: URL) { }

// 不推荐
func configure(_ t: TickerModel) { }
func load(_ u: URL) { }
```

## 七、错误处理

### 1. 避免强制解包

避免使用`!`强制解包，应使用可选绑定或guard语句：

```swift
// 推荐
if let price = ticker.price {
    // 使用price
}

guard let price = ticker.price else {
    // 处理nil情况
    return
}

// 不推荐
let price = ticker.price!
```

### 2. 提前退出

使用guard语句提前处理错误情况：

```swift
func process(data: Data?) {
    guard let data = data else {
        handleError(.noData)
        return
    }

    guard data.count > 0 else {
        handleError(.emptyData)
        return
    }

    // 处理有效数据
}
```

### 3. 网络错误处理

网络请求错误处理应包括网络连接检查：

```swift
func handleAPIError(_ error: Error) {
    if !AICHttpManager.shared.isReachable {
        // 处理网络不可用情况
        showToast(message: "网络连接不可用，请检查网络设置")
    } else {
        // 处理其他错误
        showToast(message: error.localizedDescription)
    }
}
```

## 八、注释规范

### 1. 代码注释

使用`//`添加单行注释，使用`/// `添加文档注释：

```swift
// 这是普通注释

/// 这是文档注释，会显示在代码提示中
/// - Parameter ticker: 币种模型
/// - Returns: 格式化后的价格字符串
func formatPrice(ticker: TickerModel) -> String {
    // 实现格式化逻辑
}
```

### 2. MARK注释

使用`// MARK:`分割代码区域：

```swift
// MARK: - Properties

// MARK: - Lifecycle Methods

// MARK: - Private Methods
```

## 九、UI布局

### 1. 使用约束或SnapKit

推荐使用Auto Layout实现UI布局，可借助SnapKit简化代码：

```swift
priceLabel.snp.makeConstraints { make in
    make.top.equalTo(nameLabel.snp.bottom).offset(8)
    make.left.equalToSuperview().offset(16)
    make.right.equalToSuperview().offset(-16)
}
```

### 2. 统一边距

使用统一的边距常量：

```swift
private struct Layout {
    static let standardMargin: CGFloat = 16.0
    static let smallMargin: CGFloat = 8.0
    static let largeMargin: CGFloat = 24.0
}

// 使用
containerView.snp.makeConstraints { make in
    make.top.equalToSuperview().offset(Layout.standardMargin)
    make.left.equalToSuperview().offset(Layout.standardMargin)
    make.right.equalToSuperview().offset(-Layout.standardMargin)
}
```

## 十、资源管理

### 1. 颜色使用

所有颜色定义必须使用项目封装的主题管理系统，遵循以下规则：

1. 优先使用预定义的主题颜色：

```swift
// 推荐 - 使用预定义主题颜色
label.textColor = UIColor.baseCurrentTextColor
view.backgroundColor = UIColor.baseCurrentBgColor
button.backgroundColor = UIColor.baseCurrentMainColor

// 不推荐 - 直接使用RGB值
label.textColor = UIColor(red: 51/255, green: 51/255, blue: 51/255, alpha: 1.0)
```

2. 如果在`UIColor+BaseExtension`或`UIColor+HomePageTools`中没有相应的色号，应使用`DynamicHelper.themeColor`方法：

```swift
// 推荐 - 使用DynamicHelper创建动态颜色
let borderColor = DynamicHelper.themeColor(day: 0x333333, night: 0xC3C7D9)
let subtitleColor = DynamicHelper.themeColor(day: 0x666666, night: 0x999999)

// 不推荐 - 使用UIColor的动态颜色API
let color = UIColor { traitCollection in
    return traitCollection.userInterfaceStyle == .dark ?
        UIColor(red: 195/255, green: 199/255, blue: 217/255, alpha: 1) :
        UIColor(red: 51/255, green: 51/255, blue: 51/255, alpha: 1)
}
```

3. 禁止使用不支持深色模式的固定颜色：

```swift
// 错误用法 - 不适配深色模式
view.backgroundColor = UIColor(red: 242/255, green: 242/255, blue: 247/255, alpha: 1.0)
button.tintColor = UIColor(hex: 0x333333)
```

### 2. 图片使用

图片应根据所属模块使用对应的命名空间：

```swift
// 行情模块图片
let tickerImage = UIImage.ticker.image(name: "ticker_icon")

// 通用/基础图片
let logoImage = UIImage.base.image(name: "app_logo")

// 不推荐
let image = UIImage(named: "ticker_icon")
```

## 十一、本地数据库使用规范

### 1. WCDB使用规范

项目使用WCDB.Swift进行数据持久化，遵循以下规范：

#### 数据库模型定义

- 数据库模型类必须实现`TableCodable`协议
- 使用`CodingKeys`枚举定义表字段映射
- 使用适当的数据类型，避免滥用字符串类型存储复杂数据

```swift
// 推荐
final class MessageModel: TableCodable {
    var messageID: Int = 0
    var content: String = ""
    var senderID: String = ""
    var timestamp: Int = 0

    enum CodingKeys: String, CodingTableKey {
        typealias Root = MessageModel
        static let objectRelationalMapping = TableBinding(CodingKeys.self)
        case messageID
        case content
        case senderID
        case timestamp
    }
}

// 不推荐 - 缺少类型安全和映射定义
class MessageModel: TableCodable {
    var data: [String: Any] = [:]

    // 缺少CodingKeys定义
}
```

#### 复杂数据类型处理

对于复杂数据类型（如字典、数组、日期等），使用计算属性和序列化方法进行转换：

```swift
// 数据库模型
final class MessageModel: TableCodable {
    var dataJSON: String = "{}"  // 存储JSON字符串
    var timestamp: Int = 0       // 存储时间戳

    // 其他属性和CodingKeys定义...
}

// 扩展提供类型安全的访问方法
extension MessageModel {
    // 字典数据的计算属性
    var data: [String: Any] {
        get {
            if let jsonData = dataJSON.data(using: .utf8),
               let dict = try? JSONSerialization.jsonObject(with: jsonData) as? [String: Any] {
                return dict
            }
            return [:]
        }
        set {
            if let jsonData = try? JSONSerialization.data(withJSONObject: newValue),
               let jsonString = String(data: jsonData, encoding: .utf8) {
                dataJSON = jsonString
            }
        }
    }

    // 日期的计算属性
    var date: Date {
        get {
            return Date(timeIntervalSince1970: TimeInterval(timestamp))
        }
        set {
            timestamp = Int(newValue.timeIntervalSince1970)
        }
    }
}
```

#### 数据库管理器

- 使用单例模式管理数据库连接
- 为每个数据库表创建专门的管理类
- 提供清晰的CRUD（创建、读取、更新、删除）操作方法
- 使用事务处理批量操作

```swift
class MessageDBManager {
    // 单例
    static let shared = MessageDBManager()
    private init() {
        setupDatabase()
    }

    // 数据库和表名
    private let database: Database
    private let tableName = "messages"

    // 初始化数据库
    private func setupDatabase() {
        let path = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first! + "/messages.db"
        database = Database(withPath: path)

        do {
            try database.create(table: tableName, of: MessageModel.self)
        } catch {
            print("创建表失败: \(error)")
        }
    }

    // CRUD操作
    func insert(message: MessageModel) -> Bool {
        do {
            try database.insert(objects: message, intoTable: tableName)
            return true
        } catch {
            print("插入失败: \(error)")
            return false
        }
    }

    func getMessage(id: Int) -> MessageModel? {
        do {
            return try database.getObject(fromTable: tableName, where: MessageModel.Properties.messageID == id)
        } catch {
            print("查询失败: \(error)")
            return nil
        }
    }

    func update(message: MessageModel) -> Bool {
        do {
            try database.update(table: tableName,
                               on: MessageModel.Properties.all,
                               with: message,
                               where: MessageModel.Properties.messageID == message.messageID)
            return true
        } catch {
            print("更新失败: \(error)")
            return false
        }
    }

    func delete(id: Int) -> Bool {
        do {
            try database.delete(fromTable: tableName, where: MessageModel.Properties.messageID == id)
            return true
        } catch {
            print("删除失败: \(error)")
            return false
        }
    }

    // 使用事务处理批量操作
    func batchInsert(messages: [MessageModel]) -> Bool {
        do {
            try database.run(transaction: {
                for message in messages {
                    try database.insert(objects: message, intoTable: tableName)
                }
            })
            return true
        } catch {
            print("批量插入失败: \(error)")
            return false
        }
    }
}
```

#### 查询优化

- 使用适当的索引提高查询性能
- 避免在循环中执行数据库操作
- 使用条件查询减少结果集大小
- 使用分页加载大量数据

```swift
// 创建索引
try database.create(index: ["messageID", "timestamp"],
                   on: tableName,
                   named: "message_index")

// 条件查询
func getMessages(fromTime: Date, toTime: Date, limit: Int = 20, offset: Int = 0) -> [MessageModel] {
    do {
        let fromTimestamp = Int(fromTime.timeIntervalSince1970)
        let toTimestamp = Int(toTime.timeIntervalSince1970)

        return try database.getObjects(
            fromTable: tableName,
            where: MessageModel.Properties.timestamp.between(fromTimestamp, and: toTimestamp),
            orderBy: [MessageModel.Properties.timestamp.asOrder(by: .descending)],
            limit: limit,
            offset: offset
        )
    } catch {
        print("查询失败: \(error)")
        return []
    }
}
```

## 十二、Swift 最佳实践

### 1. 使用 Swift 的现代特性

#### 类型推断

当类型可以被编译器推断时，可以省略类型声明：

```swift
// 推荐
let message = "Hello"
let numbers = [1, 2, 3]
let view = UIView()

// 不推荐（类型已明确）
let message: String = "Hello"
let numbers: [Int] = [1, 2, 3]
let view: UIView = UIView()

// 推荐（类型不明确时声明）
let price: Double = 29.99
let completion: (Result<User, Error>) -> Void = { result in
    // 处理结果
}
```

#### 属性观察器

使用属性观察器（willSet/didSet）监控属性变化：

```swift
var currentTab: TabType = .market {
    didSet {
        updateUI()
        trackTabSelection()
    }
}
```

#### 计算属性

使用计算属性代替简单的方法：

```swift
// 推荐
var formattedPrice: String {
    return String(format: "%.2f", price)
}

// 不推荐
func getFormattedPrice() -> String {
    return String(format: "%.2f", price)
}
```

### 2. 闭包和函数式编程

#### 尾随闭包语法

当函数的最后一个参数是闭包时，使用尾随闭包语法：

```swift
// 推荐
tableView.rx.itemSelected.subscribe(onNext: { [weak self] indexPath in
    self?.handleSelection(at: indexPath)
})

// 不推荐
tableView.rx.itemSelected.subscribe(onNext: ({ [weak self] indexPath in
    self?.handleSelection(at: indexPath)
}))
```

#### 函数式编程

使用 map、filter、reduce 等高阶函数处理集合：

```swift
// 推荐
let names = users.map { $0.name }
let activeUsers = users.filter { $0.isActive }
let totalBalance = accounts.reduce(0) { $0 + $1.balance }

// 不推荐
var names = [String]()
for user in users {
    names.append(user.name)
}

var activeUsers = [User]()
for user in users {
    if user.isActive {
        activeUsers.append(user)
    }
}
```

### 3. 错误处理

#### 使用 Result 类型

使用 Result 类型处理可能失败的操作：

```swift
func fetchUserProfile(completion: @escaping (Result<UserProfile, Error>) -> Void) {
    networkService.request(.userProfile) { result in
        switch result {
        case .success(let response):
            do {
                let profile = try JSONDecoder().decode(UserProfile.self, from: response.data)
                completion(.success(profile))
            } catch {
                completion(.failure(error))
            }
        case .failure(let error):
            completion(.failure(error))
        }
    }
}

// 使用
fetchUserProfile { result in
    switch result {
    case .success(let profile):
        self.updateUI(with: profile)
    case .failure(let error):
        self.handleError(error)
    }
}
```

#### 使用 try? 和 try!

适当使用 try? 和 try!：

```swift
// 使用 try? 处理可能失败但不需要详细错误信息的情况
if let data = try? JSONEncoder().encode(model) {
    // 使用 data
}

// 只在确定不会失败的情况下使用 try!
let image = try! UIImage(data: validImageData)!
```

### 4. 并发和异步编程

#### 使用 DispatchQueue

合理使用 GCD 进行异步操作：

```swift
// 后台线程执行耗时操作
DispatchQueue.global().async {
    let result = self.performHeavyTask()

    // 主线程更新 UI
    DispatchQueue.main.async {
        self.updateUI(with: result)
    }
}
```

#### 避免嵌套回调

使用 DispatchGroup 或 Combine 避免回调地狱：

```swift
// 使用 DispatchGroup
let group = DispatchGroup()

group.enter()
fetchUserProfile { result in
    // 处理结果
    group.leave()
}

group.enter()
fetchUserSettings { result in
    // 处理结果
    group.leave()
}

group.notify(queue: .main) {
    // 所有请求完成后执行
    self.updateUI()
}
```

### 5. 协议和扩展

#### 协议优于继承

优先使用协议和扩展而非继承：

```swift
// 推荐
protocol Loadable {
    func showLoadingView()
    func hideLoadingView()
}

extension Loadable where Self: UIViewController {
    func showLoadingView() {
        // 默认实现
    }

    func hideLoadingView() {
        // 默认实现
    }
}

class ProfileViewController: UIViewController, Loadable {
    // 使用默认实现或自定义实现
}

// 不推荐
class BaseViewController: UIViewController {
    func showLoadingView() {
        // 实现
    }

    func hideLoadingView() {
        // 实现
    }
}

class ProfileViewController: BaseViewController {
    // 继承基类方法
}
```

#### 使用扩展分离功能

使用扩展分离不同功能的代码：

```swift
// MARK: - 核心功能
class TickerViewController: UIViewController {
    // 属性和初始化方法
}

// MARK: - UI 设置
private extension TickerViewController {
    func setupUI() {
        // UI 设置代码
    }
}

// MARK: - 数据加载
private extension TickerViewController {
    func loadData() {
        // 数据加载代码
    }
}

// MARK: - UITableViewDataSource
extension TickerViewController: UITableViewDataSource {
    // 实现数据源方法
}

// MARK: - UITableViewDelegate
extension TickerViewController: UITableViewDelegate {
    // 实现代理方法
}
```

## 十三、架构模式

### 1. 混合架构模式

项目同时使用MVC和MVVM两种架构模式，根据模块的不同选择合适的架构：

#### MVC架构

MVC（Model-View-Controller）：

- **Model**：数据模型，表示业务数据和业务规则
- **View**：视图层，负责UI展示
- **Controller**：控制器，协调Model和View，处理业务逻辑

```swift
// Model
struct TickerModel: Codable {
    let id: String
    let name: String
    let price: Double
    let change: Double
}

// Controller
class TickerViewController: AICBaseViewController {
    // 视图属性
    private lazy var tableView = UITableView()

    // 数据源
    private var tickers: [TickerModel] = []

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadData()
    }

    private func loadData() {
        AICHttpManager.shared.post("/api/v1/ticker/list", parameters: nil, progress: nil, success: { [weak self] task, response in
            guard let self = self, let data = response else { return }
            let json = JSON(data)
            if json["success"].boolValue {
                let dataArray = json["data"].arrayValue
                let models = dataArray.compactMap { TickerModel.model(withJSON: $0) }
                self.tickers = models
                self.tableView.reloadData()
            } else {
                // 处理API错误
                let errorCode = json["errorCode"].intValue
                let errorMessage = json["error"].stringValue
                print("API错误: \(errorCode) - \(errorMessage)")
            }
        }, failure: { [weak self] task, error in
            // 处理网络错误
            print("网络错误: \(error.localizedDescription)")
        })
    }
}
```

#### MVVM架构（推荐使用）

MVVM（Model-View-ViewModel）：

- **Model**：数据模型，表示业务数据和业务规则
- **View**：视图层，包括UIViewController和自定义视图
- **ViewModel**：视图模型，处理业务逻辑并提供视图所需的数据

```swift
// Model
struct AddressModel: Codable {
    let address: String
    let balance: Double
    let platform: String
}

// ViewModel
class AddressViewModel {
    // 数据源
    private(set) var addresses: [AddressModel] = []

    // 回调
    var didLoad: (() -> Void)?

    // 加载数据
    func requestAddressList() {
        AICHttpManager.shared.post("/api/v1/address/list",parameters: nil, progress: nil, success: { [weak self] task, response in
            guard let self = self else { return }
            let json = JSON(response ?? "")
            if json["success"].boolValue {
                let dataArray = json["data"].arrayValue
                let models = dataArray.compactMap { AddressModel.model(withJSON: $0) }
                self.addresses = models
                self.didLoad?()
            } else {
                // 处理API错误
                let errorCode = json["errorCode"].intValue
                let errorMessage = json["error"].stringValue
                print("API错误: \(errorCode) - \(errorMessage)")
            }
        }, failure: { [weak self] task, error in
            // 处理网络错误
            print("网络错误: \(error.localizedDescription)")
        })
    }

    // 提供UI所需的数据
    func numberOfItems() -> Int {
        return addresses.count
    }

    func address(at index: Int) -> AddressModel? {
        guard index < addresses.count else { return nil }
        return addresses[index]
    }
}

// View (ViewController)
class AddressViewController: AICBaseViewController {
    private let viewModel = AddressViewModel()
    private lazy var tableView = UITableView()

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindViewModel()
        loadData()
    }

    private func bindViewModel() {
        viewModel.didLoad = { [weak self] in
            self?.tableView.reloadData()
        }
    }

    private func loadData() {
        viewModel.requestAddressList()
    }
}
```

## 十四、总结与最佳实践

### 1. 代码审查清单

在提交代码前，请检查以下事项：

- [ ] 代码遵循命名规范
- [ ] 没有硬编码的字符串（已本地化）
- [ ] 没有内存泄漏（检查循环引用）
- [ ] 适当的错误处理
- [ ] 支持深色模式
- [ ] 代码有适当的注释
- [ ] 没有编译警告
- [ ] 代码格式一致

### 2. 性能优化

- 避免在主线程执行耗时操作
- 使用缓存减少网络请求
- 优化表格视图性能（重用单元格、异步加载图片）
- 避免过度使用自动布局约束
- 使用适当的集合类型（Array、Set、Dictionary）

### 3. 安全最佳实践

- 不要在代码中硬编码敏感信息（API密钥、密码等）
- 使用安全的加密算法
- 验证所有用户输入
- 使用HTTPS进行网络通信
- 敏感数据存储在钥匙串中



## 十五、Swift和Objective-C混编规范

虽然项目主要使用Swift开发，但仍有部分历史代码使用Objective-C编写。在混编环境中，应遵循以下规范：

### 1. 新功能开发

- 所有新功能模块应使用Swift开发
- 如需使用现有Objective-C代码，应通过桥接方式调用

### 2. 桥接文件管理

- 在桥接头文件(`AICoin-Bridging-Header.h`)中只导入必要的Objective-C头文件
- 桥接头文件应按模块分组并添加注释
- Swift代码暴露给Objective-C时，使用`@objc`标记相关类和方法

```swift
// Swift代码暴露给Objective-C
@objc class AICTickerManager: NSObject {
    @objc static let shared = AICTickerManager()

    @objc func fetchTickerData(completion: @escaping ([String: Any]?, Error?) -> Void) {
        // 实现代码
    }
}
```

### 3. 类型转换

- 在Swift和Objective-C之间传递数据时，注意类型映射关系
- 使用`as?`安全转换类型，避免强制转换
- 对于复杂对象，考虑创建专门的转换方法

```swift
// Swift中调用Objective-C方法
if let result = objcManager.fetchData() as? [String: Any] {
    // 处理结果
}

// 转换方法示例
func convert(from objcModel: ObjcTickerModel) -> TickerModel {
    return TickerModel(
        id: objcModel.identifier,
        name: objcModel.name,
        price: objcModel.price.doubleValue
    )
}
```

遵循本规范将帮助我们保持代码质量，提高开发效率，减少bug，并使新成员能够更快地融入团队。