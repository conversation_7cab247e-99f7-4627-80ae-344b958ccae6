# 8. 工具类

AICoin-iOS项目中包含了丰富的工具类，用于处理字符串、日期和数字格式化等常见操作。本文档将详细介绍这些工具类的使用方法和示例代码。

## 8.1 字符串处理

AICoin-iOS项目中提供了多种字符串处理工具，主要通过String的扩展实现。

### 8.1.1 基础字符串操作

```swift
// 字符串截取
let str = "AICoin"
let firstChar = str[0]  // "A"
let subStr1 = str.substring(fromIndex: 2)  // "Coin"
let subStr2 = str.substring(toIndex: 3)  // "AIC"
let rangeStr = str[1..<4]  // "ICo"

// 本地化字符串
let localizedStr = "ticker_title".ticker.localized  // 使用Ticker模块本地化
let baseLocalizedStr = "common_confirm".base.localized  // 使用基础模块本地化
```

### 8.1.2 字符串搜索和高亮

```swift
// 搜索分词
let searchText = "BTC/USDT"
let tokens = searchText.searchTokenize  // 返回 ["BTC", "USDT", "/"]

// 高亮显示
let text = "BTC价格上涨"
let keyWords = ["BTC"]
let highlightColor = UIColor.red
let attributedString = text.highlight(keyWords: keyWords, highlightColor: highlightColor)
```

### 8.1.3 字符串格式化

```swift
// 手机号格式化 (例如: 138 1234 5678)
let phoneNumber = "13812345678"
let formattedPhone = phoneNumber.phoneNumParseString()

// 提取最长数字
let mixedString = "abc123def4567"
if let longestNumber = mixedString.longestNumber() {
    print(longestNumber)  // "4567"
}

// 字符串转Double数组
let arrayString = "[1738339200,80000,1738723122,100000]"
if let doubleArray = arrayString.toDoubleArray() {
    print(doubleArray)  // [1738339200.0, 80000.0, 1738723122.0, 100000.0]
}
```

## 8.2 日期处理

AICoin-iOS项目使用Date扩展和SwiftDate库处理日期和时间相关操作。

### 8.2.1 时间戳操作

```swift
// 获取当前时间戳
let timestamp = Date.getNowTimeStamp()  // 返回10位时间戳

// 获取当前时间字符串
let currentTimeString = Date.getNowTimeString(dateFormat: "yyyy-MM-dd HH:mm:ss")

// 时间戳转换为时间字符串
let dateString = Date.getTimeString(timeStamp: 1672531200, dateFormat: "yyyy-MM-dd")  // "2023-01-01"
```

### 8.2.2 日期转换

```swift
// 日期字符串转Date对象
let date = Date.getDate(timeString: "2023-01-01 12:00:00", dateFormat: "yyyy-MM-dd HH:mm:ss")

// 日期字符串转时间戳
let timestamp = Date.getTimeStamp(timeString: "2023-01-01 12:00:00", dateFormat: "yyyy-MM-dd HH:mm:ss")

// Date对象转字符串
let dateString = date.getStringTime(dateFormat: "yyyy年MM月dd日")
```

### 8.2.3 相对时间显示

```swift
// 相对日期显示（今天、昨天、日期）
let relativeDate = Date.relativeDateString(from: 1672531200)  // 根据日期返回"今天"、"昨天"或具体日期

// 转换为"刚刚"、"x分钟前"、"x小时前"等格式
let relativeTimeString = Date.convertTimeToString(1672531200)
```

### 8.2.4 日期组件获取

```swift
// 获取日期的年、月、日、时、分、秒
let date = Date()
let (year, month, day, hour, minute, second) = date.getTime()
```

### 8.2.5 日期计算

```swift
// 计算两个日期之间的天数
let days = TickerDataFormatHelper.calenderTime(endTime: 1672531200)
```

## 8.3 数字格式化

AICoin-iOS项目提供了多种数字格式化工具，用于处理价格、百分比等数值的显示。

### 8.3.1 基础数字格式化

```swift
// 保留指定小数位
let formattedNumber = TickerDataFormatHelper.transformStr(str: "123.456789", num: 2)  // "123.46"

// 添加千位分隔符
let numberWithSeparator = TickerDataFormatHelper.requestNumFormatter(data: "1234567.89")  // "1,234,567.89"

// 格式化带正负号的数字
let signedNumber = TickerMethod.SignNumber(10.5)  // "+10.5"
```

### 8.3.2 特定场景的数字格式化

```swift
// 格式化百分比
let percentString = TickerDataFormatHelper.setRate(str: "0.1256")  // "12.56%"

// 格式化价格（根据数值大小自动调整小数位数）
let priceString = TickerMethod.formatWithUnit(value: "1234.56", unit: true)
// 返回格式化后的价格和单位，例如 (value: "1.23", unit: "K")

// 全球指数价格格式化
let globalPrice = TickerDataFormatHelper.requsetGlobalPrice(price: "12.3456")  // "12.35"
```

### 8.3.3 使用NumberFormatter

AICoin-iOS项目使用AICCacheManager缓存NumberFormatter实例，提高性能：

```swift
// 获取缓存的NumberFormatter
let formatter = AICCacheManager.share.numberFormatter(withId: "myFormatter") { formatter in
    formatter.numberStyle = .decimal
    formatter.roundingMode = .halfEven
    formatter.minimumFractionDigits = 2
    formatter.maximumFractionDigits = 2
    formatter.usesGroupingSeparator = true
}

// 使用formatter格式化数字
let formattedString = formatter.string(for: Decimal(string: "1234.56"))  // "1,234.56"
```

### 8.3.4 数值舍入和精度控制

```swift
// 四舍五入到指定小数位
let roundedValue = 0.123456789.rounded(toPlaces: 4)  // 0.1235

// 四舍五入并返回固定小数位的字符串
let roundedString = 0.123456789.roundedString(toPlaces: 4)  // "0.1235"
```

## 8.4 定时器工具

AICoin-iOS项目提供了TimerHelper类用于管理定时器：

```swift
// 启动定时器（使用selector回调）
TimerHelper.shareInstance.startTimerWithIdentifier("updatePrice", 
                                                 interval: 3.0, 
                                                 target: self, 
                                                 selector: #selector(updatePrice))

// 启动定时器（使用闭包回调）
TimerHelper.shareInstance.startTimer(with: "updateChart", 
                                   interval: 5.0) { [weak self] in
    self?.updateChart()
}

// 停止定时器
TimerHelper.shareInstance.stopTimerWithIdentifier("updatePrice")

// 暂停定时器
TimerHelper.shareInstance.pause(identifier: "updateChart")

// 恢复定时器
TimerHelper.shareInstance.resume(identifier: "updateChart")

// 停止所有定时器
TimerHelper.shareInstance.stopAllTimers()
```

以上是AICoin-iOS项目中常用的字符串处理、日期处理和数字格式化工具类的使用方法。这些工具类可以帮助开发者更高效地处理各种数据格式化需求。
