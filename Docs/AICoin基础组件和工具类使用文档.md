# AICoin-iOS 基础组件和工具类使用文档

## 1. 概述

### 1.1 项目架构简介

AICoin-iOS是一款专注于数字货币行情、资讯和交易的移动应用，采用模块化架构设计，主要由以下几个核心模块组成：

- **基础模块（Base）**：提供基础UI组件、网络请求、数据存储、工具类等底层支持
- **首页模块（HomePage）**：展示市场热门币种和概览，提供快速入口和推荐内容
- **行情模块（Ticker）**：提供各种数字货币的实时行情、K线图表和市场数据
- **自选模块**：用户自定义关注的数字货币列表和分组管理
- **资讯内容模块（Content）**：提供新闻、快讯、动态等内容服务
- **个人中心模块（Me）**：用户账户管理、设置和个性化功能
- **K线图表模块（CandleChart）**：专业的K线图表展示和分析工具
- **登录模块（Login）**：用户认证和账户管理
- **交易模块（Trade）**：数字货币交易功能

项目采用混合开发模式，主要使用Swift和Objective-C两种语言，并通过桥接文件（AICoin-Swift.h）实现两种语言的互操作。项目整体遵循MVC架构模式，同时在部分模块中引入MVVM设计模式提高代码可维护性。

### 1.2 文档目的和使用方法

本文档旨在帮助开发人员快速了解和使用AICoin-iOS项目中的基础组件和工具类，提高开发效率，保持代码风格一致性，减少重复开发工作。

**使用方法**：

1. **快速查找**：文档按功能模块分类，开发者可根据需求快速定位到相应章节
2. **代码示例**：每个组件和工具类都提供了实际使用示例，可直接复制使用
3. **最佳实践**：遵循文档中推荐的最佳实践，确保代码质量和性能
4. **版本兼容**：注意查看组件的版本兼容性说明，确保在不同iOS版本中正常工作

**适用人群**：

- 新加入项目的iOS开发人员
- 需要使用特定组件的现有团队成员
- 进行代码审查的技术负责人

### 1.3 基础组件和工具类的分类

AICoin-iOS项目中的基础组件和工具类可分为以下几大类：

#### 1.3.1 基础UI组件

提供统一的视觉风格和交互体验，包括：
- 基础控制器（AICBaseViewController等）
- 基础视图（AICBaseTableView、AICBaseLabel等）
- 导航组件（AICBaseNavigationBar、AICBaseNavigationController）
- 弹窗组件（AICAlertViewController、AICPopoverViewController等）

#### 1.3.2 网络组件

处理网络请求、数据解析和缓存，包括：
- HTTP请求（AICHttpManager、AICBaseHttpManager）
- WebSocket通信（WebSocketHelper、TickerSubscribeDataManager）
- 网络缓存（AICNetworkCache）
- 数据模型（AICBaseResponseModel）

#### 1.3.3 数据存储

管理本地数据持久化，包括：
- WCDB数据库操作（支持一句代码取出数据并组合为对象，支持语言集成查询和多线程高并发）
- UserDefaults封装
- 缓存管理（AICCacheManager）
- 序列化工具
  - SwiftyJSON（需指定映射关系及数据类型）
  - YYModel（利用OC特性，Swift类需声明@objcMembers）
  - DataDecodable（继承Swift 4.2 Decodable协议，不支持继承类）

#### 1.3.4 主题管理

实现应用的主题切换和样式管理，包括：
- 主题管理器（SSBThemeManager）
- 颜色管理（BaseThemeProtocol）
- 图片资源管理（模块化资源管理）
- 日间/夜间模式切换（全局通知刷新）

#### 1.3.5 工具类

提供各种辅助功能，包括：
- 字符串处理（String+Extension）
- 日期处理
- 数字格式化
- 图片处理（UIImage+AICTools）
- 视图扩展（UIView+AICTools）
- 线程管理（AICSwiftTool）
- 常量定义（AICGlobalConstant）

#### 1.3.6 布局工具

辅助界面布局，包括：
- Masonry扩展（Masonry+AICTools，仅用于OC旧UI）
- SnapKit扩展（UIView+SnpAddition，推荐使用）
- Bees布局（运算符重载，简洁但可能不再维护）
- 安全区域适配
- 空视图（DZNEmptyDataSet）

#### 1.3.7 推送与通知

处理应用内和远程通知，包括：
- 本地通知
- 远程推送
- WebSocket实时通知

#### 1.3.8 实用功能

提供特定业务场景的功能封装，包括：
- 分享功能（ShareSDK+AICUI）
- 用户管理（UserManager）
- 安全模式
- 多语言支持（SystemConfig，支持简中、繁中和英文）
- 弹窗组件（AICBaseAlertController）
- 下拉刷新（AICRefreshNormalHeader、AICRefreshAutoNormalFooter）
- 图片加载（YYWebImageManager）
- 数据绑定
  - ReactiveObjC（响应式编程，信号处理）
  - ObservableValue（属性观察者模式）

通过合理使用这些基础组件和工具类，可以显著提高开发效率，保持代码风格一致，并确保应用的稳定性和可维护性。

## 2. 主题管理

### 2.1 概述

AICoin应用支持白天和黑夜两种主题模式，通过`SSBThemeManager`进行统一管理。主题切换时，通过全局通知机制刷新整个应用界面。需要注意的是，K线图表模块有独立的主题管理，可以不跟随应用主题设置。

主题管理包括颜色和图片资源两部分，每个模块各自管理自己的主题资源：
- 颜色：通过继承`BaseThemeProtocol`的类进行配置
- 图片：在模块下的Assets.xcassets中对应的白天（BaseDay）和黑夜（BaseNight）文件夹配置，资源名称需保持一致

### 2.2 使用方法

#### 2.2.1 获取主题图片

```swift
// 获取模块主题图片
let image = UIImage.模块.image(name: "图片名称")

// 示例：获取基础模块的图片
let backImage = UIImage.base.image(name: "nav_back")
```

#### 2.2.2 获取主题颜色

```swift
// 获取模块主题颜色
let color = UIColor.模块.current.颜色属性

// 示例：获取基础模块的背景色
let backgroundColor = UIColor.base.current.backgroundColor
```

#### 2.2.3 主题切换

主题切换通过`SSBThemeManager`实现：

```swift
// 切换主题
SSBThemeManager.shared().changeTheme(at: view) {
    // 主题切换完成后的回调
}
```

### 2.3 自定义主题颜色

要为模块添加自定义主题颜色，需要：

1. 创建遵循`BaseThemeProtocol`协议的主题类
2. 实现日间和夜间两种主题颜色

```swift
// 定义协议
protocol ModuleThemeColorProtocol {
    var backgroundColor: UIColor { get }
    var textColor: UIColor { get }
    // 其他颜色...
}

// 日间主题实现
class ModuleDayTheme: ModuleThemeColorProtocol {
    var backgroundColor: UIColor { return UIColor(hex: "#FFFFFF") }
    var textColor: UIColor { return UIColor(hex: "#333333") }
}

// 夜间主题实现
class ModuleNightTheme: ModuleThemeColorProtocol {
    var backgroundColor: UIColor { return UIColor(hex: "#121212") }
    var textColor: UIColor { return UIColor(hex: "#EEEEEE") }
}

// 主题管理类
class ModuleTheme: NSObject {
    let day = ModuleDayTheme()
    let night = ModuleNightTheme()

    // 获取当前主题
    var current: ModuleThemeColorProtocol {
        if SSBThemeManager.shared().currentThemeName == .night {
            return self.night
        }
        return self.day
    }
}

// 扩展UIColor
extension UIColor {
    static let module = ModuleTheme()
}
```

## 3. 多语言支持

### 3.1 概述

AICoin应用支持简体中文、繁体中文和英文三种语言，由`InternalToolKit`中的`SystemConfig`类管理语言状态。当切换语言时，会替换应用的`rootViewController`为新的`AICTabbarController`实例，以完成整个应用的语言切换。

各模块的语言文案在对应模块的strings文件中配置，通过扩展的本地化方法获取对应语言的文案。

### 3.2 使用方法

#### 3.2.1 获取本地化文本

```swift
// 基本用法
let localizedString = "文本".模块.localized

// 示例：获取基础模块的本地化文本
let cancelText = "取消".base.localized
```

#### 3.2.2 语言切换

语言切换通过`SystemConfig`实现：

```swift
// 切换到简体中文
SystemConfig.setUserInterfaceLanguage(.simplifiedChinese)

// 切换到繁体中文
SystemConfig.setUserInterfaceLanguage(.traditionalChinese)

// 切换到英文
SystemConfig.setUserInterfaceLanguage(.english)
```

#### 3.2.3 获取当前语言

```swift
// 判断是否为中文界面
let isChinese = SystemConfig.isChineseUserInterface()

// 获取当前语言类型
let currentLanguage = SystemConfig.userInterfaceLanguage()
```

### 3.3 添加新的本地化文本

1. 在对应模块的Localizable.strings文件中添加新的键值对：

```
// 简体中文 (zh-Hans.lproj/Localizable.strings)
"hello_world" = "你好，世界";

// 繁体中文 (zh-Hant.lproj/Localizable.strings)
"hello_world" = "你好，世界";

// 英文 (en.lproj/Localizable.strings)
"hello_world" = "Hello, World";
```

2. 在代码中使用：

```swift
let greeting = "hello_world".base.localized
```

### 3.4 模块化本地化扩展

为新模块添加本地化支持：

```swift
// 定义协议
protocol ModuleStringProtocol {
    var localized: String { get }
}

// 实现类
class ModuleString: ModuleStringProtocol {
    let str: String

    init(str: String) {
        self.str = str
    }

    var localized: String {
        return NSLocalizedString(self.str, tableName: "ModuleLocalizable", bundle: Bundle.main, value: "", comment: "")
    }
}

// 扩展String
extension String {
    var module: ModuleStringProtocol {
        return ModuleString(str: self)
    }
}
```

## 4. 布局与UI组件

### 4.1 布局工具

AICoin项目不再使用Storyboard或XIB进行UI开发，而是采用代码方式实现自动布局，以适配不同设备。项目中使用了多种自动布局库，各有特点：

#### 4.1.1 SnapKit（推荐）

SnapKit是项目中推荐使用的布局库，语法简洁，易于维护：

```swift
// 基本用法
view.snp.makeConstraints { make in
    make.top.equalTo(superview.snp.top).offset(10)
    make.left.right.equalToSuperview()
    make.height.equalTo(44)
}

// 安全区域适配
view.snp.makeConstraints { make in
    make.top.equalTo(view.snp_safeTop)
    make.bottom.equalTo(view.snp_safeBottom)
    make.left.equalTo(view.snp_safeLeading)
    make.right.equalTo(view.snp_safeTrailing)
}
```

#### 4.1.2 Masonry

Masonry主要用于Objective-C编写的旧UI组件：

```objc
// 基本用法
[view mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(superview.mas_top).offset(10);
    make.left.right.equalTo(superview);
    make.height.equalTo(@44);
}];

// 安全区域适配
[view mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(view.aic_safeTop);
    make.bottom.equalTo(view.aic_safeBottom);
    make.left.right.equalTo(superview);
}];
```

#### 4.1.3 Bees

Bees使用运算符重载实现更简洁的布局语法，但可能不再维护：

```swift
// 基本用法
view.bee.layout {
    $0.top == superview.top + 10
    $0.left == superview.left
    $0.right == superview.right
    $0.height == 44
}
```

### 4.2 空视图

项目集成了DZNEmptyDataSet来处理空数据状态的视图展示：

```swift
// AICTableView默认使用BaseEmptyDelegateModel作为空视图模型
tableView.emptyDataSetSource = self
tableView.emptyDataSetDelegate = self

// 自定义空视图
func title(forEmptyDataSet scrollView: UIScrollView!) -> NSAttributedString! {
    return NSAttributedString(string: "暂无数据", attributes: [
        .font: UIFont.systemFont(ofSize: 16),
        .foregroundColor: UIColor.gray
    ])
}

func image(forEmptyDataSet scrollView: UIScrollView!) -> UIImage! {
    return UIImage(named: "empty_placeholder")
}
```

### 4.3 弹窗组件

项目使用`AICBaseAlertController`实现自定义弹窗，不同于系统的`UIAlertController`，它不会创建新的window：

```swift
// 创建自定义弹窗
let alertVC = AICBaseAlertController()
alertVC.contentView = yourCustomView
alertVC.modalPresentationStyle = .overFullScreen
alertVC.modalTransitionStyle = .crossDissolve

// 显示弹窗
present(alertVC, animated: true, completion: nil)
```

### 4.4 下拉刷新组件

项目对MJRefresh进行了封装，提供了`AICRefreshNormalHeader`和`AICRefreshAutoNormalFooter`：

```swift
// 添加下拉刷新
tableView.mj_header = AICRefreshNormalHeader(refreshingBlock: { [weak self] in
    // 刷新数据
    self?.loadData()
})

// 添加上拉加载更多
tableView.mj_footer = AICRefreshAutoNormalFooter(refreshingBlock: { [weak self] in
    // 加载更多数据
    self?.loadMoreData()
})

// 结束刷新
tableView.mj_header?.endRefreshing()
tableView.mj_footer?.endRefreshing()
```

### 4.5 图片加载

项目使用`YYWebImageManager`进行异步图片加载和缓存：

```swift
// 基本用法
imageView.aic_setImage(with: URL(string: urlString))

// 带占位图
imageView.aic_setImage(with: URL(string: urlString), placeholder: UIImage(named: "placeholder"))

// 带回调
imageView.aic_setImage(with: URL(string: urlString), placeholder: nil, options: [], completion: { (image, url, _, _, error) in
    // 处理加载完成后的逻辑
})
```

## 5. 数据存储与序列化

### 5.1 WCDB数据库

项目使用WCDBSwift进行数据持久化，支持一句代码取出数据并组合为对象，支持语言集成查询和多线程高并发。

#### 5.1.1 定义数据模型

```swift
import WCDBSwift

// 定义数据库模型
class MessageModel: TableCodable {
    var messageID: Int = 0
    var content: String = ""
    var timestamp: Int = 0

    // 定义表结构
    enum CodingKeys: String, CodingTableKey {
        typealias Root = MessageModel
        static let objectRelationalMapping = TableBinding(CodingKeys.self)

        case messageID
        case content
        case timestamp

        // 定义主键
        static var columnConstraintBindings: [CodingKeys: ColumnConstraintBinding]? {
            return [
                messageID: ColumnConstraintBinding(isPrimary: true)
            ]
        }
    }
}
```

#### 5.1.2 数据库操作

```swift
// 创建/打开数据库
let dbPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first! + "/database.db"
let database = Database(withPath: dbPath)

// 创建表
try database.create(table: "messages", of: MessageModel.self)

// 插入数据
let message = MessageModel()
message.messageID = 1
message.content = "Hello WCDB"
message.timestamp = Int(Date().timeIntervalSince1970)
try database.insert(objects: message, intoTable: "messages")

// 查询数据
let messages: [MessageModel] = try database.getObjects(fromTable: "messages", where: MessageModel.Properties.messageID > 0)

// 更新数据
try database.update(table: "messages", on: MessageModel.Properties.content, with: message, where: MessageModel.Properties.messageID == 1)

// 删除数据
try database.delete(fromTable: "messages", where: MessageModel.Properties.messageID == 1)
```

### 5.2 序列化工具

项目中使用了三种不同的序列化方案，各有优缺点：

#### 5.2.1 SwiftyJSON

需要指定映射关系及数据类型，实现比较啰嗦：

```swift
// 解析JSON
let json = JSON(data)
if json["success"].boolValue {
    let id = json["data"]["id"].intValue
    let name = json["data"]["name"].stringValue
    let user = UserModel(id: id, name: name)
}
```

#### 5.2.2 YYModel

利用OC的特性进行自动映射，Swift类需声明@objcMembers：

```swift
// 定义模型
@objcMembers
class UserModel: NSObject {
    var id: Int = 0
    var name: String = ""
}

// 解析JSON
let dict = try JSONSerialization.jsonObject(with: data) as? [String: Any]
let user = UserModel.model(withJSON: dict?["data"])
```

#### 5.2.3 DataDecodable

继承Swift 4.2 Decodable协议，完善类型转换，Swift类方便调用，但不支持继承的类：

```swift
// 定义模型
struct UserModel: DataDecodable {
    let id: Int
    let name: String

    enum CodingKeys: String, CodingKey {
        case id
        case name
    }
}

// 解析JSON
if let responseModel = try? JSONDecoder().decode(ResponseModel<UserModel>.self, from: data) {
    let user = responseModel.data
}
```

### 5.3 数据绑定

项目提供了两种数据绑定方案：

#### 5.3.1 ReactiveObjC

响应式编程框架，使用信号（RACSignal）处理变量变化和传递：

```swift
// 创建信号
let signal = RACSignal.createSignal { subscriber in
    // 发送信号
    subscriber.sendNext("Hello RAC")
    subscriber.sendCompleted()
    return RACDisposable.empty()
}

// 订阅信号
signal.subscribeNext { value in
    print("Received: \(value)")
}
```

#### 5.3.2 ObservableValue

通过属性观察方法通知所有观察者数据变化：

```swift
// 定义可观察属性
@Observable
var value: Double = 0

// 订阅属性变化
$value.subscribe { [weak self] newValue in
    // 处理属性变化
    self?.updateUI(with: newValue)
}.disposed(by: DisposeBag())

// 更新属性值
value = 42.0  // 会自动触发订阅者的回调
```

## 6. 网络组件

### 6.1 HTTP请求

项目的网络请求主要通过`AICHttpManager`和`AICBaseHttpManager`进行管理，它们基于AFNetworking进行封装，提供了统一的网络请求接口。

#### 6.1.1 基本请求

```swift
// 基本POST请求
AICHttpManager.shared.post("/api/v1/endpoint", parameters: params, progress: nil, success: { (task, response) in
    // 处理成功响应
    if let json = JSON(response) {
        // 处理JSON数据
    }
}, failure: { (task, error) in
    // 处理错误
})

// 基本GET请求
AICHttpManager.shared.get("/api/v1/endpoint", parameters: params, progress: nil, success: { (task, response) in
    // 处理成功响应
}, failure: { (task, error) in
    // 处理错误
})
```

#### 6.1.2 使用AICBaseHttpManager

`AICBaseHttpManager`在`AICHttpManager`基础上提供了更便捷的方法和标准响应模型封装：

```swift
// 使用AICBaseHttpManager发起请求
AICBaseHttpManager.post("user/profile", parameters: ["userid": UserManager.share.safeUserID]) { (response) in
    if response.success, let data = response.data {
        // 处理成功响应数据
        let userProfile = UserProfileModel(from: data)
    } else {
        // 处理错误
        print(response.error?.localizedDescription ?? "Unknown error")
    }
}

// 在子线程回调中处理响应
AICBaseHttpManager.postAtChildThread("heavy/operation", parameters: params) { (response) in
    // 在后台线程处理数据
    let processedData = self.processData(response.data)

    // 切回主线程更新UI
    AICSwiftTool.runonMainQueue {
        self.updateUI(with: processedData)
    }
}
```

#### 6.1.3 文件上传

```swift
// 上传文件
AICHttpManager.shared.post("upload/image", parameters: params, constructingBodyWithBlock: { (formData) in
    formData.appendPart(withFileData: imageData, name: "file", fileName: "image.jpg", mimeType: "image/jpeg")
}, progress: { (progress) in
    // 更新上传进度
    print("Upload progress: \(progress.fractionCompleted)")
}, success: { (task, response) in
    // 处理上传成功响应
}, failure: { (task, error) in
    // 处理上传失败
})
```

### 6.2 WebSocket通信

项目使用`WebSocketHelper`和`TickerSubscribeDataManager`进行WebSocket通信，主要用于实时行情数据的获取。

#### 6.2.1 基本使用

```swift
// 创建WebSocket连接
let wsHelper = WebSocketHelper(host: "wss://api.aicoin.com/ws")
wsHelper.add(observer: self)
wsHelper.createWebSocket()

// 实现WebSocketHelperProtocol协议
func webSocketHelperDidOpen(_ helper: WebSocketHelper) {
    // WebSocket连接已打开，可以发送订阅指令
    helper.sendData("{\"type\":\"subscribe\",\"channel\":\"ticker\"}")
}

func webSocketHelperDidReceiveData(_ helper: WebSocketHelper, text: String) {
    // 处理接收到的数据
    if let data = text.data(using: .utf8),
       let json = try? JSON(data: data) {
        // 处理JSON数据
    }
}

func webSocketHelperDidClose(_ helper: WebSocketHelper, error: Error?) {
    // 处理连接关闭
    if let error = error {
        print("WebSocket closed with error: \(error.localizedDescription)")
    }
}
```

#### 6.2.2 行情数据订阅

使用`TickerSubscribeDataManager`进行行情数据订阅：

```swift
// 获取单例
let manager = TickerSubscribeDataManager.shared

// 订阅单个市场
let marketModel = TickerMarketListModel()
marketModel.marketListKey = "binance_btc_usdt"
let observation = manager.sub(market: marketModel)

// 订阅多个市场
let observations = manager.sub(markets: marketModels)

// 订阅深度数据
let depthObservation = manager.sub(depth: "binance_btc_usdt") { (depthModel) in
    // 处理深度数据更新
    self.updateDepthView(with: depthModel)
}

// 订阅成交数据
let tradesObservation = manager.sub(trades: "binance_btc_usdt") { (tradesModel) in
    // 处理成交数据更新
    self.updateTradesView(with: tradesModel)
}

// 持有订阅对象，当不再需要时可以释放
// 当订阅对象被释放时，会自动取消订阅
```

### 6.3 网络缓存

项目使用`AICNetworkCache`进行网络响应数据的缓存：

```swift
// 缓存数据
AICNetworkCache.shared.cache(key: cacheKey, value: model, expireSeconds: 60 * 5)  // 缓存5分钟

// 获取缓存数据
if let cachedModel: UserModel = AICNetworkCache.shared.object(forKey: cacheKey) {
    // 使用缓存数据
    self.updateUI(with: cachedModel)
}

// 清除缓存
AICNetworkCache.shared.removeObject(forKey: cacheKey)
AICNetworkCache.shared.removeAllObjects()  // 清除所有缓存
```

## 7. 最佳实践

### 7.1 组件选择建议

在AICoin项目开发中，推荐遵循以下组件选择原则：

1. **布局工具**：优先使用SnapKit进行布局，不再使用Storyboard或XIB
2. **网络请求**：使用AICBaseHttpManager进行标准API请求，处理复杂逻辑时使用postAtChildThread方法
3. **数据模型**：
   - 纯Swift类优先使用DataDecodable
   - 需要与OC交互的类使用YYModel并添加@objcMembers
   - 处理复杂或不确定结构的JSON时使用SwiftyJSON
4. **数据存储**：使用WCDB进行数据持久化，简单配置使用UserDefaults
5. **主题适配**：所有UI组件必须支持主题切换，遵循模块化的主题管理方式
6. **多语言**：所有用户可见文本必须使用本地化方法，不要硬编码

### 7.2 性能优化建议

1. **网络优化**：
   - 合理使用网络缓存减少请求次数
   - 大量数据处理使用子线程回调
   - WebSocket连接状态管理，避免频繁重连

2. **UI性能**：
   - 使用复用机制（UITableView/UICollectionView）
   - 避免主线程进行耗时操作
   - 图片异步加载并缓存

3. **内存管理**：
   - 正确使用weak/strong引用避免循环引用
   - 大型资源及时释放
   - 使用autoreleasepool处理临时大量对象

### 7.3 代码规范

1. **命名规范**：
   - 类名：使用大驼峰命名法（如AICBaseViewController）
   - 方法名：使用小驼峰命名法（如loadData()）
   - 常量：使用k前缀（如kHomePageSubviewsShouldUpdate）

2. **注释规范**：
   - 公开API必须添加文档注释（使用///格式）
   - 复杂逻辑添加实现注释（使用//格式）
   - 临时代码使用TODO/FIXME标记

3. **代码组织**：
   - 按功能模块组织代码
   - 相关功能放在同一个扩展中
   - 公开API在前，私有方法在后

### 7.4 常见问题解决方案

1. **主题切换后UI未更新**：
   - 检查是否实现了主题切换的通知处理
   - 确保所有颜色和图片都使用主题方法获取

2. **内存泄漏**：
   - 使用Instruments检测内存泄漏
   - 检查闭包中的self是否使用weak引用
   - 检查delegate是否使用weak声明

3. **网络请求失败**：
   - 检查网络连接状态
   - 验证API参数格式
   - 查看服务器返回的错误信息

4. **UI适配问题**：
   - 使用自动布局而非固定尺寸
   - 适配不同设备使用safeArea相关API
   - 横竖屏切换时重新布局

### 7.5 新功能开发流程

开发新功能时，建议遵循以下流程：

1. **规划阶段**：
   - 明确功能需求和交互流程
   - 确定使用的组件和数据模型
   - 设计API接口和数据结构

2. **开发阶段**：
   - 创建数据模型和网络请求
   - 实现UI界面和交互逻辑
   - 添加主题和多语言支持

3. **测试阶段**：
   - 单元测试核心功能
   - UI测试交互流程
   - 性能测试和内存检测

4. **优化阶段**：
   - 代码重构和优化
   - 处理边缘情况
   - 完善错误处理和用户提示
