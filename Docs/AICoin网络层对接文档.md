# AICoin iOS 网络层对接文档

## 一、网络层架构概述

AICoin iOS应用采用多层网络架构，主要由以下几个核心组件组成：

1. **AICHttpManager** - 核心底层网络请求管理器，基于AFNetworking封装，位于Pods中
2. **AICNetworkCache** - 网络缓存管理器，用于缓存API响应数据

这些组件共同构成了一个功能丰富、高效可靠的网络层，支持不同业务模块的网络需求。

## 二、核心网络组件详解

### 1. AICHttpManager

`AICHttpManager`是应用网络请求的核心底层组件，基于AFNetworking封装，提供了统一的网络请求接口。该组件的具体实现被封装在Pods中，项目大部分接口请求都直接使用`AICHttpManager.shared.post`等方法来发起请求。

#### 1.1 主要功能

- 网络请求的发送与响应处理
- 加密与签名验证
- 网络状态监控
- 请求参数预处理
- 全局错误处理

#### 1.2 请求方法

```objc
// 基本POST请求
[AICHttpManager.shared POST:@"/api/v1/endpoint"
    parameters:params
    progress:nil
    success:^(NSURLSessionDataTask *task, id responseObject) {
        // 处理成功响应
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        // 处理错误
    }];
```

Swift调用示例：
```swift
// 基本POST请求
AICHttpManager.shared.post("/api/v1/endpoint", parameters: params, progress: nil, success: { (task, response) in
    // 处理成功响应
}, failure: { (task, error) in
    // 处理错误
})
```
#### 1.3 网络状态监测

AICHttpManager提供了网络状态监测功能：

```swift
// 检查网络是否可用
if AICHttpManager.shared.isReachable {
    // 网络可用，执行网络请求
} else {
    // 网络不可用，提示用户
}

// 监听网络状态变化
NotificationCenter.default.addObserver(self,
                                       selector: #selector(networkingChanged),
                                       name: .AICNetworkingChangedInNotice,
                                       object: nil)
```

#### 1.4 异步/等待(async/await)支持

Swift 5.5+支持使用async/await语法：

```swift
// 使用async/await (Swift 5.5+)
Task {
    let (task, response) = await AICHttpManager.asyncPost("/api/v1/endpoint", parameters: params)
    let json = JSON(response ?? "")
    if json["success"].boolValue {
        // 处理成功响应
    }
}
```

### 2. AICNetworkCache

`AICNetworkCache`是一个专门用于缓存网络响应数据的工具类，可以减少重复请求，提高应用性能。

#### 2.1 主要功能

- 内存缓存 - 使用NSCache实现高效的内存缓存
- 磁盘缓存 - 将数据缓存到本地文件系统
- 自动过期 - 支持设置缓存过期时间
- 内存警告处理 - 在系统内存不足时自动清理缓存

#### 2.2 使用方法

```swift
// 获取缓存实例
let cache = AICNetworkCache.shared

// 缓存数据到内存
cache.cache(key: "api/v1/ticker", value: responseData, expireSeconds: 60)

// 从内存获取缓存
if let cachedData: Data = cache.get(for: "api/v1/ticker") {
    // 使用缓存数据
}

// 缓存数据到磁盘
cache.cacheToDisk(key: "api/v1/ticker", value: responseModel, expireSeconds: 300)

// 从磁盘获取缓存
if let cachedModel: ResponseModel = cache.getFromDisk(for: "api/v1/ticker") {
    // 使用缓存数据
}

// 清除特定缓存
cache.removeCache(for: "api/v1/ticker")

// 清除所有缓存
cache.removeAllCache()

// 清除磁盘缓存
cache.clearDiskCache()

// 清除过期的磁盘缓存
cache.clearExpiredDiskCache()
```

## 三、网络请求流程

### 1. 标准请求流程

1. 创建请求参数
2. 调用对应的网络请求方法
3. 等待响应回调
4. 解析响应数据
5. 处理业务逻辑

```swift
// 示例：获取币种行情
func requestTickerData(tickerId: String, completion: @escaping (TickerModel?, Error?) -> Void) {
    let params = ["id": tickerId]

    AICHttpManager.shared.post("/api/v1/ticker", parameters: params, progress: nil, success: { [weak self] task, response in
        guard let self = self else { return }
        let json = JSON(response ?? "")
        if json["success"].boolValue {
            let data = json["data"]
            let model = TickerModel.model(withJSON: data)
            completion(model, nil)
        } else {
            completion(nil, NSError(domain: "APIError", code: json["errorCode"].intValue, userInfo: [NSLocalizedDescriptionKey: json["error"].stringValue]))
        }
    }, failure: { task, error in
        completion(nil, error)
    })
}
```

### 2. 缓存请求流程

1. 检查内存/磁盘缓存
2. 如果缓存有效，使用缓存数据
3. 如果缓存无效或不存在，发起网络请求
4. 接收响应后更新缓存

```swift
// 示例：带缓存的行情请求
func requestTickerDataWithCache(tickerId: String, completion: @escaping (TickerModel?, Error?) -> Void) {
    let cacheKey = "ticker_\(tickerId)"

    // 检查缓存
    if let cachedModel: TickerModel = AICNetworkCache.shared.get(for: cacheKey) {
        completion(cachedModel, nil)
        return
    }

    // 缓存不存在，发起网络请求
    requestTickerData(tickerId: tickerId) { (model, error) in
        if let model = model {
            // 更新缓存
            AICNetworkCache.shared.cache(key: cacheKey, value: model, expireSeconds: 60)
        }
        completion(model, error)
    }
}
```


## 四、最佳实践

### 1. 网络层封装

建议为每个业务模块创建专门的网络请求管理类，封装该模块的所有API调用：

```swift
// 示例：行情模块网络管理器
class TickerNetworkManager {
    static let shared = TickerNetworkManager()

    // 获取行情列表
    func requestTickerList(completion: @escaping ([TickerModel]?, Error?) -> Void) {
        AICHttpManager.shared.post("/api/v1/ticker/list", parameters: nil, progress: nil, success: { task, response in
            let json = JSON(response ?? "")
            if json["success"].boolValue {
                let dataArray = json["data"].arrayValue
                let models = dataArray.compactMap { TickerModel.model(withJSON: $0) }
                completion(models, nil)
            } else {
                completion(nil, NSError(domain: "APIError", code: json["errorCode"].intValue, userInfo: nil))
            }
        }, failure: { task, error in
            completion(nil, error)
        })
    }

    // 获取K线数据
    func requestKLineData(tickerId: String, period: String, completion: @escaping ([KLineModel]?, Error?) -> Void) {
        // 实现请求逻辑
    }
}
```

### 2. 取消请求

在视图控制器即将消失时，应该取消尚未完成的网络请求：

```swift
class TickerViewController: AICBaseViewController {
    var dataTask: URLSessionDataTask?

    func requestData() {
        // 取消之前的请求
        dataTask?.cancel()

        // 发起新的请求
        dataTask = AICHttpManager.shared.post("/api/v1/ticker", parameters: nil, progress: nil, success: { [weak self] task, response in
            guard let self = self else { return }
            // 处理响应
        }, failure: { [weak self] task, error in
            guard let self = self else { return }
            // 处理错误
        })
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)

        // 取消未完成的请求
        dataTask?.cancel()
    }
}
```

### 3. 错误重试

对于重要请求，可以实现自动重试机制：

```swift
func requestWithRetry(retryCount: Int = 3, completion: @escaping (Bool, Any?) -> Void) {
    func attempt(remainingAttempts: Int) {
        AICHttpManager.shared.post("/api/v1/important_data", parameters: nil, progress: nil, success: { task, response in
            let json = JSON(response ?? "")
            completion(json["success"].boolValue, json["data"])
        }, failure: { task, error in
            if remainingAttempts > 0 {
                // 请求失败但还有重试次数，延迟后重试
                DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                    attempt(remainingAttempts: remainingAttempts - 1)
                }
            } else {
                // 无重试次数，返回失败结果
                completion(false, error)
            }
        })
    }

    // 开始第一次尝试
    attempt(remainingAttempts: retryCount)
}
```

### 4. 请求防抖

对于用户输入触发的请求，应实现防抖机制避免频繁请求：

```swift
var searchTask: DispatchWorkItem?

func searchCoins(keyword: String) {
    // 取消之前的延迟搜索任务
    searchTask?.cancel()

    // 创建新的延迟搜索任务
    let task = DispatchWorkItem { [weak self] in
        guard let self = self else { return }

        // 发起搜索请求
        AICHttpManager.shared.post("/api/v1/search", parameters: ["keyword": keyword], progress: nil, success: { task, response in
            // 处理搜索结果
            let json = JSON(response ?? "")
            if json["success"].boolValue {
                // 处理搜索结果
            }
        }, failure: { task, error in
            // 处理错误
        })
    }

    // 延迟执行
    searchTask = task
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3, execute: task)
}
```

## 五、WebSocket通信

除了RESTful API外，AICoin还使用WebSocket进行实时数据通信，主要通过`WebSocketHelper`类和`TickerSubscribeDataManager`类实现。

### 1. WebSocket基础连接

```swift
// 获取WebSocket助手实例
let wsHelper = WebSocketHelper(host: "wss://api.aicoin.com/ws")

// 添加观察者
wsHelper.add(observer: self)

// 建立连接
wsHelper.connectWebSocket()

// 发送消息
wsHelper.sendData("{\"type\":\"ping\", \"userid\":\"12345\"}")

// 断开连接
wsHelper.disconnectWebSocket()
```

### 2. 实现观察者协议

```swift
extension MyViewController: WebSocketHelperProtocol {
    // 连接成功
    func webSocketHelperDidOpen(_ helper: WebSocketHelper) {
        print("WebSocket连接已建立")
        // 可以开始订阅数据
    }

    // 收到消息
    func webSocketHelperDidReceiveData(_ helper: WebSocketHelper, text: String) {
        // 处理收到的消息
        if let data = text.data(using: .utf8),
           let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
            print("收到WebSocket消息: \(json)")
        }
    }

    // 连接关闭
    func webSocketHelperDidClose(_ helper: WebSocketHelper, error: Error?) {
        print("WebSocket连接已关闭: \(error?.localizedDescription ?? "未知错误")")
    }
}
```

### 3. 使用TickerSubscribeDataManager订阅行情数据

AICoin使用`TickerSubscribeDataManager`管理WebSocket订阅，它提供了多种订阅方法，是获取实时行情数据的推荐方式。

#### 3.1 订阅单个交易对价格

```swift
// 示例：订阅BTC/USDT在Binance交易所的最新价格
func subscribeBTCUSDT() {
    // 获取交易对模型
    guard let marketModel = AICTickerDataBase.share.fetchMarketListModel(withKey: "binance_btcusdt") else {
        return
    }

    // 订阅单个交易对
    let observation = TickerSubscribeDataManager.shared.sub(market: marketModel)

    // 保持对observation的引用，当不再需要时会自动取消订阅
    self.observation = observation

    // 监听数据更新
    NotificationCenter.default.addObserver(self,
                                          selector: #selector(handleTickerUpdate(_:)),
                                          name: .tickerDataDidUpdate,
                                          object: nil)
}

// 处理价格更新通知
@objc func handleTickerUpdate(_ notification: Notification) {
    if let userInfo = notification.userInfo,
       let marketListKey = userInfo["marketListKey"] as? String,
       marketListKey == "binance_btcusdt" {
        // 从数据池获取最新价格
        if let model = TickerTempDataPool.shared.marketModel(forKey: "binance_btcusdt") {
            let price = model.price
            let change = model.change
            print("BTC/USDT最新价格: \(price), 24h涨跌幅: \(change)%")

            // 更新UI
            DispatchQueue.main.async {
                self.priceLabel.text = price
                self.changeLabel.text = "\(change)%"
                self.changeLabel.textColor = change.doubleValue >= 0 ? .baseCurrentUpColor : .baseCurrentDownColor
            }
        }
    }
}
```

#### 3.2 订阅多个交易对价格

```swift
// 示例：订阅多个交易对的价格
func subscribeMultipleMarkets() {
    // 获取要订阅的交易对列表
    let marketKeys = ["binance_btcusdt", "binance_ethusdt", "okex_btcusdt"]
    let markets = marketKeys.compactMap { AICTickerDataBase.share.fetchMarketListModel(withKey: $0) }

    // 批量订阅
    let observation = TickerSubscribeDataManager.shared.sub(markets: markets)

    // 保持对observation的引用
    self.observation = observation

    // 监听数据更新
    NotificationCenter.default.addObserver(self,
                                          selector: #selector(handleTickerUpdate(_:)),
                                          name: .tickerDataDidUpdate,
                                          object: nil)
}

// 处理多个交易对的价格更新
@objc func handleTickerUpdate(_ notification: Notification) {
    if let userInfo = notification.userInfo,
       let marketListKey = userInfo["marketListKey"] as? String {
        // 检查是否是我们关注的交易对
        if ["binance_btcusdt", "binance_ethusdt", "okex_btcusdt"].contains(marketListKey) {
            // 从数据池获取最新价格
            if let model = TickerTempDataPool.shared.marketModel(forKey: marketListKey) {
                print("\(marketListKey) 最新价格: \(model.price), 24h涨跌幅: \(model.change)%")

                // 更新UI
                DispatchQueue.main.async {
                    self.updateUI(for: marketListKey, with: model)
                }
            }
        }
    }
}
```

#### 3.3 订阅深度数据

```swift
// 示例：订阅BTC/USDT的深度数据
func subscribeDepth() {
    // 订阅深度数据
    let observation = TickerSubscribeDataManager.shared.sub(depth: "binance_btcusdt") { [weak self] (depthModel) in
        // 处理深度数据
        let asks = depthModel.asks // 卖单
        let bids = depthModel.bids // 买单

        print("BTC/USDT深度数据更新:")
        print("卖单数量: \(asks.count)")
        print("买单数量: \(bids.count)")

        // 更新UI
        DispatchQueue.main.async {
            self?.updateDepthUI(asks: asks, bids: bids)
        }
    }

    // 保持对observation的引用
    self.depthObservation = observation
}
```

#### 3.4 订阅成交数据

```swift
// 示例：订阅BTC/USDT的成交数据
func subscribeTrades() {
    // 订阅成交数据
    let observation = TickerSubscribeDataManager.shared.sub(trades: "binance_btcusdt") { [weak self] (tradesModel) in
        // 处理成交数据
        let trades = tradesModel.trades

        print("BTC/USDT最新成交:")
        for trade in trades {
            print("价格: \(trade.price), 数量: \(trade.amount), 方向: \(trade.type == 1 ? "买入" : "卖出")")
        }

        // 更新UI
        DispatchQueue.main.async {
            self?.updateTradesUI(trades: trades)
        }
    }

    // 保持对observation的引用
    self.tradesObservation = observation
}
```

### 4. WebSocket消息格式

#### 4.1 订阅消息格式

订阅单个交易对的WebSocket消息格式：

```json
{
  "type": "sub_single",
  "params": ["binance_btcusdt"]
}
```

订阅多个交易对的WebSocket消息格式：

```json
{
  "type": "sub_single",
  "params": ["binance_btcusdt", "binance_ethusdt", "okex_btcusdt"]
}
```

订阅深度数据的WebSocket消息格式：

```json
{
  "type": "sub_depth",
  "params": ["binance_btcusdt"],
  "level": 10
}
```

订阅成交数据的WebSocket消息格式：

```json
{
  "type": "sub_trades",
  "params": ["binance_btcusdt"]
}
```

#### 4.2 响应数据格式

价格更新的WebSocket响应格式：

```json
{
  "type": "ticker",
  "data": {
    "marketListKey": "binance_btcusdt",
    "price": "45678.12",
    "change": "2.35",
    "high": "46123.45",
    "low": "44789.01",
    "volume": "1234.56",
    "amount": "56789012.34",
    "time": 1634567890123
  }
}
```

深度数据的WebSocket响应格式：

```json
{
  "type": "depth",
  "data": {
    "marketListKey": "binance_btcusdt",
    "asks": [
      ["45680.12", "1.2345"],
      ["45685.67", "0.5678"]
    ],
    "bids": [
      ["45675.43", "2.3456"],
      ["45670.89", "1.7890"]
    ],
    "time": 1634567890123
  }
}
```

成交数据的WebSocket响应格式：

```json
{
  "type": "trades",
  "data": {
    "marketListKey": "binance_btcusdt",
    "trades": [
      {
        "price": "45678.12",
        "amount": "0.1234",
        "type": 1,
        "time": 1634567890123
      },
      {
        "price": "45679.34",
        "amount": "0.0567",
        "type": 2,
        "time": 1634567890456
      }
    ]
  }
}
```

### 5. WebSocket错误处理与重连

```swift
// 处理WebSocket连接错误
func webSocketHelperDidClose(_ helper: WebSocketHelper, error: Error?) {
    print("WebSocket连接已关闭: \(error?.localizedDescription ?? "未知错误")")

    // 可以在这里实现重连逻辑
    DispatchQueue.main.asyncAfter(deadline: .now() + 5) { [weak self] in
        self?.reconnectWebSocket()
    }
}

// 重连WebSocket
func reconnectWebSocket() {
    // TickerSubscribeDataManager会自动处理重连和重新订阅
    // 如果直接使用WebSocketHelper，需要手动重连
    wsHelper.connectWebSocket()
}
```

### 6. 完整示例：实时监控BTC价格

```swift
class BTCPriceViewController: UIViewController {
    @IBOutlet weak var priceLabel: UILabel!
    @IBOutlet weak var changeLabel: UILabel!

    // 保持对订阅对象的引用
    private var observation: TickerSubscribeObservation?

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        subscribeBTCPrice()
    }

    private func setupUI() {
        priceLabel.text = "加载中..."
        changeLabel.text = "--"
    }

    private func subscribeBTCPrice() {
        // 获取BTC/USDT交易对模型
        guard let marketModel = AICTickerDataBase.share.fetchMarketListModel(withKey: "binance_btcusdt") else {
            priceLabel.text = "获取交易对失败"
            return
        }

        // 订阅价格更新
        observation = TickerSubscribeDataManager.shared.sub(market: marketModel)

        // 监听价格更新通知
        NotificationCenter.default.addObserver(self, selector: #selector(handlePriceUpdate(_:)), name: .tickerDataDidUpdate, object: nil)
    }

    @objc private func handlePriceUpdate(_ notification: Notification) {
        if let userInfo = notification.userInfo,
           let marketListKey = userInfo["marketListKey"] as? String,
           marketListKey == "binance_btcusdt" {
            // 从数据池获取最新价格
            if let model = TickerTempDataPool.shared.marketModel(forKey: "binance_btcusdt") {
                // 更新UI
                DispatchQueue.main.async { [weak self] in
                    guard let self = self else { return }
                    self.priceLabel.text = model.price
                    self.changeLabel.text = "\(model.change)%"
                    self.changeLabel.textColor = model.change.doubleValue >= 0 ? .baseCurrentUpColor : .baseCurrentDownColor
                }
            }
        }
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)

        // 移除通知观察者
        NotificationCenter.default.removeObserver(self, name: .tickerDataDidUpdate, object: nil)

        // 不再需要时，observation会自动取消订阅
        // 也可以手动设置为nil
        observation = nil
    }
}
```

## 六、基础组件与工具类

AICoin项目提供了丰富的基础组件和工具类，用于简化开发流程和提高代码质量。以下是与网络层相关的主要工具类及其使用方法。

### 1. JSON解析与模型转换

#### 1.1 SwiftyJSON

项目使用SwiftyJSON进行JSON解析，它提供了简洁的API和类型安全的访问方式：

```swift
// 解析JSON数据
let json = JSON(responseData)

// 安全地访问JSON数据
let success = json["success"].boolValue
let errorCode = json["errorCode"].intValue
let message = json["message"].stringValue
let data = json["data"]

// 访问数组
let items = json["data"]["items"].arrayValue
for item in items {
    let id = item["id"].stringValue
    let name = item["name"].stringValue
    print("ID: \(id), Name: \(name)")
}

// 检查键是否存在
if json["data"]["optional"].exists() {
    // 处理可选数据
}
```

#### 1.2 模型转换

项目提供了多种模型转换方式：

##### YYModel

```swift
// 从JSON字典创建模型
let model = TickerModel.model(withJSON: jsonDict)

// 从JSON字符串创建模型
let modelFromString = TickerModel.model(withJSON: jsonString)

// 模型转JSON
let jsonDict = model.modelToJSONObject()
let jsonString = model.modelToJSONString()
```

##### AICJSONModel

项目自定义的JSON模型协议，提供类型安全的解析：

```swift
// 模型定义
struct TickerModel: AICJSONModel {
    var id: String = ""
    var name: String = ""
    var price: Double = 0.0

    init(_ deserializer: AICJSONDeserializer) {
        self.id = deserializer["id"] ?? ""
        self.name = deserializer["name"] ?? ""
        self.price = deserializer["price"] ?? 0.0
    }
}

// 使用
let model = TickerModel.aicModel(with: jsonDict)
```

### 2. 日期与时间处理

项目使用SwiftDate和自定义扩展处理日期和时间：

```swift
// 获取当前时间戳
let timestamp = Date.getNowTimeStamp()

// 时间戳转日期字符串
let dateString = Date.getTimeString(timeStamp: timestamp, dateFormat: "yyyy-MM-dd HH:mm:ss")

// 日期字符串转时间戳
let timestamp = Date.getTimeStamp(timeString: "2023-01-01 12:00:00", dateFormat: "yyyy-MM-dd HH:mm:ss")

// 格式化日期
let formattedDate = date.getStringTime(dateFormat: "MM-dd HH:mm")

// 使用TickerDataFormatHelper
let dateString = TickerDataFormatHelper.requestDate(time: timestamp, dateFormat: "yyyy-MM-dd")
```

### 3. 数据格式化

项目提供了多种数据格式化工具：

```swift
// 数字格式化（保留小数位）
let formattedPrice = TickerDataFormatHelper.transformStr(str: "123.456789", num: 2) // "123.46"

// 价格格式化（根据币种自动选择小数位）
let formattedPrice = TickerDataFormatHelper.formatPrice("0.00123456", coin: "BTC") // "0.00123456"
let formattedPrice = TickerDataFormatHelper.formatPrice("1234.56", coin: "USDT") // "1,234.56"

// 涨跌幅格式化
let changeText = TickerDataFormatHelper.formatChange(1.23) // "+1.23%"
```

### 4. 网络缓存管理

除了前面介绍的`AICNetworkCache`，项目还提供了更多缓存相关工具：

```swift
// 缓存管理器
let cacheManager = AICCacheManager.share

// 获取缓存的DateFormatter（提高性能）
let dateFormatter = cacheManager.dateFormatter(withId: "hourFormatter") { formatter in
    formatter.dateFormat = "HH:mm"
}
let formattedTime = dateFormatter.string(from: Date())

// 缓存自定义对象
cacheManager.setObject(userInfo, forKey: "currentUser")
let cachedUser = cacheManager.object(forKey: "currentUser") as? UserInfo
```

### 5. 数据库操作 (WCDB)

项目使用WCDB.Swift进行数据持久化：

```swift
// 定义数据库模型
class MessageModel: TableCodable {
    var messageID: Int = 0
    var content: String = ""
    var timestamp: Int = 0

    enum CodingKeys: String, CodingTableKey {
        typealias Root = MessageModel
        static let objectRelationalMapping = TableBinding(CodingKeys.self)
        case messageID
        case content
        case timestamp
    }
}

// 数据库操作
class DBManager {
    private let database: Database
    private let tableName = "messages"

    init() {
        let path = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first! + "/app.db"
        database = Database(withPath: path)
        try? database.create(table: tableName, of: MessageModel.self)
    }

    // 插入数据
    func insert(message: MessageModel) -> Bool {
        do {
            try database.insert(objects: message, intoTable: tableName)
            return true
        } catch {
            print("插入失败: \(error)")
            return false
        }
    }

    // 查询数据
    func getMessage(id: Int) -> MessageModel? {
        do {
            return try database.getObject(fromTable: tableName, where: MessageModel.Properties.messageID == id)
        } catch {
            print("查询失败: \(error)")
            return nil
        }
    }

    // 更新数据
    func update(message: MessageModel) -> Bool {
        do {
            try database.update(table: tableName,
                               on: MessageModel.Properties.all,
                               with: message,
                               where: MessageModel.Properties.messageID == message.messageID)
            return true
        } catch {
            print("更新失败: \(error)")
            return false
        }
    }

    // 删除数据
    func delete(id: Int) -> Bool {
        do {
            try database.delete(fromTable: tableName, where: MessageModel.Properties.messageID == id)
            return true
        } catch {
            print("删除失败: \(error)")
            return false
        }
    }
}
```

### 6. 网络状态监测

除了前面介绍的网络状态监测方法，还可以使用以下方式：

```swift
// 获取当前网络类型
let netType = AICHttpManager.shared.netType
switch netType {
case .none:
    print("无网络连接")
case .wifi:
    print("WiFi连接")
case .wwan:
    print("蜂窝网络连接")
}

// 检查是否是正式环境
let isProduction = AICHttpManager.shared.isProduceEnv()

// 监听网络状态变化
NotificationCenter.default.addObserver(self,
                                      selector: #selector(networkChanged(_:)),
                                      name: .AICNetworkingChangedInNotice,
                                      object: nil)

@objc func networkChanged(_ notification: Notification) {
    if let isReachable = notification.object as? Bool, isReachable {
        print("网络已连接")
    } else {
        print("网络已断开")
    }
}
```

### 7. 实用工具方法

项目提供了许多实用的工具方法：

```swift
// 在主线程执行代码
AICSwiftTool.runonMainQueue {
    // 更新UI
}

// 在全局队列执行代码
AICSwiftTool.runonGlobalQueueAsync {
    // 执行耗时操作
}

// 延迟执行
AICSwiftTool.delay(seconds: 2.0) {
    // 延迟2秒后执行
}

// 字典转JSON字符串
let jsonString = dictionary.jsonStringEncoded()

// 数据解压缩
let decompressedData = compressedData.gzipInflate()

// 安全地获取用户ID
let userID = UserManager.share.safeUserID
```

### 8. 集成使用示例

以下是一个综合使用多个工具类的示例：

```swift
class TickerDetailViewController: AICBaseViewController {
    private var dataTask: URLSessionDataTask?
    private var tickerModel: TickerModel?

    override func viewDidLoad() {
        super.viewDidLoad()
        loadData()
    }

    private func loadData() {
        // 取消之前的请求
        dataTask?.cancel()

        // 检查网络状态
        guard AICHttpManager.shared.isReachable else {
            showToast("网络连接不可用")
            return
        }

        // 检查缓存
        let cacheKey = "ticker_detail_\(tickerId)"
        if let cachedModel: TickerModel = AICNetworkCache.shared.get(for: cacheKey) {
            self.tickerModel = cachedModel
            updateUI()
            return
        }

        // 发起网络请求
        let params = ["id": tickerId]
        dataTask = AICHttpManager.shared.post("/api/v1/ticker/detail",
                                             parameters: params,
                                             progress: nil,
                                             success: { [weak self] task, response in
            guard let self = self else { return }

            let json = JSON(response ?? "")
            if json["success"].boolValue {
                // 解析数据
                if let model = TickerModel.model(withJSON: json["data"]) {
                    self.tickerModel = model

                    // 缓存数据
                    AICNetworkCache.shared.cache(key: cacheKey, value: model, expireSeconds: 60)

                    // 更新UI
                    AICSwiftTool.runonMainQueue {
                        self.updateUI()
                    }

                    // 保存到数据库
                    self.saveToDatabase(model)
                }
            } else {
                let errorCode = json["errorCode"].intValue
                let errorMessage = json["error"].stringValue
                self.showError(message: errorMessage)
            }
        }, failure: { [weak self] task, error in
            guard let self = self else { return }
            self.showError(message: "网络请求失败")
        })
    }

    private func updateUI() {
        guard let model = tickerModel else { return }

        // 格式化价格
        let formattedPrice = TickerDataFormatHelper.formatPrice(model.price, coin: model.coin)
        priceLabel.text = formattedPrice

        // 格式化涨跌幅
        let changeText = TickerDataFormatHelper.formatChange(model.change)
        changeLabel.text = changeText
        changeLabel.textColor = model.change >= 0 ? .baseCurrentUpColor : .baseCurrentDownColor

        // 格式化时间
        let timeString = Date.getTimeString(timeStamp: Int(model.time), dateFormat: "yyyy-MM-dd HH:mm:ss")
        timeLabel.text = "更新时间: \(timeString)"
    }

    private func saveToDatabase(_ model: TickerModel) {
        AICSwiftTool.runonGlobalQueueAsync {
            // 数据库操作
            let dbManager = DBManager()
            _ = dbManager.insert(message: model)
        }
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)

        // 取消未完成的请求
        dataTask?.cancel()
    }
}