# AICoin-iOS 布局与UI组件文档

## 目录

1. [布局工具](#1-布局工具)
2. [空视图](#2-空视图)
3. [弹窗组件](#3-弹窗组件)
4. [下拉刷新](#4-下拉刷新)
5. [图片加载](#5-图片加载)
6. [最佳实践](#6-最佳实践)

## 1. 布局工具

AICoin-iOS项目不使用Storyboard或XIB进行UI开发，而是采用代码方式实现自动布局，以适配不同设备。项目中使用了多种自动布局库，各有特点。

### 1.1 SnapKit（推荐）

SnapKit是项目中推荐使用的布局库，语法简洁，易于维护。

#### 1.1.1 基本用法

```swift
// 基本约束
view.snp.makeConstraints { make in
    make.top.equalTo(superview.snp.top).offset(10)
    make.left.right.equalToSuperview()
    make.height.equalTo(44)
}

// 更新约束
view.snp.updateConstraints { make in
    make.height.equalTo(50)
}

// 重新设置约束
view.snp.remakeConstraints { make in
    make.edges.equalToSuperview().inset(UIEdgeInsets(top: 10, left: 10, bottom: 10, right: 10))
}
```

#### 1.1.2 安全区域适配

项目中对SnapKit进行了扩展，提供了安全区域适配的便捷方法：

```swift
// 使用安全区域约束
view.snp.makeConstraints { make in
    make.top.equalTo(view.snp_safeTop)
    make.bottom.equalTo(view.snp_safeBottom)
    make.left.equalTo(view.snp_safeLeading)
    make.right.equalTo(view.snp_safeTrailing)
}
```

#### 1.1.3 Ticker模块示例

```swift
// 行情列表页面布局示例
class TickerMarketListViewController: UIViewController {

    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .plain)
        tableView.register(TickerMarketListCell.self, forCellReuseIdentifier: "TickerMarketListCell")
        tableView.delegate = self
        tableView.dataSource = self
        return tableView
    }()

    private lazy var searchBar: UISearchBar = {
        let searchBar = UISearchBar()
        searchBar.placeholder = "搜索".ticker.localized
        return searchBar
    }()

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }

    private func setupUI() {
        // 添加子视图
        view.addSubview(searchBar)
        view.addSubview(tableView)
        searchBar.snp.makeConstraints { make in
            make.top.equalTo(view.snp_safeTop)
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
        }

        tableView.snp.makeConstraints { make in
            make.top.equalTo(searchBar.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
    }
}
```


## 2. 空视图

AICoin项目集成了DZNEmptyDataSet来处理空数据状态的视图展示，默认AICTableView的空视图是BaseEmptyDelegateModel。

### 2.1 基本用法

```swift
// 在视图控制器中实现DZNEmptyDataSet协议
class TickerMarketListViewController: UIViewController, DZNEmptyDataSetSource, DZNEmptyDataSetDelegate {

    override func viewDidLoad() {
        super.viewDidLoad()

        // 设置空视图代理
        tableView.emptyDataSetSource = self
        tableView.emptyDataSetDelegate = self
    }

    // MARK: - DZNEmptyDataSetSource

    func title(forEmptyDataSet scrollView: UIScrollView!) -> NSAttributedString! {
        return NSAttributedString(string: "暂无数据".base.localized, attributes: [
            .font: UIFont.systemFont(ofSize: 16),
            .foregroundColor: UIColor.base.current.textColor
        ])
    }

    func description(forEmptyDataSet scrollView: UIScrollView!) -> NSAttributedString! {
        return NSAttributedString(string: "请稍后再试".base.localized, attributes: [
            .font: UIFont.systemFont(ofSize: 14),
            .foregroundColor: UIColor.base.current.subtitleColor
        ])
    }

    func image(forEmptyDataSet scrollView: UIScrollView!) -> UIImage! {
        return UIImage.base.image(name: "empty_placeholder")
    }

    // MARK: - DZNEmptyDataSetDelegate

    func emptyDataSetShouldDisplay(_ scrollView: UIScrollView!) -> Bool {
        // 控制空视图的显示条件
        return dataSource.isEmpty && !isLoading
    }

    func emptyDataSetShouldAllowScroll(_ scrollView: UIScrollView!) -> Bool {
        return true
    }

    func emptyDataSet(_ scrollView: UIScrollView!, didTap button: UIButton!) {
        // 处理空视图按钮点击
        loadData()
    }
}
```

### 2.2 使用BaseEmptyDelegateModel

AICTableView默认使用BaseEmptyDelegateModel作为空视图模型，可以简化空视图的配置：

```swift
// 创建空视图模型
let emptyModel = BaseEmptyDelegateModel()
emptyModel.title = "暂无行情数据"
emptyModel.detail = "请稍后再试"
emptyModel.image = UIImage.ticker.image(name: "empty_market")
emptyModel.buttonTitle = "刷新"
emptyModel.buttonAction = { [weak self] in
    self?.loadData()
}

// 设置到表格视图
tableView.aic_emptyDataModel = emptyModel
```

### 2.3 Ticker模块示例

```swift
// 行情列表空视图示例
func setupEmptyView() {
    let emptyModel = BaseEmptyDelegateModel()
    emptyModel.title = "暂无行情数据".ticker.localized
    emptyModel.detail = "请检查网络连接或稍后再试".ticker.localized
    emptyModel.image = UIImage.ticker.image(name: "ticker_empty")
    emptyModel.buttonTitle = "刷新".base.localized
    emptyModel.buttonAction = { [weak self] in
        self?.refreshData()
    }

    tableView.aic_emptyDataModel = emptyModel
}
```

## 3. 弹窗组件

AICoin项目提供了一套完整的自定义弹窗系统，根据使用频率统计，项目中最常用的几种弹窗组件如下：

- **AICInfoAlertController**: 最常用的信息提示弹窗
- **AICBaseCustomPresentationViewController**: 所有自定义弹窗的基类
- **AICAlertViewController**: 项目中通用的警告控制器
- **AICInfoActionSheetController**: 底部弹出的信息菜单
- **AICCustomViewAlertController**: 自定义视图警告控制器
- **AICPopoverViewController**: 气泡弹窗基类

### 3.1 AICInfoAlertController (最常用)

`AICInfoAlertController`是项目中使用最频繁的弹窗类型，用于显示标题、消息和按钮，适合简单的提示信息场景。

```swift
// 创建信息提示弹窗
func showInfoAlert() {
    // 创建带标题和消息的弹窗
    let alertVC = AICInfoAlertController(
        title: "提示信息".ticker.localized,
        message: "操作已完成".ticker.localized
    )

    // 添加按钮
    alertVC.addAction(action: AICAlertAction(
        title: "确定".ticker.localized,
        style: .default,
        handler: { _ in
            // 按钮点击回调
            print("用户点击了确定")
        }
    ))

    // 显示弹窗
    present(alertVC, animated: true, completion: nil)
}
```

### 3.2 AICBaseCustomPresentationViewController (基类)

`AICBaseCustomPresentationViewController`是所有自定义弹窗的基类，许多具体弹窗继承自此类。它提供了自定义转场动画和交互式消失功能，支持两种风格：alert(中间弹窗)和actionSheet(底部弹出)。

```swift
// 创建自定义弹窗
class CustomAlertViewController: AICBaseCustomPresentationViewController {

    init() {
        // 使用alert样式初始化（居中显示）
        super.init(style: .alert)
    }

    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }

    // 必须覆写preferredContentSize
    override var preferredContentSize: CGSize {
        get {
            return CGSize(width: 280, height: 200)
        }
        set {
            super.preferredContentSize = newValue
        }
    }

    private func setupUI() {
        // 设置弹窗内容和布局
        view.backgroundColor = UIColor.base.current.backgroundColor
        // 添加子视图和设置约束（此处省略具体实现）
    }
}

// 底部弹出样式的弹窗
class BottomSheetViewController: AICBaseCustomPresentationViewController {

    init() {
        super.init(style: .actionSheet)
    }

    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // 必须覆写preferredContentSize
    override var preferredContentSize: CGSize {
        get {
            return CGSize(width: UIScreen.main.bounds.width, height: 300)
        }
        set {
            super.preferredContentSize = newValue
        }
    }
}
```

### 3.3 AICAlertViewController (通用警告控制器)

`AICAlertViewController`是项目中通用的警告控制器，提供预设样式，使用频率较高。它简化了弹窗创建过程，支持标题、自定义内容视图和多个操作按钮。

```swift
// 创建通用警告弹窗 - 通过继承使用
class CustomAlertViewController: AICAlertViewController {

    init() {
        // 创建自定义内容视图
        let contentView = CustomAlertContentView()

        super.init(
            title: "操作确认".ticker.localized,
            customView: contentView,
            hasCancelBtn: true
        )
    }

    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupActions()
    }

    private func setupActions() {
        // 添加取消按钮
        addAction(BaseAlertAction(
            title: "取消".ticker.localized,
            type: .cancel,
            action: nil
        ))

        // 添加确认按钮
        addAction(BaseAlertAction(
            title: "确认".ticker.localized,
            type: .default,
            action: {
                // 按钮点击回调
                self.performAction()
            }
        ))
    }

    private func performAction() {
        // 执行具体操作
    }
}

// 使用示例
func showAlert() {
    let alertVC = CustomAlertViewController()
    present(alertVC, animated: true, completion: nil)
}

// 不带标题的弹窗
class NoTitleAlertViewController: AICAlertViewController {

    init() {
        let contentView = CustomContentView()
        super.init(customView: contentView, hasCancelBtn: true)
    }

    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}
```

### 3.4 AICCustomViewAlertController (自定义视图)

`AICCustomViewAlertController`用于完全自定义内容的场景，适合需要复杂UI的弹窗。

```swift
// 创建自定义内容弹窗 - 通过继承使用
class CustomViewAlertViewController: AICCustomViewAlertController {

    init() {
        // 创建自定义内容视图
        let contentView = CustomView()
        contentView.frame = CGRect(x: 0, y: 0, width: 280, height: 200)

        super.init(customView: contentView)
    }

    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        // 可以在这里添加额外的设置
    }
}

// 使用示例
func showCustomAlert() {
    let alertVC = CustomViewAlertViewController()
    present(alertVC, animated: true, completion: nil)
}
```

### 3.5 AICInfoActionSheetController (底部菜单)

`AICInfoActionSheetController`用于从底部弹出的菜单选项，适合提供多个操作选项的场景。

```swift
// 创建底部操作菜单
func showActionSheet() {
    // 创建底部菜单
    let actionSheet = AICInfoActionSheetController(
        title: "请选择操作".ticker.localized,
        detail: "从以下选项中选择一项".ticker.localized
    )

    // 添加操作按钮
    actionSheet.addAction(AICAlertAction(
        title: "编辑".ticker.localized,
        style: .default,
        handler: { _ in
            self.editItem()
        }
    ))

    actionSheet.addAction(AICAlertAction(
        title: "删除".ticker.localized,
        style: .destructive,
        handler: { _ in
            self.deleteItem()
        }
    ))

    actionSheet.addAction(AICAlertAction(
        title: "取消".ticker.localized,
        style: .cancel,
        handler: nil
    ))

    // 显示底部菜单
    present(actionSheet, animated: true, completion: nil)
}
```

### 3.6 AICPopoverViewController (气泡弹窗)

`AICPopoverViewController`是气泡弹窗基类，用于小型上下文菜单，可以指定箭头位置。

```swift
// 创建气泡弹窗
func showPopover(from sourceView: UIView) {
    // 创建气泡弹窗内容
    let contentVC = PopoverContentViewController()

    // 创建气泡弹窗
    let popoverVC = AICPopoverViewController()
    popoverVC.contentViewController = contentVC

    // 设置气泡样式
    popoverVC.style.position = .bottom
    popoverVC.style.arrowSize = 8
    popoverVC.style.cornerRadius = 8

    // 显示气泡弹窗
    popoverVC.show(from: sourceView, in: self.view)
}
```

## 4. AICSwitch开关组件

AICoin项目提供了自定义的开关组件`AICSwitch`，它是基于`SevenSwitch`的封装，支持主题适配和自定义样式。

### 4.1 基本特性

- 自动适配主题颜色（日间/夜间模式）
- 支持阴影效果
- 固定尺寸设计（37x13）
- 扩大点击区域，提升用户体验
- 圆角设计，视觉效果更佳

### 4.2 基本用法

```swift
// 创建开关
let aicSwitch = AICSwitch()

// 添加事件监听
aicSwitch.addTarget(self, action: #selector(switchValueChanged(_:)), for: .valueChanged)

// 设置开关状态
aicSwitch.on = true

// 获取开关状态
let isOn = aicSwitch.isOn

// 添加到视图
view.addSubview(aicSwitch)
aicSwitch.snp.makeConstraints { make in
    make.right.equalToSuperview().offset(-16)
    make.centerY.equalToSuperview()
}

// 处理开关状态变化
@objc func switchValueChanged(_ sender: AICSwitch) {
    if sender.isOn {
        // 开关打开时的处理
        print("开关已打开")
    } else {
        // 开关关闭时的处理
        print("开关已关闭")
    }
}
```

### 4.3 在表格中使用

AICoin项目提供了`BaseSwitchTableViewCell`，方便在表格中使用开关组件：

```swift
// 创建带开关的表格单元格
let cell = tableView.dequeueReusableCell(
    withIdentifier: "SwitchCell",
    for: indexPath
) as! BaseSwitchTableViewCell

// 配置单元格
cell.titleLabel.text = "开启通知".base.localized
cell.aSwitch.on = isNotificationEnabled

// 处理开关状态变化
cell.touchSwitchCallBack = { isOn in
    // 更新设置
    self.updateNotificationSetting(isEnabled: isOn)
}

return cell
```

### 4.4 主题适配

`AICSwitch`会自动适配当前主题，但也可以手动更新颜色：

```swift
// 主题切换时手动更新颜色
NotificationCenter.default.addObserver(self,
                                      selector: #selector(themeDidChange),
                                      name: NSNotification.Name(SSBThemeManagerShouldChangeTheme),
                                      object: nil)

@objc private func themeDidChange() {
    // 更新开关颜色
    aicSwitch.updateColor()
}
```

## 5. 下拉刷新

AICoin项目对MJRefresh进行了封装，提供了`AICRefreshNormalHeader`和`AICRefreshAutoNormalFooter`，支持主题切换和国际化。

### 5.1 基本用法

```swift
// 添加下拉刷新
tableView.mj_header = AICRefreshNormalHeader(refreshingBlock: { [weak self] in
    // 刷新数据
    self?.loadData()
})

// 添加上拉加载更多
tableView.mj_footer = AICRefreshAutoNormalFooter(refreshingBlock: { [weak self] in
    // 加载更多数据
    self?.loadMoreData()
})

// 结束刷新
tableView.mj_header?.endRefreshing()
tableView.mj_footer?.endRefreshing()

// 没有更多数据
tableView.mj_footer?.endRefreshingWithNoMoreData()

// 重置没有更多数据状态
tableView.mj_footer?.resetNoMoreData()
```

### 5.2 自定义刷新样式

```swift
// 自定义下拉刷新样式
let header = AICRefreshNormalHeader(refreshingBlock: { [weak self] in
    self?.loadData()
})
header.setTitle("下拉刷新".base.localized, for: .idle)
header.setTitle("释放更新".base.localized, for: .pulling)
header.setTitle("正在刷新...".base.localized, for: .refreshing)
header.lastUpdatedTimeLabel?.isHidden = true  // 隐藏最后更新时间
tableView.mj_header = header

// 自定义上拉加载样式
let footer = AICRefreshAutoNormalFooter(refreshingBlock: { [weak self] in
    self?.loadMoreData()
})
footer.setTitle("点击或上拉加载更多".base.localized, for: .idle)
footer.setTitle("正在加载...".base.localized, for: .refreshing)
footer.setTitle("没有更多数据".base.localized, for: .noMoreData)
tableView.mj_footer = footer
```

### 5.3 Ticker模块示例

```swift
// 行情列表刷新示例
func setupRefresh() {
    // 下拉刷新
    tableView.mj_header = AICRefreshNormalHeader(refreshingBlock: { [weak self] in
        // 刷新行情数据
        self?.viewModel.refreshMarketList { [weak self] success in
            self?.tableView.mj_header?.endRefreshing()
            if success {
                self?.tableView.reloadData()
            }
        }
    })

    // 上拉加载更多
    tableView.mj_footer = AICRefreshAutoNormalFooter(refreshingBlock: { [weak self] in
        // 加载更多行情数据
        self?.viewModel.loadMoreMarkets { [weak self] (success, hasMore) in
            if !hasMore {
                self?.tableView.mj_footer?.endRefreshingWithNoMoreData()
            } else {
                self?.tableView.mj_footer?.endRefreshing()
            }

            if success {
                self?.tableView.reloadData()
            }
        }
    })
}
```

## 6. 图片加载

AICoin项目使用`YYWebImageManager`进行异步图片加载和缓存，通过扩展方法简化调用。

### 6.1 基本用法

```swift
// 基本用法
imageView.aic_setImage(with: URL(string: urlString))

// 带占位图
imageView.aic_setImage(with: URL(string: urlString), placeholder: UIImage(named: "placeholder"))

// 带回调
imageView.aic_setImage(with: URL(string: urlString), placeholder: nil, options: [], completion: { (image, url, _, _, error) in
    // 处理加载完成后的逻辑
})
```

### 6.2 高级选项

```swift
// 设置图片加载选项
let options: YYWebImageOptions = [
    .setImageWithFadeAnimation,  // 淡入动画
    .progressiveBlur,            // 渐进式模糊
    .allowBackgroundTask         // 允许后台任务
]

// 加载并处理图片
imageView.aic_setImage(with: URL(string: urlString), placeholder: placeholderImage, options: options) { (image, url, _, _, error) in
    if let error = error {
        print("图片加载失败: \(error.localizedDescription)")
    } else if let image = image {
        // 图片加载成功，可以进行额外处理
        self.processLoadedImage(image)
    }
}
```

### 6.3 Ticker模块示例

```swift
// 行情列表中加载币种图标示例
class TickerMarketListCell: UITableViewCell {

    private let iconImageView = UIImageView()
    private let nameLabel = UILabel()
    private let priceLabel = UILabel()

    func configure(with model: TickerMarketListModel) {
        // 设置币种名称、价格等其他数据
        nameLabel.text = model.coinShow
        priceLabel.text = model.lastPriceText

        // 加载币种图标
        if let iconURL = URL(string: model.logo) {
            // 使用YYWebImageManager加载图片
            iconImageView.aic_setImage(with: iconURL,
                                      placeholder: UIImage.ticker.image(name: "default_coin_icon"))
        } else {
            // 使用默认图标
            iconImageView.image = UIImage.ticker.image(name: "default_coin_icon")
        }
    }
}
```

## 7. 最佳实践

### 7.1 布局最佳实践

- 优先使用SnapKit进行布局，避免使用frame和CGRect
- 使用安全区域适配方法适配不同设备
- 避免硬编码尺寸，使用相对尺寸和比例
- 使用UIStackView简化线性布局
- 复杂UI拆分为小组件，提高复用性

### 7.2 空视图最佳实践

- 为不同的空状态设计不同的提示信息和图标
- 提供明确的操作指引，如刷新按钮
- 空视图文案应简洁明了，避免技术术语
- 考虑首次加载、网络错误、无数据等不同场景

### 7.3 弹窗最佳实践

- 使用AICBaseAlertController而非UIAlertController
- 弹窗内容保持简洁，避免过多信息
- 提供明确的操作按钮，避免用户困惑
- 考虑键盘弹出时的布局调整

### 7.4 下拉刷新最佳实践

- 避免自动触发刷新，除非必要
- 提供明确的视觉反馈，如刷新状态文本
- 考虑首次加载时是否显示刷新控件
- 合理使用无更多数据状态

### 7.5 图片加载最佳实践

- 始终提供占位图，避免布局跳动
- 使用适当的图片缓存策略
- 考虑列表滚动时的图片加载性能
- 处理图片加载失败的情况

### 7.6 开关组件最佳实践

- 使用AICSwitch而非系统UISwitch，保持视觉一致性
- 在主题切换时调用updateColor()方法更新颜色
- 使用BaseSwitchTableViewCell简化表格中的开关使用
- 为开关提供明确的标签说明其功能
- 考虑开关状态变化时的用户反馈