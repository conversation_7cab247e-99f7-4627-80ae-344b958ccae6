//
//  SetupAppDelegate.m
//  AICoin
//
//  Created by y<PERSON><PERSON><PERSON><PERSON> on 16/6/14.
//  Copyright © 2016年 AICoin. All rights reserved.
//

#import "SetupAppDelegate.h"

#import "AICHttpManager+NetworkingTools.h"
#import <AFNetworking/AFNetworkActivityIndicatorManager.h>
//#import "AICOCVenderBridge.h"
//#import "HttpDnsService+Tools.h"

#import "UIView+Toast.h"

#import <UserNotifications/UserNotifications.h>
#import "UserManager.h"
#import "NSDictionary+Extension.h"
//#import "CoreDataManager.h"

#import <AudioToolbox/AudioToolbox.h>

//#import "JPUSHService.h"
//#import "SoundManager.h"
#import "UIImageView+Extend.h"
//#import "SettingConfig.h"

#import "WidgetDataSelectedViewController.h"

#import "AICoin-Swift.h"

#import "BifuNotificationHistory.h"

//#import "JPUSHTag.h"
//#import "AICRunLoopThread.h"
#import "FXBlurView.h"
//#import "TradeSecurityGestureLockTimeView.h"

//#import "SetPreferCurrencyVC.h"
//#import "MutiLanguageSettingVC.h"
//#import "AvoidCrash.h"
#import <BuglyHotfix/Bugly.h>
#import <UMMobClick/MobClick.h>
#import "XGPush.h"
#import "XGPushPrivate.h"
//#import <ShareSDK/ShareSDK.h>
//#import <ShareSDKConnector/ShareSDKConnector.h>
//#import <TencentOpenAPI/TencentOAuth.h>
//#import <TencentOpenAPI/QQApiInterface.h>
//#import "WXApi.h"
//#import "WeiboSDK.h"
//#import <FBSDKMessengerShareKit/FBSDKMessengerShareKit.h>
//#import "MobScreenshotCenter.h"

//#import "UIView+ChatTools.h"
//#import "UIView+SSBtcChatToast.h"
//#import "JHThemeManager.h"

//#import "SSBtcChatManagerHeader.h"
#import "AICCacheManager.h"
//#import "SSBtcCacheManager+AICChatPrivate.h"
//#import "SSBtcChatBaseNetManager.h"
#import "UIViewController+AICTools.h"
#import "PriceWarning.h"
#import "AICoin-Swift.h"

//#import "OCKLineThemeManager.h"

#import <InternalToolKit/InternalToolKit.h>
#import <IQKeyboardManager/IQKeyboardManager.h>
#import <YYKit_modify/YYKit.h>
#import <tingyunApp/NBSAppAgent.h>
#import "NSBundle+Language.h"
#import "AICDomainManager.h"
#import <FlutterPluginRegistrant/GeneratedPluginRegistrant.h>
//#import <Sentry/Sentry.h>

//http可容许的错误率
#define HTTP_REQUEST_ERROR_RATE_THRESHOLD 0.4

#if (AIC_APP_TYPE == 3)
    /// Test
    #define TPNS_ACCESS_ID 1620011461

    #define TPNS_ACCESS_KEY @"I1Y9B7PAGG7J"
#else
    /// Pro
    #define TPNS_ACCESS_ID 1620011516

    #define TPNS_ACCESS_KEY @"IF7WTRDPC43X"
#endif


@interface SetupAppDelegate() <XGPushTokenManagerDelegate, UIAlertViewDelegate>

@property (nonatomic, strong) NSDictionary *notificationUserInfo;

@property (nonatomic, strong, readonly) AICTabBarController *rootVC;

//是否应该开启HTTPDNS
//@property (nonatomic, assign, getter=isShouldOpenHttpDNS, readonly) BOOL shouldOpenHttpDNS;
@property (nonatomic, assign) BOOL tryOpenHttpDNS;
@property (nonatomic, assign) NSInteger reconnectTime;

/// tpns注册定时器
@property (nonatomic, assign) NSInteger tpnsTime;
@property (nonatomic, assign) NSTimer *tpnsTimer;

@property (nonatomic, assign) NSInteger failCount;
@end

@implementation SetupAppDelegate

{
    __weak UIAlertView *_remoteAlertView;
    
    NSString *_coin;
    NSString *_market;
//    NSString *_coinTitle;
//    NSString *_marketTitle;
    RealtimeNewsAlertViewController *alertController;
    
    
    NSMutableDictionary<NSString *, NSString *>* _cachedWarningNotes;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (BOOL)isShouldOpenHttpDNS {
    return [AICCacheManager shareManager].openOptimizingRequests && _tryOpenHttpDNS;
}

- (instancetype)init {
    if (self = [super init]) {
        
        _tryOpenHttpDNS = true;
        
        [NSBundle loadUserSettingLanguage];
        
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(applicationDidFinishShowRootViewController) name:AICApplicationDidFinishShowRootViewControllerNotice object:nil];
        
        /// 监听登录，去访问TPNS灰度接口
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(registerTPNS) name:AICUserNoticeKeyLoginSuccess object:nil];
        /// 监听登出，去访问TPNS灰度接口
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(registerTPNS) name:AICUserNoticeKeyLogoutSuccess object:nil];
    }
    return self;
}

- (void)setLaunchScreen:(BOOL)launchScreen {
    _launchScreen = launchScreen;
    [self application:[UIApplication sharedApplication] supportedInterfaceOrientationsForWindow:nil];
}

#pragma mark - UIApplicationDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
//    self.flutterEngine = [[FlutterEngine alloc] initWithName:@"flutter_engine"];
//    [self.flutterEngine run];
//    [GeneratedPluginRegistrant registerWithRegistry:self.flutterEngine];
    self.flutterEngineGroup = [[FlutterEngineGroup alloc] initWithName:@"multiple-flutters" project:nil];
    self.chatFlutterEngine = [self.flutterEngineGroup makeEngineWithEntrypoint:@"chatMain" libraryURI:nil];
    [self.chatFlutterEngine run];
    self.tradeFlutterEngine = [self.flutterEngineGroup makeEngineWithEntrypoint:@"tradeRemoteMain" libraryURI:nil];
    [self.tradeFlutterEngine run];
    [GeneratedPluginRegistrant registerWithRegistry:self.chatFlutterEngine];
    [GeneratedPluginRegistrant registerWithRegistry:self.tradeFlutterEngine];

    [AICSwiftTool runonMainQueueWithBlock:^{
        [self configTingyun];
        [self configBugly];
//        [self configHTTPDNS];
//        [self configShareSDK];
//        [self configModule];
        [self configOther];
        
        [self configUM];
//        [self configSetTPNS];
//        [self registerTPNS];
        _reconnectTime = 1;
        _tpnsTime = 1;
        self.isTPNSRegistSuccess = false;
        [XGPushTokenManager defaultTokenManager].delegate = self;
        [[UIApplication sharedApplication] registerForRemoteNotifications];
        
        //  检查主题是否需要修改
        [SSBThemeManager.sharedManager checkChangeThemeInUserDefaults];
        [self configSentry];
        
    }];
    
    if(aic_appType == AICAPPTypeTest){
        //  设置测试版默认环境为 API-TEST
        NSDictionary *defaultValues = @{@"aic_openAPITestEnv": @(YES)};
        [[NSUserDefaults standardUserDefaults] registerDefaults:defaultValues];
    }
    
    /// 获取配置下发
    [AICSwiftTool runonGlobalQueueAsyncWithBlock:^{
        [[AICDomainManager sharedManager] requestDoaminData];
    }];
    
    /// 更新聊天室推送配置
    [AICSwiftTool runonGlobalQueueAsyncWithBlock:^{
        [[ChatMessageCount shared] updateChartRoomPushState];
    }];
    
    #if DEBUG
   //iOS 接入InjectionIII热重载
   [[NSBundle bundleWithPath:@"/Applications/InjectionIII.app/Contents/Resources/iOSInjection.bundle"] load];
  //同时还支持tvOS和MacOS，配置时只需要在/Applications/InjectionIII.app/Contents/Resources/目录下找到对应的bundle文件,替换路径即可
    #endif
    
    [self configXGPushWithOptions:launchOptions];
    
    //    BOOL agreePrivacypolicy = [[NSUserDefaults standardUserDefaults] boolForKey:@"agreePrivacypolicy"];
        
    self.window = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
    
    self.window.rootViewController = [[AICTabBarController alloc] init];
    
    [self.window makeKeyAndVisible];
    
    // 初始化全局的FloatingWindow
    UIWindow *currentKeyWindow = [UIApplication sharedApplication].keyWindow;
    
    FloatingWindow *second = FloatingWindow.shared;
    second.backgroundColor = [UIColor clearColor];
    [second makeKeyAndVisible];
    
    [currentKeyWindow makeKeyWindow];
    [UserLevelModel.shared fetchUserLevelInfo];
    [BaseSwiftMethod showAgreementAlertOn:self.window.rootViewController];
//    [self getFlashCacheLimit];
    NSDictionary *userInfo = launchOptions[UIApplicationLaunchOptionsRemoteNotificationKey];
    if (userInfo) {
        // 处理app退出后台的情况下点击通知栏拉起半屏化弹窗
        NSData *jsonData = [userInfo[@"custom"] dataUsingEncoding:NSUTF8StringEncoding];
        if(jsonData != nil){
            NSError *err;
            NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData
                                                                options:NSJSONReadingMutableContainers
                                                                  error:&err];
            [self aichandleRemoteNotification:dic withApplication:[UIApplication sharedApplication] isClick:true];
        }
    }
//    
    BOOL openAPITest = [[NSUserDefaults standardUserDefaults] boolForKey:@"ClearCacheSecondary"];
    if(!openAPITest) {
        [[AICCacheManager shareManager] clearWebCache];
        [[NSUserDefaults standardUserDefaults] setBool:true forKey:@"ClearCacheSecondary"];
    }
//    return [super application:application didFinishLaunchingWithOptions:launchOptions];
    return true;
}

/// 添加标签回调
- (void)xgPushDidClearAndAppendTags:(NSArray<NSString *> *)tags error:(NSError *)error {
    [TPNSTag xgPushErrorCallBackWithTags:tags error:error];
}

- (void)applicationDidBecomeActive:(UIApplication *)application {

    [[TimerHelper shareInstanceForOC] resumeAllTimers];
//    [[TimerHelper shareInstanceForOC] stopTimerWithIdentifier:@"DidEnterBackgroundDisConnectWS"];
    [self checkTickerTabConfig];
    [AICNewsTabModel requestNewsTabConfig];
    [RealtimeNewsTabModel requestRealtimeNewsTabConfig];
    
    // 重置应用角标，-1不清空通知栏，0清空通知栏
    [XGPush defaultManager].xgApplicationBadgeNumber = -1;
//    // 将应用角标清零
    [[XGPush defaultManager] setBadge:0];
    
    
    [[CheckVersion share] checkWithManual:false];
    [[CheckVersion share] checkServerAvailable];
    
//    [[AICTickerRequestOperation share] getLiqHeight];
    
    [[UserManager shareManager] updateUserTPNSTag];
    
//    [self checkRemotePatch];
    
    if (_hasRunSettingConfig) {
        return;
    }
    
    _hasRunSettingConfig = true;
    _cachedWarningNotes = [NSMutableDictionary dictionary];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self checkTickerTabConfig];
        [AICNewsTabModel requestNewsTabConfig];
        [RealtimeNewsTabModel requestRealtimeNewsTabConfig];
    });
}


- (void)applicationWillResignActive:(UIApplication *)application {
    [[TimerHelper shareInstanceForOC] pauseAllTimers];
    [[CoreDataManager sharedManagedData] saveContext];
}


- (void)applicationDidReceiveMemoryWarning:(UIApplication *)application {
    [[NSURLCache sharedURLCache] removeAllCachedResponses];
}

- (void)applicationWillTerminate:(UIApplication *)application {
    [[CoreDataManager sharedManagedData] saveContext];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

//- (BOOL)application:(UIApplication *)application openURL:(NSURL *)url sourceApplication:(nullable NSString *)sourceApplication annotation:(nonnull id)annotation{
//    [self handleOpenURL:url];
//
//    return true;
//}

- (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(nonnull NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options {
    [self handleOpenURL:url];
    
    return true;
}


- (void)application:(UIApplication *)application handleWatchKitExtensionRequest:(NSDictionary *)userInfo reply:(void (^)(NSDictionary * _Nullable))reply {
    NSMutableDictionary *mDict = [NSMutableDictionary new];
    NSString *string = @"OK,I see.";
    mDict[@"key"] = [NSKeyedArchiver archivedDataWithRootObject: string];
    if (reply) {
        reply(mDict);
    }
}

- (BOOL)application:(UIApplication *)application continueUserActivity:(nonnull NSUserActivity *)userActivity restorationHandler:(nonnull void (^)(NSArray<id<UIUserActivityRestoring>> * _Nullable))restorationHandler {
//    BOOL handled = NO;
    
    // Extract the payload
    NSDictionary *userInfo = [userActivity userInfo];
    
    if (restorationHandler) {
        restorationHandler(@[[UIApplication sharedApplication].delegate.window.rootViewController]);
    }
    
    if([(NSNumber *)userInfo[@"toDetail"] integerValue] == 1) {
        _coin = userInfo[@"coin"];
        _market = userInfo[@"market"];
        [self gotoDetailPage];
    }else if ([(NSNumber *)userInfo[@"toSetAppleWatchItems"] integerValue] == 1) {
        [self gotoSetAppleWatchItems];
    }
    if ([userActivity.activityType isEqualToString:NSUserActivityTypeBrowsingWeb]) {
        NSURL *webUrl = userActivity.webpageURL;
        [self handleUniversalLink:webUrl];
        [AICShare handleUrlWithUrl:webUrl];
    }
    return true;
}

- (BOOL)handleUniversalLink:(NSURL *)url {
    
    NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:YES];
    NSString *host = components.host;
    NSArray<NSString*> *pathComponents = components.path.pathComponents;
    if ([host isEqualToString:@"www.aicoin.net.cn"] || [host isEqualToString:@"www.aicoin.com"]) {
        if (pathComponents.count >= 4) {
            if ([pathComponents[1] isEqualToString:@"app"]) {
                NSString *path = pathComponents[2];
                if ([path isEqualToString:@"moments"]) {
                    NSString *ID = pathComponents[3];
                    if (ID && ID.length > 0) {
                        [DynamicHelper goMomentsDetailWithId:ID];
                    }
                }
                return YES;
            }
        }
    }
    return NO;
}

- (void)application:(UIApplication *)application didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken {
    /// TPNS
    if (![deviceToken isKindOfClass:[NSData class]]) return;
       const unsigned *tokenBytes = (const unsigned *)[deviceToken bytes];
       NSString *hexToken = [NSString stringWithFormat:@"%08x%08x%08x%08x%08x%08x%08x%08x",
                             ntohl(tokenBytes[0]), ntohl(tokenBytes[1]), ntohl(tokenBytes[2]),
                             ntohl(tokenBytes[3]), ntohl(tokenBytes[4]), ntohl(tokenBytes[5]),
                             ntohl(tokenBytes[6]), ntohl(tokenBytes[7])];
       NSLog(@"deviceToken:%@",hexToken);
}

/// 注册推送服务成功回调
/// @param deviceToken APNs 生成的Device Token
/// @param xgToken TPNS 生成的 Token，推送消息时需要使用此值。TPNS 维护此值与APNs 的 Device Token的映射关系
/// @param error 错误信息，若error为nil则注册推送服务成功
- (void)xgPushDidRegisteredDeviceToken:(nullable NSString *)deviceToken xgToken:(nullable NSString *)xgToken error:(nullable NSError *)error {
    NSLog(@"TPNSdeviceToken:%@",xgToken);
    NSLog(@"%s, result %@, error %@", __FUNCTION__, error ? @"NO" : @"OK", error);
    NSString *errorStr = !error ? NSLocalizedString(@"success", nil) : NSLocalizedString(@"failed", nil);
    NSString *message = [NSString stringWithFormat:@"%@%@", NSLocalizedString(@"register_app", nil), errorStr];
    if (!error) {
        [self bindTPNSToken];
        self.tpnsTime = 1;
        
        /// 重置应用角标，-1不清空通知栏，0清空通知栏
        [XGPush defaultManager].xgApplicationBadgeNumber = -1;
        /// 重置服务端自动+1基数
        [[XGPush defaultManager] setBadge:0];
    } else {
        self.tpnsErrorCode = error.code;
    }
    //设置是否注册成功
    self.isTPNSRegistSuccess = error ? false : true;
    
}

/// 注册推送服务失败回调
/// @param error 注册失败错误信息
- (void)xgPushDidFailToRegisterDeviceTokenWithError:(nullable NSError *)error {
    NSLog(@"%s, errorCode:%ld, errMsg:%@", __FUNCTION__, (long)error.code, error.localizedDescription);
    self.isTPNSRegistSuccess = false;
    if (_tpnsTime < 4) {
        _tpnsTime += 1;
        [self registerTPNS];
    }
}

/// 统一接收消息的回调
/// @param notification 消息对象(有2种类型NSDictionary和UNNotification具体解析参考示例代码)
/// @note 此回调为前台收到通知消息及所有状态下收到静默消息的回调（消息点击需使用统一点击回调）
/// 区分消息类型说明：xg字段里的msgtype为1则代表通知消息,msgtype为2则代表静默消息,msgtype为9则代表本地通知
- (void)xgPushDidReceiveRemoteNotification:(nonnull id)notification withCompletionHandler:(nullable void (^)(NSUInteger))completionHandler {
    NSDictionary *notificationDic = nil;
    if ([notification isKindOfClass:[UNNotification class]]) {
        notificationDic = ((UNNotification *)notification).request.content.userInfo;
        completionHandler(UNNotificationPresentationOptionBadge | UNNotificationPresentationOptionSound | UNNotificationPresentationOptionAlert);
        if (notificationDic[@"custom"]) {
            NSData *jsonData = [notificationDic[@"custom"] dataUsingEncoding:NSUTF8StringEncoding];
            NSError *err;
        
            NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData
                                                                    options:NSJSONReadingMutableContainers
                                                                      error:&err];
            [self aichandleRemoteNotification:dic withApplication:[UIApplication sharedApplication] isClick:false];
        } else {
            
            NSDictionary *userInfo = notificationDic;
            if([((UNNotification *)notification).request.trigger isKindOfClass:[UNPushNotificationTrigger class]]) {
                [self aichandleRemoteNotification:userInfo withApplication:[UIApplication sharedApplication] isClick:false];
            } else {
                [self aichandleLocalNotification:userInfo withApplication:[UIApplication sharedApplication] isClick:false];
            }
        }
    } else if ([notification isKindOfClass:[NSDictionary class]]) {
        notificationDic = notification;
        completionHandler(UIBackgroundFetchResultNewData);
    }
    if (notificationDic[@"xg"] && notificationDic[@"custom"]) {
        FlashIDMapManager *manager = [FlashIDMapManager shared];
        NSData *jsonData = [notificationDic[@"custom"] dataUsingEncoding:NSUTF8StringEncoding];
        NSError *err;
    
        NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData
                                                                options:NSJSONReadingMutableContainers
                                                                  error:&err];
//        if([dic[@"hotnews"] integerValue] == 1 && dic[@"flashID"] != NULL && [dic[@"is_selected"] integerValue] == 1){
//            [manager addKeyValuePairWithKey:
//             [NSString stringWithFormat:@"%@", dic[@"flashID"]] value: [NSString stringWithFormat:@"%@", notificationDic[@"xg"][@"tpnsCollapseId"]]];
//        }
        // 静默消息处理
        [self parseSilentMessage:dic];
        NSString* acitonStr = [NSString stringWithFormat:@"%@", dic[@"action"]];
        if(acitonStr) {
            NSString* flashID = [NSString stringWithFormat:@"%@", dic[@"flashID"]];
//            if([acitonStr isEqualToString:@"PoPus"]) {
//                //  撤回弹窗
//                NSString *msgID = [[FlashIDMapManager shared] getMsgIDWithFlashID:flashID];
//                if(msgID){
//                    [self removeOneNotificationWithID:msgID];
//                }
//            }else if([acitonStr isEqualToString:@"upPus"]) {
//                //  更新弹窗
//                NSString *msgID = [[FlashIDMapManager shared] getMsgIDWithFlashID:flashID];
//                if(msgID){
//                    
//                }
//            }else 
                if([acitonStr isEqualToString:@"delFlash"]){
                //  删除快讯
                NSString *msgID = [[FlashIDMapManager shared] getMsgIDWithFlashID:flashID];
                if(msgID){
                    [self removeOneNotificationWithID:msgID];
                }
                NSNotification *notification = [[NSNotification alloc] initWithName:@"deleteFlash" object:nil userInfo:notificationDic];
                [[NSNotificationCenter defaultCenter] postNotification:notification];
            }else if([acitonStr isEqualToString:@"upFlash"]) {
                //  更新快讯
                NSNotification *notification = [[NSNotification alloc] initWithName:@"updateFlash" object:nil userInfo:notificationDic];
                [[NSNotificationCenter defaultCenter] postNotification:notification];
            }
        }
    }
    NSLog(@"receive notification dic: %@", notificationDic);
}

/// 统一点击回调
/// @param response 如果iOS 10+/macOS 10.14+则为UNNotificationResponse，低于目标版本则为NSDictionary
/// 区分消息类型说明：xg字段里的msgtype为1则代表通知消息,msgtype为9则代表本地通知
- (void)xgPushDidReceiveNotificationResponse:(nonnull id)response withCompletionHandler:(nonnull void (^)(void))completionHandler {
    NSLog(@"[TPNS Demo] click notification");
    NSDictionary *notificationDic = nil;
    if ([response isKindOfClass:[UNNotificationResponse class]]) {
        /// iOS10+消息体获取
        NSLog(@"notification dic: %@", ((UNNotificationResponse *)response).notification.request.content.userInfo);
        notificationDic = ((UNNotificationResponse *)response).notification.request.content.userInfo;
        
        if (notificationDic[@"custom"]) {
            NSData *jsonData = [notificationDic[@"custom"] dataUsingEncoding:NSUTF8StringEncoding];
                NSError *err;
                NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData
                                                                    options:NSJSONReadingMutableContainers
                                                                      error:&err];
            
            [self aichandleRemoteNotification:dic withApplication:[UIApplication sharedApplication] isClick:true];

        } else {
            [self aichandleRemoteNotification:notificationDic withApplication:[UIApplication sharedApplication] isClick:true];
        }
    } else if ([response isKindOfClass:[NSDictionary class]]) {
        /// <IOS10消息体获取
        NSLog(@"notification dic: %@", response);
    }
    completionHandler();
}

/// 灰度接口,判断是否需要注册TPNS
- (void) registerTPNS {
    NSMutableDictionary *parameters = [NSMutableDictionary dictionary];
    parameters[@"userid"] = [UserManager shareManager].user.userId;
    [[AICHttpManager sharedManager] POST:@"/api/upgrade/warning/config/get" parameters:parameters progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObj) {
        if ([responseObj[@"success"] boolValue]) {
            NSString *alive = responseObj[@"data"][@"alive"];
            if ([alive isEqualToString:@"tpns"]) {
                [MeCostant setIsTpns:true];
                if ( [[XGPushTokenManager defaultTokenManager] xgTokenString].length > 0) {
                    [self bindTPNSToken];
                } else {
                    [[XGPush defaultManager] startXGWithAccessID:TPNS_ACCESS_ID accessKey:TPNS_ACCESS_KEY delegate:self];
                }
            } else if ([alive isEqualToString:@"jpush"]) {
                if (self.isTPNSRegistSuccess) {
                    [[XGPush defaultManager] stopXGNotification];
                }
                [MeCostant setIsTpns:false];
            }
        } else {
            if (_reconnectTime < 3) {
                _reconnectTime += 1;
                [self registerTPNS];
            }
        }
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        if (_reconnectTime < 3) {
            _reconnectTime += 1;
            [self registerTPNS];
        }
    }];
}

- (void)registerAPN {
    UNUserNotificationCenter *center = [UNUserNotificationCenter currentNotificationCenter];
    [center requestAuthorizationWithOptions:(UNAuthorizationOptionAlert + UNAuthorizationOptionSound) completionHandler:^(BOOL granted, NSError * _Nullable error) {
        
    }];
}

// 移除某一个指定的通知
- (void)removeOneNotificationWithID:(NSString *)noticeId {
    NSLog(@"撤回通知");
    UNUserNotificationCenter *center = [UNUserNotificationCenter currentNotificationCenter];
    [center removeDeliveredNotificationsWithIdentifiers:@[noticeId]];
}

- (void)application:(UIApplication *)application didReceiveRemoteNotification:(NSDictionary *)userInfo {
    [self aichandleRemoteNotification:userInfo withApplication:application isClick:false];
}

- (void)application:(UIApplication *)application didReceiveLocalNotification:(UILocalNotification *)notification
{
    NSDictionary *dict = notification.userInfo[@"rc"];
    if (dict) {
        //        [self gotoBifuGroup:dict];
    } else {
        [self aichandleLocalNotification:notification.userInfo withApplication:application isClick:false];
    }
}

- (void)application:(UIApplication *)application didReceiveRemoteNotification:(NSDictionary *)userInfo fetchCompletionHandler:(void (^)(UIBackgroundFetchResult))completionHandler {
    [self aichandleRemoteNotification:userInfo withApplication:application isClick:false];
    // IOS 7 Support Required
    completionHandler(UIBackgroundFetchResultNoData);
}

- (UIInterfaceOrientationMask)application:(UIApplication *)application supportedInterfaceOrientationsForWindow:(UIWindow *)window {
    if (self.launchScreen) {
        return UIInterfaceOrientationMaskLandscapeRight;
    }
    return UIInterfaceOrientationMaskPortrait;
}

- (void)application:(UIApplication *)application didFailToRegisterForRemoteNotificationsWithError:(NSError *)error {
    [Bugly reportExceptionWithCategory:3
                                  name:@"远程通知注册失败"
                                reason:[error localizedDescription]
                             callStack:[NSArray array]
                             extraInfo:@{
                                         @"userID": [[UserManager shareManager] safeIdentifier],
                                         @"errorInfo": [NSString stringWithFormat:@"%@", error.userInfo]
                                         }
                          terminateApp:false];
}

#pragma mark - 初始化

//- (void)configShareSDK {
//    [AICShare registerShareSDK];
//}

/// 配置腾讯推送相关处理
- (void)configXGPushWithOptions:(NSDictionary *)launchOptions {
    [XGPush defaultManager].launchOptions = [launchOptions mutableCopy];
    [[XGPush defaultManager] setEnableDebug:YES];
    /// @note TPNS SDK1.2.7.1+ 新加坡服务点接入
    [[XGPush defaultManager] configureClusterDomainName:@"tpns.sgp.tencent.com"];
}


/// 配置bugly错误上报的处理
- (void)configBugly {
    BuglyConfig *config = [[BuglyConfig alloc] init];
    config.channel = aic_appChannelId;
    config.applicationGroupIdentifier = kWidgetAppGroupName;
    config.unexpectedTerminatingDetectionEnable = YES;
    
    if (aic_appType == AICAPPTypeTest) {
        config.blockMonitorEnable = YES;
    }
    
    [BuglyLog initLogger:BuglyLogLevelVerbose consolePrint:YES];
    [Bugly startWithAppId:aic_buglyAppId
#ifdef DEBUG
        developmentDevice:false
#endif
                   config:config];
    
    let userId = [UserManager shareManager].safeIdentifier;
    
    [Bugly setUserIdentifier:userId];
    
//#if AIC_ENABLE_PRO
//
//
//    [JPEngine startEngine];
//
//    //捕获 JSPatch 异常并上报
//    [JPEngine handleException:^(NSString *msg) {
//        NSException *jspatchException = [NSException exceptionWithName:@"Hotfix Exception" reason:msg userInfo:nil];
//        [Bugly reportException:jspatchException];
//    }];
//
//    [self checkRemotePatch];
//#endif
}


/**
 检测远程补丁
 */
//- (void)checkRemotePatch {
//#if AIC_ENABLE_PRO
//    //是否使用本地测试
//    BOOL localTest = false;
//
//
//#ifndef DEBUG
//    //正式环境为 false 勿改
//    localTest = false;
//#endif
//
//    void(^runJSAtPathAction)(NSString *) = ^(NSString *patchFile) {
//        //执行补丁加载并上报激活状态
//        if ([[NSFileManager defaultManager] fileExistsAtPath:patchFile] &&
//            [JPEngine evaluateScriptWithPath:patchFile] != nil) {
//            NSLog(@"\n\n------热更新成功------\n\n");
//            if (localTest == false) {
//                [[BuglyMender sharedMender] reportPatchStatus:BuglyHotfixPatchStatusActiveSucess];
//            }
//        }
//        else {
//            NSLog(@"\n\n------热更新失败------\n\n");
//            if (localTest == false) {
//                [[BuglyMender sharedMender] reportPatchStatus:BuglyHotfixPatchStatusActiveFail];
//            }
//        }
//    };
//
//
//    if (localTest) {
//        runJSAtPathAction([[NSBundle mainBundle] pathForResource:@"main" ofType:@"js"]);
//        return;
//    }
//
//    //检测补丁策略
//    [[BuglyMender sharedMender] checkRemoteConfigWithEventHandler:^(BuglyHotfixEvent event, NSDictionary *patchInfo) {
//        AICHotPatchModel *model = [AICHotPatchModel modelWithJSON:patchInfo];
//        //有新补丁或本地补丁状态正常
//        //因为Bugly会加载本地旧版本的补丁 所以在补丁描述中需要指定buildId e.g {"buildId":"1"}
//        if (/*[model.descModel.buildId isEqualToString:[UIApplication sharedApplication].appBuildVersion] && */(event == BuglyHotfixEventPatchValid || event == BuglyHotfixEventNewPatch)) {
//            //获取本地补丁路径
//            NSString *patchDirectory = [[BuglyMender sharedMender] patchDirectory];
//            if (patchDirectory) {
//                //指定执行的 js 脚本文件名
//                NSString *patchFileName = @"main.js";
//                NSString *patchFile = [patchDirectory stringByAppendingPathComponent:patchFileName];
//                //执行补丁加载并上报激活状态
//                runJSAtPathAction(patchFile);
//            }
//        }
//    }];
//#endif
//}


- (void)configUM {
    if (aic_appType != AICAPPTypeTest) {
        UMConfigInstance.appKey = @"5a0281f1734be469840000f4";
        UMConfigInstance.channelId = aic_appChannelId;
        
        [MobClick setAppVersion:[UIApplication sharedApplication].appVersion];
        [MobClick setCrashReportEnabled:false];
        [MobClick startWithConfigure:UMConfigInstance];
        
    }
}

- (void)configSentry {
//    [SentrySDK startWithConfigureOptions:^(SentryOptions *options) {
//            options.dsn = @"https://a3c017fe8c63a5a22d15198d9d769bb5@119.91.245.108/6";
//            options.debug = NO;
//            
//            // Enable tracing to capture 100% of transactions for tracing.
//            // Use 'options.tracesSampleRate' to set the sampling rate.
//            // We recommend setting a sample rate in production.
//            options.tracesSampleRate = @0.001;
//            options.profilesSampleRate = @0.001;
//            options.enableTracing = YES;
//            //options.enableCaptureFailedRequests = YES;
////            options.sendDefaultPii = YES;
////            options.attachStacktrace = YES;
//        }];
//    if ([UserManager shareManager].isLogin) {
//        SentryUser *user = [[SentryUser alloc] initWithUserId:[UserManager shareManager].user.identifier];
//        [SentrySDK setUser:user];
//    }
}

- (void)configOther {
    [[AICTickerDataBase share] updateDB];

    [[UserManager shareManager] checkUserInfoExpDate];
    [[UserManager shareManager] requestUserInfoWithCompletion:^(UserModel * _Nullable model, NSError * _Nullable error) {
        [[AICHttpManager sharedManager] changeBaseURL];
    }];
    [[UserManager shareManager] updatePhoneNumber];
    
    [TradeModule setup];
    
    [[TimerHelper shareInstanceForOC] startTimerWithIdentifier:@"kCheckTickerTabConfigTimer" interval:60.0 * 4 target:self selector:@selector(checkTickerTabConfig) isFire:false];
    
    [[TimerHelper shareInstanceForOC] startTimerWith:@"requestPriceWarningTags" interval:60 * 30 completion:^{
//        [[UserManager shareManager] updateGlobalConfig];
        [PriceWarning requestPriceWarningTags];
        [TPNSTag updateTags];
    } isFire:false];
    
    /// 轮询tpns灰度接口
    [[TimerHelper shareInstanceForOC] startTimerWith:@"requestTPNS" interval:60 * 30 completion:^{
        [self registerTPNS];
    } isFire:false];
    
    if (@available(iOS 11.0, *)) {
        [[UIScrollView appearance] setContentInsetAdjustmentBehavior:UIScrollViewContentInsetAdjustmentNever];
        [[UITableView appearance] setEstimatedRowHeight:0];
        [[UITableView appearance] setEstimatedSectionFooterHeight:0];
        [[UITableView appearance] setEstimatedSectionHeaderHeight:0];
    }
    
    if (@available(iOS 15.0, *)) {
        [[UITableView appearance] setSectionHeaderTopPadding:CGFLOAT_MIN];
    }
    
//    [PriceWarning requestPriceWarningTags];
    
    //检查地区配置
    [AICTickerRequestOperation.share updateMarketConfigWithCompletion:^(BOOL flag) {
        NSLog(@"%@", flag ? @"=== 地区配置更新成功" : @"=== 地区配置更新失败");
    }];
    
    [CandleChartModule setup];
}

/// 配置听云网络监控的处理
- (void)configTingyun {
    [NBSAppAgent setStartOption:NBSOption_Net];
    [NBSAppAgent startWithAppID:@"1d8f03a893844a509e20d59e84e950e7" channelId:aic_appChannelId];
}

//- (NSString *)replaceDomain:(NSString *)domain {
//    //httpdns开关
//    if (self.isShouldOpenHttpDNS == false) {
//        return domain;
//    }
//
//    HttpDnsService *httpdns = [HttpDnsService sharedInstance];
//    NSArray *trustDomains = httpdns.aic_trustDomain;
//
//    //将ip转换回原来的https域名进行认证
//    __block NSString *host = nil;
//    [trustDomains enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
//        NSArray *ips = [httpdns getIpsByHostAsync:obj];
//        if (ips && [ips containsObject:domain]) {
//            host = obj;
//            *stop = YES;
//        }
//    }];
//
//    if (host.length) {
//        return host;
//    }
//
//    return domain;
//}

//- (NSURLRequest *)requestBySerializingRequest:(NSURLRequest *)request withParameters:(id)parameters error:(NSError *__autoreleasing  _Nullable *)error {
//
//    //当前请求方式错误率较高 则更换另一种方式
//    if (aic_requestErrorRate() > HTTP_REQUEST_ERROR_RATE_THRESHOLD) {
//        _tryOpenHttpDNS = !_tryOpenHttpDNS;
//    }
//
//    if (self.isShouldOpenHttpDNS == false) {
//        return request;
//    }
//
//    NSURLRequest *aRequest = request;
//
//    NSString *host = request.URL.host;
//    if ([[[AICHttpDNSManager sharedManager] ipMapDic] objectForKey:host] == NULL) {
//        HttpDnsService *httpdns = [HttpDnsService sharedInstance];
//        NSString *ip = [httpdns getIpByHostAsync:host];
//        [[[AICHttpDNSManager sharedManager] ipMapDic] setObject:ip forKey:host];
//    }
//    NSString *ip = [[[AICHttpDNSManager sharedManager] ipMapDic] objectForKey:host];
//    if (ip.length) {
//        NSMutableURLRequest *mRequest = nil;
//
//        if ([aRequest isKindOfClass:[NSMutableURLRequest class]]) {
//            mRequest = (NSMutableURLRequest *)aRequest;
//        }
//        else {
//            mRequest = [aRequest mutableCopy];
//        }
//
//        NSURL *replaceURL = ({
//            // 通过HTTPDNS获取IP成功，进行URL替换和HOST头设置
//            NSURLComponents *components = [NSURLComponents componentsWithURL:request.URL resolvingAgainstBaseURL:NO];
//            components.host = ip;
//            components.URL;
//        });
//        mRequest.URL = replaceURL;
//        [mRequest setValue:host forHTTPHeaderField:@"host"];
//        return mRequest;
//    }
//    
//    return aRequest;
//}

//- (void)syncTimeCompletionWithLocalTime:(NSTimeInterval)localTime correctTime:(NSTimeInterval)correctTime {
//    [[HttpDnsService sharedInstance] setAuthCurrentTime:(NSUInteger)(correctTime / 1000.0)];
//}


#pragma mark - UIAlertViewDelegate

- (void)alertView:(UIAlertView *)alertView didDismissWithButtonIndex:(NSInteger)buttonIndex {
    if ([alertView isEqual:_remoteAlertView] && _notificationUserInfo) {
        if (buttonIndex == 1) {
            _coin = [self.notificationUserInfo objectForKey:@"c"];
            _market = [self.notificationUserInfo objectForKey:@"m"];
            [self gotoDetailPage];
        }
        [[NSNotificationCenter defaultCenter] postNotificationName:AICRemoteAlertPushReach object:self.notificationUserInfo];
        [[SoundManager sharedManager] stopMusic:YES];
        self.notificationUserInfo = nil;
    }
}

#pragma mark - push

+ (void)saveTPNSConfig {
}

//TPNS token上传
-(void)bindTPNSToken
{
    NSMutableDictionary *parameters = [NSMutableDictionary dictionary];
    NSString *tpnsStr;
    #if (AIC_APP_TYPE == 3)
        BOOL openAPITest = [[NSUserDefaults standardUserDefaults] boolForKey:@"aic_openAPITestEnv"];
        tpnsStr = openAPITest ? @"tpns_ios_test" : @"tpns_ios";
    #else
        tpnsStr = @"tpns_ios";
    #endif
    parameters[@"tpns"] = tpnsStr;
    parameters[@"tpns_id"] = [[XGPushTokenManager defaultTokenManager] xgTokenString];
    parameters[@"device"] = @"ios";
    parameters[@"version"] = [UIApplication sharedApplication].appVersion;
    parameters[@"pushVersion"] = AICPushVersion;
    parameters[@"ewsBigOrderVersion"] = AICBigOrderVersion;
    parameters[@"ewsMarketSingleVersion"] = AICMarketSingleVersion;
    parameters[@"ewsIndexVersion"] = AICMarketIndexVersion;
    parameters[@"ewsMarketCmpVersion"] = AICMarketCmpVersion;
    parameters[@"ewsSmwVersion"] = AICStrategyVersion;
    parameters[@"ewsSignalVersion"] = AICSignalVersion;
    parameters[@"ewsScriptVersion"] = AICEwsScriptVersion;
    parameters[@"lan"] = [SystemConfig serverAllEnumStringValue:[SystemConfig userInterfaceLanguage]];
    [parameters safeSetObject:[UserManager shareManager].user.userId forKey:@"userid"];
    [[AICHttpManager sharedManager] POST:@"/api/upgrade/warning/config/save" parameters:parameters progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObj) {
        if ([responseObj[@"success"] boolValue]) {
        }
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
    }];
    
    if (self.isTPNSRegistSuccess) {
        [TPNSTag updateTags];
    }
}

//远程推送：预警、行情、币区势(消息、聊天)、DACA(消息、聊天)、资讯
- (void)aichandleRemoteNotification:(NSDictionary *)userInfo withApplication:(UIApplication *)application isClick:(BOOL)isClick {
    [self trackingNotice:userInfo];
    //币区势推送
    if ([userInfo[@"vip"] integerValue] == 1) {
        NSString *content = userInfo[@"aps"][@"alert"];
        NSString *title = userInfo[@"tag"];
        if (content) {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self showBifuAlertView:content title:title];
            });
        }
        
        return;
    }
    
    // 要闻推送
    if (userInfo[@"aid"]) {
        NSString *aid = userInfo[@"aid"];
        
        NSString *alert = userInfo[@"aps"][@"alert"];
        
        if (application.applicationState == UIApplicationStateActive) {
            [self showNewsPushAlertView:alert];
        } else {
            InformationItem *item = [[InformationItem alloc] init];
            item.articelId = aid;
            item.articleId1 = aid;
            item.describe = alert;
            
            [self showInformationDetailViewControllerWithItem:item];
        }
        return;
    }
    
    // 快讯
    if ([userInfo[@"hotnews"] integerValue] == 1) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            NSString *flashID = userInfo[@"flashID"];
            if(flashID != NULL && [flashID isKindOfClass:NSString.class]) {
                NSInteger flashId = [userInfo[@"flashID"] integerValue];
                if (isClick) {
                    [self pushToastView:flashId];
                }
            }
        });
        return;
    }
    
    // 资讯
    if ([userInfo[@"news"] integerValue] == 1) {        
        if (![userInfo[@"aps"][@"alert"] isKindOfClass: [NSDictionary class]]) {
            return;
        }
        NSString *alert = userInfo[@"aps"][@"alert"][@"body"];
        NSString *title = userInfo[@"aps"][@"alert"][@"title"];
        NSInteger type = [userInfo[@"type"] integerValue];
        NSString *aid;
        if (type == 1) {
            aid = [NSString stringWithFormat:@"%@", userInfo[@"news_id"]];
        }
        
        if (application.applicationState != UIApplicationStateActive && type == 1) {
            InformationItem *item = [[InformationItem alloc] init];
            item.articelId = aid;
            item.articleId1 = aid;
            item.describe = alert;
            
            [self showInformationDetailViewControllerWithItem:item];

        } else {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self showNewsAlertView:title content:alert inforID:aid];
            });
        }
        
        return;
    }
    
    //  处理快讯删除、撤回、修改
    
   
    
    // 推送类型
    NSInteger alertType = [userInfo[@"alert_type"] intValue];
    
    // 聊天室
    if(alertType == 50){
        if(isClick){
            [self parseChatRoom:userInfo application:application];
        }
        [[ChatMessageCount shared] updateCountWithCount:1];
    }else if(alertType == 30){ //策略云详情
        if(isClick){
            [HomePageConstant gotoStrategyCloud];
            //  延迟0.3s 跳转到策略详情
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self navigateToStrategyDetail:userInfo[@"type"] strategyId:userInfo[@"strategy_id"] strategyName:userInfo[@"strategy_name"]];
            });
        }
    } else { // 其他预警
        [self parseWarning:userInfo application:application isClick:isClick];
    }
}

//-(void)getFlashCacheLimit {
//    [[AICHttpManager sharedManager] POST:@"/api/upgrade/hotFlash/cacheLimit" parameters:nil progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObj) {
//        if([responseObj[@"success"] boolValue]) {
//            NSString *limitStr = responseObj[@"data"][@"limit"];
//            if(limitStr){
//                [[NSUserDefaults standardUserDefaults] setObject:limitStr forKey:@"flashIDCacheLimit"];
//            }
//        }
//    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
//        
//    }];
//}


//本地推送：预警、行情、币区势、DACA、资讯
- (void)aichandleLocalNotification:(NSDictionary *)userInfo withApplication:(UIApplication *)application isClick:(BOOL)isClick {
    
    NSDictionary *extraDict0 = userInfo;
    if (userInfo[@"extras"]) {
        extraDict0 = userInfo[@"extras"];
    }
    
    if (extraDict0) {
        if ([extraDict0[@"vip"] integerValue] == 1) { //币区势推送
            NSString *content = userInfo[@"aps"][@"alert"];
            NSString *title = extraDict0[@"tag"];
            if (content) {
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [self showBifuAlertView:content title:title];
                });
            }
            return;
        }
        if ([extraDict0[@"hotnews"] integerValue] == 1) { //实时快讯
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                NSInteger flashId = [userInfo[@"flashID"] integerValue];
                if (isClick) {
                    [self pushToastView:flashId];
                }
            });
            return;
        }
        //        NSDictionary *dictrc = extraDict0[@"rc"];
        //        if (dictrc) {
        //            [self gotoBifuGroup:dictrc];
        //            return;
        //        }
    } else {
        if ([userInfo[@"vip"] integerValue] == 1) { //币区势推送
            NSString *content = userInfo[@"aps"][@"alert"];
            NSString *title = userInfo[@"tag"];
            if (content) {
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [self showBifuAlertView:content title:title];
                });
            }
            return;
        }
        //{"ios":{"sound":"default","extras":{"tag":"aicoin","hotnews":"1"},"badge":"1","alert":"周学东：对比特币平台划监管红线(中证报)"},"alert":"周学东：对比特币平台划监管红线(中证报)"}
        if ([userInfo[@"hotnews"] integerValue] == 1) { //实时快讯
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                NSInteger flashId = [userInfo[@"flashID"] integerValue];
                if (isClick) {
                    [self pushToastView:flashId];
                }
            });
            return;
        }
    }
    
    NSDictionary *extraDict = userInfo[@"extras"];
    NSString *alertId = @"";
    if ([extraDict isKindOfClass:[NSDictionary class]]) {
        _coin = extraDict[@"c"];
        _market = extraDict[@"m"];
        alertId = extraDict[@"id"];
    } else {
        _coin = userInfo[@"c"];
        _market = userInfo[@"m"];
        alertId = userInfo[@"id"];
    }
    
    if (alertId) {
        if (_cachedWarningNotes[alertId]) {
            return;
        }
        
        NSString *alert = userInfo[@"alert"];
        
        self.notificationUserInfo = userInfo;
        
        SettingConfig *config = [AICCacheManager shareManager].settingConfig;
        if ([config.appRunningVibrate isEqualToString:@"YES"]) {
            [[SoundManager sharedManager] playVibrate];
        }
        [[SoundManager sharedManager] stopMusic:true];
        if (application.applicationState == UIApplicationStateActive) {
            if ([config.appRunningSound isEqualToString:@"YES"]) {
                if ([config.appRunningSoundFileName isEqualToString:@"sound.wav"] || [config.appRunningSoundFileName isEqualToString:@"sound-defult.wav"]) {
                    [[SoundManager sharedManager] playMusic:config.appRunningSoundFileName loopTimes:[config.appRunningSoundLoopTimes integerValue]];
                } else {
                    [[SoundManager sharedManager] playSystemMusicWith:config.appRunningSoundFileName loopTimes:[config.appRunningSoundLoopTimes integerValue]];
                }
            }
            [self showReceivePushAlertViewWithAlertString:[NSString stringWithFormat:@"%@", alert]];
        } else {
            if (_coin && _market) {
                [self gotoDetailPage];
            }
        }
        [[NSNotificationCenter defaultCenter] postNotificationName:@"remote_price_push_reach" object:nil];
        if (alertId) {
            [_cachedWarningNotes setValue:@"1" forKey:alertId];
        }
    }
}

/**
 预警推送

 @param userInfo userInfo
 @param application application
 */
- (void)parseWarning:(NSDictionary *)userInfo application:(UIApplication *)application isClick:(BOOL)isClick {
    if (userInfo[@"alert_type"]) {
        NSString *alertid = [NSString stringWithFormat:@"%@", userInfo[@"id"]  ?: @""];
        if (_cachedWarningNotes[alertid]) {
            return;
        }
        
        id alert = userInfo[@"aps"][@"alert"];
        self.notificationUserInfo = userInfo;
        if (application.applicationState == UIApplicationStateActive) {
            dispatch_async(dispatch_get_main_queue(), ^{
                SettingConfig *config = [AICCacheManager shareManager].settingConfig;
                if ([config.appRunningVibrate isEqualToString:@"YES"]) {
                    [[SoundManager sharedManager] playVibrate];
                }
                
                if ([config.appRunningSound isEqualToString:@"YES"]) {
                    if ([config.appRunningSoundFileName isEqualToString:@"sound.wav"] || [config.appRunningSoundFileName isEqualToString:@"sound-defult.wav"]) {
                        // 剧烈波动 2
                        // "ews_type": "up", // 上涨或下跌 `up` / `down`
                        if([TickerPreAlertAlertPresentor getAlertTypeWithInfo:userInfo] == 2) {
                            NSString *ewsType = userInfo[@"ews_type"];
                            if ([ewsType isEqualToString:@"up"]){
                                [[SoundManager sharedManager] playMusic:@"alert_sound_up.wav" loopTimes:[config.appRunningSoundLoopTimes integerValue]];
                            }else {
                                [[SoundManager sharedManager] playMusic:@"sound.wav" loopTimes:[config.appRunningSoundLoopTimes integerValue]];
                            }
                        }else {
                            [[SoundManager sharedManager] playMusic:config.appRunningSoundFileName loopTimes:[config.appRunningSoundLoopTimes integerValue]];
                        }
                    }
                    else {
                        [[SoundManager sharedManager] playSystemMusicWith:config.appRunningSoundFileName loopTimes:[config.appRunningSoundLoopTimes integerValue]];
                    }
                }
            });
            
            NSDictionary *extras = userInfo[@"extras"];
            
            //兼容旧版
            if ([alert isKindOfClass:NSString.class] &&
                [extras isKindOfClass: [NSDictionary class]] &&
                extras[@"version"] == nil) {
                [self showReceivePushAlertViewWithAlertString:[NSString stringWithFormat:@"%@",(NSString *)alert]];
            }
            //新版预警样式
            else {
                [TickerPreAlertAlertPresentor receiveWithInfo:userInfo];
            }
        } else {
            [[SoundManager sharedManager] stopMusic:true];
            
            if (userInfo[@"c"] && userInfo[@"m"]) {
                if ([userInfo objectForKey:@"c"] && application.applicationState != UIApplicationStateActive) {
                    _coin = [userInfo objectForKey:@"c"];
                    _market = [userInfo objectForKey:@"m"];
                    [self gotoDetailPage];
                }
                //return;
            } else if (userInfo[@"db_key"]) {
                TickerPreAlertIndexNotificationDataModel *model = [[TickerPreAlertIndexNotificationDataModel alloc] initWithDictionary:userInfo];
                if (model.alertType == 5) {
                    let model = [[TickerIndexModel alloc] initWithCompatibilityDict:userInfo];
                    let nav = [UIViewController aic_getRootNavigationController];
                    [nav jumpToIndexDetailVCWithIndexModel:model];
                    //                aic_jumpToIndexDetailVC([[TickerIndexModel alloc] initWithCompatibilityDict:userInfo], [UIViewController aic_getRootNavigationController]);
                } else {
                    NSString *key = @"";
                    if(model.alertType == 1) {
                        key = userInfo[@"db_key_1"];
                    }else {
                        key = userInfo[@"db_key"];
                    }
                    if (model.alertType == 0 || model.alertType == 1) {     //  价格、价差预警
                        NSString *orderAmount = [NSString stringWithFormat:@"%@", userInfo[@"order_amount"]];
                        NSString *modeInfo = [NSString stringWithFormat:@"%@", userInfo[@"mode_info"]];
                        NSString *orderType = [NSString stringWithFormat:@"%@", userInfo[@"order_type"]];
                        NSString *tradeUnitRaw = [NSString stringWithFormat:@"%@", userInfo[@"trade_unit"]];
                        NSString *marketType = [NSString stringWithFormat:@"%@", userInfo[@"market_type"]];
                        NSString *tradeUnit = [NSString stringWithFormat:@"%@", @"USDT"];
                        if ([tradeUnitRaw isEqualToString:@"price"]) {
                            tradeUnit = @"USDT";
                        } else if ([tradeUnitRaw isEqualToString:@"amount"]) {
                            tradeUnit = userInfo[@"coin_show"] != nil ? userInfo[@"coin_show"] : @"USDT";
                        } else if ([tradeUnitRaw isEqualToString:@"Sheet"]) {
                            tradeUnit = @"张";
                        }

                        [TickerPreAlertAlertPresentor alertToOrderWithMarketType:marketType dbKey:key price:modeInfo quantity:orderAmount unit:tradeUnit orderType:orderType];
                    }else{
                        [self gotoDetailPageWith:key array:nil];
                    }
                }
            } else if (userInfo[@"db_key_1"]) {
                NSString *key = userInfo[@"db_key_1"];
                [self gotoDetailPageWith:key array:nil];
            } else {
                TickerPreAlertIndexNotificationDataModel *model = [[TickerPreAlertIndexNotificationDataModel alloc] initWithDictionary:userInfo];
                if (model.alertType == 5) {
                    let model = [[TickerIndexModel alloc] initWithCompatibilityDict:userInfo];
                    let nav = [UIViewController aic_getRootNavigationController];
                    [nav jumpToIndexDetailVCWithIndexModel:model];
                    //                aic_jumpToIndexDetailVC([[TickerIndexModel alloc] initWithCompatibilityDict:userInfo], [UIViewController aic_getRootNavigationController]);
                }
            }
        }
        
        
        [self sendFeedback];
        if (alertid.length > 0) {
            [_cachedWarningNotes setValue:@"1" forKey:alertid];
        }
    }else {
        if (userInfo[@"db_key"]) {
            NSString *key = userInfo[@"db_key"];
            if (isClick) {
                [self gotoDetailPageWith:key array:nil];
            }
        }
    }
    [[NSNotificationCenter defaultCenter] postNotificationName:@"kPlatformPreAlertVieNeedUpdataCustomSingal" object:nil];
}

/// 处理静默推送消息
- (void)parseSilentMessage:(NSDictionary *)userInfo{
    NSString *type = userInfo[@"type"];
    UIApplicationState state = [UIApplication sharedApplication].applicationState;
    if ([type isEqualToString:@"upIp"]) {
        // 是否在前台
        if(state == UIApplicationStateActive){
            // 子线程测速更新ip地址
            [AICSwiftTool runonGlobalQueueAsyncWithBlock:^{
                AICDomainModel *model = [AICDomainModel modelWithJSON:userInfo[@"data"]];
                [[AICDomainManager sharedManager] reSetNetWork:model];
            }];
        }
    }
}
/// 跳转聊天室
- (void)parseChatRoom:(NSDictionary *)userInfo application:(UIApplication *)application{
    NSMutableDictionary *_arguments = [userInfo mutableCopy];
    _arguments[@"alert_type"] = nil;
    UIViewController *nav = [UIViewController aic_getRootNavigationController];
    [MomentsConstant showChatRoomFrom:nav arguments:userInfo];
}

/// 跳转策略云详情
- (void)navigateToStrategyDetail:(NSString *)type strategyId:(NSString *)strategyId strategyName:(NSString *)strategyName {
    FlutterMethodChannel *channel = [FlutterMethodChannel methodChannelWithName:@"trade_remote_channel" binaryMessenger:self.tradeFlutterEngine];
    NSDictionary *params = @{
        @"type": type ?: @"",
        @"id": strategyId ?: @""
    };
    [channel invokeMethod:@"TradeRemote.toStrategyDetail" arguments:params];
}

- (void)trackingNotice:(NSDictionary*)userInfo{
    NSDictionary *extraDict0 = userInfo;
    id isTrack = extraDict0[@"isTrack"];
  
    if ([isTrack isKindOfClass:NSString.class]) {
        //  这里包含了 特色快讯_BTC突破、特色快讯_ETH突破、特色快讯_行情异动、特色快讯_爆仓数据、特色快讯_美股开/收盘、广告快讯、热搜榜
        NSString *_isTrack = (NSString*)isTrack;
        if ([_isTrack isEqualToString:@"1"]) {
            id trackTitle = extraDict0[@"trackTitle"];
            id flashID = extraDict0[@"flashID"];
            if ([trackTitle isKindOfClass:NSString.class]) {
                NSString *title = userInfo[@"aps"][@"alert"][@"title"];
                NSString *pieceTitle = [NSString stringWithFormat:@"%@_%@", title, flashID];
                if (title) {
                    [[ServiceAnalytics shared] contentInteractionWithName:trackTitle piece:pieceTitle target:nil];
                }
            }
        }
    }
    NSInteger alertType = [userInfo[@"alert_type"] intValue];
    if(alertType == 2){// 波动预警
        if (userInfo[@"db_key"]) {
            NSString *key = userInfo[@"db_key"];
            if ([[key uppercaseString] containsString:@"BTC"]) {
                [[ServiceAnalytics shared] contentInteractionWithName:@"波动预警" piece:@"BTC" target:nil];
            }else if ([[key uppercaseString] containsString:@"ETH"]){
                [[ServiceAnalytics shared] contentInteractionWithName:@"波动预警" piece:@"ETH" target:nil];
            }else {
                //  这个分支防止上面的判断异常
                TickerMarketListModel *market = [AICTickerDataBase.share fetchMarketListModelWithKey:key];
                if(market != nil && ([market.coin_show.uppercaseString isEqualToString:@"BTC"] || [market.coin_show.uppercaseString isEqualToString:@"ETH"])) {
                    [[ServiceAnalytics shared] contentInteractionWithName:@"波动预警" piece:market.coin_show.uppercaseString target:nil];
                }
            }
        }
    }else if (alertType == 20){//特色数据
        NSString *alertSubType = userInfo[@"alert_sub_type"];
        if ([alertSubType isEqualToString:@"otc_usdtcny_huobipro"]) {
            [[ServiceAnalytics shared] contentInteractionWithName:@"特色数据预警" piece:@"进离场人数" target:nil];
        }else if([alertSubType isEqualToString:@"lsur_btc_okex"]){
            [[ServiceAnalytics shared] contentInteractionWithName:@"特色数据预警" piece:@"后市信心" target:nil];
        }
    }
   
}
#pragma mark - 私有方法

- (void)pushToastView:(NSInteger)flashID {
    if (alertController != nil && alertController.presentationController != nil) {
        [alertController dismissViewControllerAnimated:false completion:^{
            if (flashID) {
                AICBaseNavigationController *nav = [UIViewController aic_getRootNavigationController];
                alertController = [[RealtimeNewsAlertViewController alloc] initWithFlashId:flashID];
                [nav presentViewController:alertController animated:true completion:nil];
            }
        }];
    } else {
        AICBaseNavigationController *nav = [UIViewController aic_getRootNavigationController];
        alertController = [[RealtimeNewsAlertViewController alloc] initWithFlashId:flashID];
        [nav presentViewController:alertController animated:true completion:nil];
    }
}

- (void)handleOpenURL:(NSURL *)url {
    if ([url.scheme compare:aic_URLScheme options:NSCaseInsensitiveSearch] == NSOrderedSame) {
        NSDictionary *dict = [self URLQueryParameters:url];
        if ([dict[@"event"] isEqualToString:@"gotoDetail"]) {
//            _coin = dict[@"coin"];
//            _market = dict[@"market"];
//            _coinTitle = dict[@"coinTitle"];
//            _marketTitle = dict[@"marketTitle"];
            NSString *marketList = [dict[@"marketList"] stringByReplacingOccurrencesOfString:@"_replace_" withString:@":"];
            NSMutableArray <NSString *> *array = [[marketList componentsSeparatedByString:@"|"] mutableCopy];
//            [array removeLastObject];
            
            // 点击锁屏行情埋点
            if ([dict[@"lockWidgetType"] isEqualToString:@"rectPrice"]) {
                [AppAnalytics.shared trackWithEventWithCategory:@"个人_金刚区" action:@"实时盯盘_锁屏小组件" name:@"点击“最新价”"];
            } else if ([dict[@"lockWidgetType"] isEqualToString:@"baseCircle"]) {
                [AppAnalytics.shared trackWithEventWithCategory:@"个人_金刚区" action:@"实时盯盘_锁屏小组件" name:@"点击“涨跌幅”"];
            }
            
            NSString *key = [dict[@"key"] stringByReplacingOccurrencesOfString:@"_replace_" withString:@":"];
            if(key){
                if (!marketList) {
                    array = [@[key] mutableCopy];
                }
                [self gotoDetailPageWith:key array:array];
            }
        }
        else if ([dict[@"event"] isEqualToString:@"gotoWidget"]) {
            WidgetDataSelectedViewController *vc = [[WidgetDataSelectedViewController alloc] initWithType:WidgetDataSelectedViewControllerTypeWidget];
            AICBaseNavigationController *nav = [[AICBaseNavigationController alloc] initWithRootViewController:vc];
            [self presentViewController:nav];
        }
        else if ([dict[@"event"] isEqualToString:@"gotoLockWidget"]) {
            [AppAnalytics.shared trackWithEventWithCategory:@"个人_金刚区" action:@"实时盯盘_锁屏小组件" name:@"点击“盯盘列表”"];
            WidgetDataSelectedViewController *vc = [[WidgetDataSelectedViewController alloc] initWithType:WidgetDataSelectedViewControllerTypeLockScreen];
            AICBaseNavigationController *nav = [[AICBaseNavigationController alloc] initWithRootViewController:vc];
            [self presentViewController:nav];
        }
        else if ([dict[@"event"] isEqualToString:@"CoinFee"]) {
            AICTabBarController *tabBarVC = self.rootVC;
            BOOL isopende = false;
            

            [tabBarVC.view showLoadingView];
            [AICSwiftTool delayWithSeconds:[self getDelayTime: isopende] doSomethingonMainQueue:^{
                [tabBarVC.view hideLoadingView];
                CoinFeesView *coinView = (CoinFeesView *)[UIView viewFromNibName:@"CoinFeesView"];
                UIWindow *keyWindow = [UIApplication sharedApplication].mainWindow;
                [coinView setFrame:keyWindow.bounds];
                [keyWindow addSubview:coinView];
                coinView.viewToast.transform = CGAffineTransformMakeScale(0.001, 0.001);
                [UIView animateWithDuration:0.25 animations:^{
                    coinView.viewToast.transform = CGAffineTransformIdentity;
                }];
            }];
        }
        else if ([dict[@"event"] isEqualToString:@"CoinCap"]) {
            NSInteger time = self.appDidFinishLaunch ? 0 : 1;
            [AICSwiftTool delayWithSeconds:time doSomethingonMainQueue:^{
                AICTabBarController *tabBarVC = self.rootVC;
                [tabBarVC selectedWithVcType:AICTabBarControllerItemTypeTicker completion:^{
                    [[NSNotificationCenter defaultCenter] postNotificationName:@"TickerSelectedMarketValue" object:nil];
                }];
            }];
        }
        else if ([dict[@"event"] isEqualToString:@"MineData"]) {
            let vc = [HomePageMiningDataViewController entrance];
            [self pushViewController:vc];
        }
        else if ([dict[@"event"] isEqualToString:@"BlockCheck"]) {
            let vc = [[BlockInfoVC alloc] init];
            [self pushViewController:vc];
        }
        else if ([dict[@"event"] isEqualToString:@"News"]) {
            AICTabBarController *tabBarVC = self.rootVC;
            
            [tabBarVC selectedWithVcType:AICTabBarControllerItemTypeDynamic completion:nil];
        }
        else if ([dict[@"event"] isEqualToString:@"Ticker"]) {
            AICTabBarController *tabBarVC = self.rootVC;
            [tabBarVC selectedWithVcType:AICTabBarControllerItemTypeOptional completion:nil];
        }
        else if ([dict[@"event"] isEqualToString:@"NewsDetail"]) {
            InformationItem *item = [[InformationItem alloc] init];
            item.articelId = dict[@"articelid"];
            item.articleId1 = dict[@"articelid"];
            item.describe = dict[@"describe"];
            
            [self showInformationDetailViewControllerWithItem:item];
        }
        else if ([dict[@"event"] isEqualToString:@"Moments"]) {
            [DynamicHelper goMomentsDetailWithId:dict[@"id"]];
        }
    } else if ([url.scheme compare:chat_URLScheme options:NSCaseInsensitiveSearch] == NSOrderedSame) {
        if ([url.absoluteString containsString:@"native"]) {
            [MeCostant goToNativeWithUrl:url.absoluteString];
        } else {
            NSMutableDictionary *param = [NSMutableDictionary dictionary];
            param[@"userid"] = UserManager.shareManager.safeUserID;
            [AICBaseHttpManager postAtChildThread:@"user/loadState" parameters: param completion:^(AICBaseResponseModel * _Nonnull res) {
                if (!self) return;
                dispatch_async(dispatch_get_main_queue(), ^{
                    if (res.success) {
                        MeChatRoomConfirmViewController *chatroom = [[MeChatRoomConfirmViewController alloc] initWithUrl: url.absoluteString type: @"app"];
                        [self pushViewController: chatroom];
                    } else {
                        AICLoginVC *vc = [[AICLoginVC alloc] init];
                        [vc setLoginCallBack:^{
                            MeChatRoomConfirmViewController *chatroom = [[MeChatRoomConfirmViewController alloc] initWithUrl: url.absoluteString type: @"app"];
                            [self pushViewController: chatroom];
                        }];
                        [self pushViewController: vc];
                    }
                });
            }];
        }
    }else if ([url.scheme compare:aic_URLScheme_Universal options:NSCaseInsensitiveSearch] == NSOrderedSame) {
        [NativeJumpTool jumpFromUrlStr:url.absoluteString viewController:nil trackFrom:@""];
    }
    
}

- (NSString *)getTimeStamp {
    return [NSString stringWithFormat:@"%.f",[[NSDate date] timeIntervalSince1970]];
}

- (void)sendFeedback {
    [[NSNotificationCenter defaultCenter] postNotificationName:@"remote_price_push_reach" object:nil];
//    NSMutableDictionary *mDict = [@{@"msgId":self.notificationUserInfo[@"_j_msgid"] ?: @"", @"time":[self getTimeStamp], @"userid": [[UserManager shareManager] safeUserID], @"msgType": @"1"} mutableCopy];
//    [[AICHttpManager sharedManager] POST:@"warnings/setReceiveTime" parameters:mDict  progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
//
//    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
//
//    }];
}


- (void)showReceivePushAlertViewWithAlertString:(NSString *)alertStr {
    [_remoteAlertView dismissWithClickedButtonIndex:0 animated:false];
    UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:NSLocalizedStringFromAICComment(@"预警提醒") message:alertStr delegate:self cancelButtonTitle:NSLocalizedStringFromAICComment(@"取消") otherButtonTitles:NSLocalizedStringFromAICComment(@"查看"), nil];
    _remoteAlertView = alertView;
    [alertView show];
}

- (void)showBifuAlertView:(NSString *)string title:(NSString *)title {
    [_remoteAlertView dismissWithClickedButtonIndex:0 animated:false];
    UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:title message:string delegate:self cancelButtonTitle:NSLocalizedStringFromAICComment(@"知道了") otherButtonTitles:nil];
    _remoteAlertView = alertView;
    [alertView show];
}

- (void)showNewsPushAlertView:(NSString *)string {
    [_remoteAlertView dismissWithClickedButtonIndex:0 animated:false];
    UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:NSLocalizedStringFromAICComment(@"要闻通知") message:string delegate:self cancelButtonTitle:NSLocalizedStringFromAICComment(@"知道了") otherButtonTitles:nil];
    _remoteAlertView = alertView;
    [alertView show];
}

- (void)showHotNewsAlertView:(NSString *)title content:(NSString *)content {
    [MomentsConstant showAlertViewWithTitle:title
                                    message:content
                          confirmCompletion:^{
                              [[NSNotificationCenter defaultCenter] postNotificationName:@"kHotNewsReachWithScrollTop" object:nil];
                          }
                           cancelCompletion:^{
                               [[NSNotificationCenter defaultCenter] postNotificationName:@"kHotNewsReachWithScrollNone" object:nil];
                           }
     ];
}

- (void)showNewsAlertView:(NSString *)title content:(NSString *)content inforID:(NSString*)inforID {
    [MomentsConstant showAlertViewWithTitle:title
                                    message:content
                          confirmCompletion:^{
                              [[NSNotificationCenter defaultCenter] postNotificationName:@"MomentsMoveToNewsPage" object:@1];
                              
                              if (inforID) {
                                  InformationItem *item = [[InformationItem alloc] init];
                                  item.articelId = inforID;
                                  item.articleId1 = inforID;
                                  item.describe = content;
                                  
                                  [self showInformationDetailViewControllerWithItem:item];
                                  return;
                              }
                          }
                           cancelCompletion:nil
     ];
}

- (void)gotoDetailPage {
    
    NSString *markListKey = [NSString stringWithFormat:@"%@:%@", _coin, _market];
    
    [[UIViewController aic_getRootNavigationController] jumpToTickerDetailVCWithFirstKey:markListKey listKeys:nil];
}

- (void)gotoDetailPageWith:(NSString *)marketListKey array:(NSArray *)array {
    AICBaseNavigationController *nav = [UIViewController aic_getRootNavigationController];
    
    __block BOOL isFind = false;
    
    NSArray <UIViewController *>*viewControllers = nil;
    
    if ([nav isKindOfClass:[AICBaseNavigationController class]]) {
        viewControllers = nav.aic_viewControllers;
    }
    else {
        viewControllers = nav.viewControllers;
    }
    
    [viewControllers enumerateObjectsUsingBlock:^(__kindof UIViewController * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj isKindOfClass:[TickerDetailNewViewController class]]) {
            isFind = true;
            *stop = true;
        }
    }];
    
    if (isFind) {
        let dic = [NSMutableDictionary dictionary];
        dic[@"firstKey"] = marketListKey;
        dic[@"listsKey"] = array;
        [NSNotificationCenter.defaultCenter postNotificationName:@"tickerDetailChangedList" object:nil userInfo:dic];
    }
    else {
        [nav jumpToTickerDetailVCWithFirstKey:marketListKey listKeys:array];
    }
}

- (NSDictionary *)URLQueryParameters:(NSURL *)URL {
    NSString *absoluteString = [URL absoluteString];
    
    // e.g: aicoinapplicationtest://
    NSString *queryString = [absoluteString componentsSeparatedByString:@"//"].lastObject;
    NSMutableDictionary *result = [NSMutableDictionary dictionaryWithCapacity:5];
    NSArray *parameters = [queryString componentsSeparatedByString:@"&"];
    for (NSString *parameter in parameters)
    {
        NSArray *parts = [parameter componentsSeparatedByString:@"="];
        NSString *key = [[parts objectAtIndex:0] stringByRemovingPercentEncoding];
        if ([parts count] > 1)
        {
            id value = [[parts objectAtIndex:1] stringByRemovingPercentEncoding];
            [result setObject:value forKey:key];
        }
    }
    return result;
}

- (CGFloat)getDelayTime:(BOOL)isopened {
    
    if (isopened) {
        return 0.5;
    }
    
    if (aic_isSmallDevice()) {
        return 3.5;
    }
    else if (aic_isIphoneX()) {
        return 1.5;
    }
    else {
        return 2.5;
    }
}


/**
 app显示根控制器
 */
- (void)applicationDidFinishShowRootViewController {
    
    //尝试连接watch 连接成功会自动同步配置
    [[AICCacheManager shareManager] tryConnectAppleWatch];
    
    //触发数据同步到通知栏
    [[AICCacheManager shareManager] syncDataToWidget];
    
//    [[CheckVersion share] checkWithManual:false];
//    [[CheckVersion share] checkServerAvailable];
//    [TickerMethod requestRateWithCompletion:^{
//        
//    }];
    [[[TickerTempDataPool sharedInstance] rateModel] requestCurrenciesRateWithCompletion:^{
        
    }];
    
    
    
    
    SettingConfig *sc = [AICCacheManager shareManager].settingConfig;
    
    if ([sc.appIdelTimerSetting isEqualToString:@"YES"]) {
        [[UIApplication sharedApplication] setIdleTimerDisabled:true];
    }
    else {
        [[UIApplication sharedApplication] setIdleTimerDisabled:false];
    }
    
    //同步数据
    {
        NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
        NSUserDefaults *WKExtensionDefaults = [[NSUserDefaults alloc] initWithSuiteName:kWKExtensionAppGroupName];
        NSUserDefaults *widgetDefaults = [[NSUserDefaults alloc] initWithSuiteName:kWidgetAppGroupName];
        
        //第一次安装 将btc季度合约加入widget和watch
        if (![defaults objectForKey:@"version1.0.0.0"]) {
            NSString *data = @"btcquarter:okcoinfutures";
            
            if (![WKExtensionDefaults objectForKey:kWKExtensionAppGroupDataKey]) {
                [WKExtensionDefaults setObject:data forKey:kWKExtensionAppGroupDataKey];
            }
            
            [defaults setObject:@"run" forKey:@"version1.0.0.0"];
        }
        
        if (![defaults objectForKey:@"version1.0.0.1"]) {
            if (![widgetDefaults objectForKey:kWidgetAppGroupDataKey]) {
                [widgetDefaults setObject:@"btcquarter:okcoinfutures|" forKey:kWidgetAppGroupDataKey];
                [widgetDefaults synchronize];
                sc.isShowWidget = YES;
                [[CoreDataManager sharedManagedData] saveContext];
            }
            
            [defaults setObject:@"run" forKey:@"version1.0.0.1"];
        }
        
        // 行情小组件设置默认的key
        if (![widgetDefaults objectForKey:kWidgetAppGroupLockScreenDataKey]) {
            [widgetDefaults setObject:@"btcusdt:binance|ethusdt:binance|bnbusdt:binance|" forKey:kWidgetAppGroupLockScreenDataKey];
            [widgetDefaults synchronize];
            [[CoreDataManager sharedManagedData] saveContext];
        }
        
        [WKExtensionDefaults setObject:[UserManager shareManager].safeUserID forKey:kWKExtensionSingInUserIdKey];
        [widgetDefaults setObject:[UserManager shareManager].safeUserID forKey:kWidgetAppSingInUserIdKey];
    }
    
    [IQKeyboardManager sharedManager].enableAutoToolbar = false;
    [IQKeyboardManager sharedManager].enable = true;
    [IQKeyboardManager sharedManager].shouldResignOnTouchOutside = true;
    
    
//    [BaseSwiftMethod showAgreementAlertOn:self.window.rootViewController];
    
    self.appDidFinishLaunch = true;
}

- (void)checkTickerTabConfig {
    [AICTickerRequestOperation.share updateTickerConfigByLocalTimeWithCompletion:^(BOOL flag) {
        
    }];
}

#pragma mark 控制器
- (void)gotoSetAppleWatchItems {
    WidgetDataSelectedViewController *vc = [[WidgetDataSelectedViewController alloc] initWithType:WidgetDataSelectedViewControllerTypeAppleWatch];
    
    AICBaseNavigationController *nav = [[AICBaseNavigationController alloc] initWithRootViewController:vc];
    [self presentViewController:nav];
}

- (void)showInformationDetailViewControllerWithItem:(InformationItem *)item {
    InformationDetailViewController *vc = (InformationDetailViewController *)[UIViewController instanceWithStoryboardName:@"News" storyboardID:@"InformationDetailViewController"];
    vc.inforItem = item;
    [self pushViewController:vc];
}

- (void)pushViewController:(UIViewController *)vc {
    
    NSInteger time = self.appDidFinishLaunch ? 0 : 1;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(time * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        vc.hidesBottomBarWhenPushed = true;
        [[UIViewController aic_getRootNavigationController] pushViewController:vc animated:true];
    });
}

- (void)pushViewControllerWithStoryboardName:(NSString *)storyboardName storyboardID:(NSString *)storyboardID {
    UIViewController *vc = [UIViewController instanceWithStoryboardName:storyboardName storyboardID:storyboardID];
    [self pushViewController:vc];
}

- (void)presentViewController:(UIViewController *)vc {
    
    NSInteger time = self.appDidFinishLaunch ? 0 : 1;
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(time * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        UINavigationController *nav = [UIViewController aic_getRootNavigationController];
        [nav presentViewController:vc animated:true completion:nil];
    });
}

- (AICTabBarController *)rootVC {
    AICTabBarController *tabBarVC = (AICTabBarController *)[UIApplication sharedApplication].delegate.window.rootViewController;
    if ([tabBarVC isKindOfClass:[AICTabBarController class]]) {
        return tabBarVC;
    }
    return nil;
}


@end

