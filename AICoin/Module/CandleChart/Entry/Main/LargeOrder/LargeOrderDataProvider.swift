//
//  LargeOrderDataProvider.swift
//  AICoin
//
//  Created by CYQ on 2020/2/1.
//  Copyright © 2020 AICoin. All rights reserved.
//

import CandleChart
import UIKit

// 平台选项数据模型
struct PlatformOption {
    let optionId: Int
    let bindTpKey: String // 用于请求大单数据的symbol参数
    let filterKey: String // 用于在布局中匹配金额阈值
    var selected: Bool = false
}

class LargeOrderDataProvider: LargeOrderDataContext {
    var market: TickerMarketListModel? {
        didSet {
            task?.cancel()
            task = nil
            clearData()
        }
    }
    
    var orderDatas: [LargeOrderData] {
        return datas
    }
    
    private(set) var datas: [ChartLargeOrderData] = []
    
    // 多平台支持属性
    private var platformOptions: [PlatformOption] = []
    private var selectedPlatforms: [PlatformOption] {
        return platformOptions.filter { $0.selected }
    }
    private var platformOrderDatas: [String: [ChartLargeOrderData]] = [:]
    private var platformCoinConfigs: [String: CoinConfigModel] = [:]
    private var isMultiPlatformMode: Bool = false
    
    // 币种配置和回调
    var coinConfig = CoinConfigModel()
    var orderDatasDidUpdateBlock: (() -> Void)?
    
    var rate: Double? {
        return market?.rateForCandleChart
    }
    
    // 过滤配置
    var filter_key = ""
    var amount_down_bound = 0
    var trade_turnover_down_bound = 0
    var pendingOrder = 0
    var seniorFilter = 0
    var is_filter_trade_turnover = true
    var show_cancel = false
    var smartFilter = true

    /// 订阅socket对象
    private var subObs: TickerSubscribeObservation?
    private var task: URLSessionTask?
    
    // MARK: - 公共方法
    
    func reloadData() {
        subObs = nil
        requestOptions()
    }
    

    
    private func clearData() {
        datas.removeAll()
        platformOrderDatas.removeAll()
        platformCoinConfigs.removeAll()
        orderDatasDidUpdateBlock?()
        subObs = nil
    }
    
    // MARK: - 统一网络请求逻辑
    
    private func requestLargeOrder(symbol: String, amountDownBound: Int, tradeTurnoverDownBound: Int, completion: @escaping (String, [ChartLargeOrderData], CoinConfigModel?) -> Void) {
        let parameters: [String: Any] = [
            "userid": UserManager.share.safeUserID,
            "symbol": symbol,
            "smartFilter": smartFilter,
            "show_cancel": show_cancel,
            "amount_down_bound": amountDownBound,
            "pendingOrder": 86400000 - pendingOrder,
            "trade_turnover_down_bound": tradeTurnoverDownBound,
            "is_filter_trade_turnover": is_filter_trade_turnover
        ]
        
        AICHttpManager.shared.post("/api/upgrade/kline/depth-stat-new", parameters: parameters, progress: nil, success: { [weak self] task, data in
            guard let self = self, let data = data else { return }
            
            let json = JSON(data)
            let coinConf = json["data"]["coin_conf"]
            let coinConfig = CoinConfigModel.fromJSON(coinConf)
            
            // 解析订单数据
            let parsedData = self.parseOrderData(from: json)
            
            // 应用过滤逻辑
            let filteredData = parsedData.filter { order in
                return self.shouldIncludeOrder(
                    order, 
                    amountDownBound: amountDownBound, 
                    tradeTurnoverDownBound: tradeTurnoverDownBound
                )
            }
            
            completion(symbol, filteredData, coinConfig)
        }, failure: nil)
    }
    
    // MARK: - 统一订单过滤逻辑
    
    private func shouldIncludeOrder(_ order: ChartLargeOrderData, amountDownBound: Int, tradeTurnoverDownBound: Int) -> Bool {
        // 高级过滤选项1：合约未平仓
        if seniorFilter == 1 {
            let positionSubValue = order.positionSubTurnOver ?? order.positionSub
            if(order.tradeType == .futures && positionSubValue == nil) {
                return false
            }
        } 
        // 高级过滤选项2：未成交委托
        else if seniorFilter == 2 {
            if order.depthState != .unfilled {
                return false
            }
        }
        
        // 过滤委托持续时间
        if (order.holdTime ?? 0) < Double(86400000 - pendingOrder) {
            return false
        }
        
        // 过滤委托金额
        if (order.depthTurnover ?? 0) < Double(amountDownBound) {
            return false
        }
        
        // 过滤成交金额
        if (order.tradeTurnover ?? 0) < Double(tradeTurnoverDownBound) {
            return false
        }
        
        // 智能过滤
        if let filterState = order.filterState, filterState == 1, smartFilter {
            return false
        }
        
        return true
    }
    
    private func shouldRemoveOrder(_ order: ChartLargeOrderData) -> Bool {
        return !shouldIncludeOrder(
            order, 
            amountDownBound: amount_down_bound, 
            tradeTurnoverDownBound: trade_turnover_down_bound
        )
    }
    
    // MARK: - 统一订阅逻辑
    
    private func subscribeToLargeOrders(symbol: String, amountDownBound: Int, tradeTurnoverDownBound: Int, updateHandler: @escaping ([ChartLargeOrderData]) -> Void) {
        TickerSubscribeDataManager.shared.sub(bigOrders: symbol) { [weak self] orders in
            guard let self = self, !orders.isEmpty else { return }
            
            // 过滤并处理新收到的订单
            let filteredOrders = orders.filter { order in
                return self.shouldIncludeOrder(
                    order, 
                    amountDownBound: amountDownBound, 
                    tradeTurnoverDownBound: tradeTurnoverDownBound
                )
            }
            
            updateHandler(filteredOrders)
        }
    }
    
    private func request() {
        task?.cancel()
        guard UserManager.share.isProKLineUser, let key = market?.marketListKey else {
            clearData()
            return
        }
        
        task = nil // 清除当前任务引用
        
        requestLargeOrder(symbol: key, amountDownBound: amount_down_bound, tradeTurnoverDownBound: trade_turnover_down_bound) { [weak self] symbol, filteredData, config in
            guard let self = self, symbol == self.market?.marketListKey else { return }
            
            self.datas = filteredData
            
            if let config = config {
                self.coinConfig = config
                self.platformCoinConfigs[symbol] = config
            }
            
            self.orderDatasDidUpdateBlock?()
            self.subscribeData(with: key)
        }
    }
    
    // 防抖定时器
    private var debounceTimer: Timer?
    
    func requestOptions(){
        // 取消之前的定时器
        debounceTimer?.invalidate()
        
        // 创建新的定时器，1秒后执行请求
        debounceTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: false) { [weak self] _ in
            guard let self = self, UserManager.share.isProKLineUser, let key = self.market?.marketListKey else {
                return
            }
            
            let paramaters : [String : String] = [
                "symbol" : key,
                "version" : "v1"
            ]
            AICHttpManager.shared.post("/api/upgrade/kline/config/fd-options", parameters: paramaters, progress: nil) {[weak self] task, response in
                guard let self = self else { return }
                let json = JSON(response ?? "")
                let data = json["data"]["current_detail"]["ai-largeorder"]
                if let filterDetails = data["filter_key"].string {
                    self.filter_key = filterDetails
                }
                
                // 解析所有可能的平台选项
                if let optionsArray = json["data"]["option_detail"]["ai-largeorder"].array {
                    self.platformOptions = optionsArray.compactMap { option in
                        if let optionId = option["option_id"].int,
                           let bindTpKey = option["bind_tp_key"].string,
                           let filterKey = option["filter_key"].string {
                            return PlatformOption(optionId: optionId, bindTpKey: bindTpKey, filterKey: filterKey)
                        }
                        return nil as PlatformOption?
                    }
                }
                
                // 继续请求布局配置
                self.requestLayoutHash()
            }
        }
    }
    
    private func requestLayoutHash() {
        AICHttpManager.shared.post("/api/upgrade/kline/layout-list", parameters: nil, progress: nil) {[weak self] task, response in
            guard let self = self else { return }
            let json = JSON(response ?? "")
            let data = json["data"]
            if let dataList = data["list"].array {
                for item in dataList {
                    let model = LayoutListModel(json: item)
                    if model.isDefault == true {
                        requestDetail(hash: model.hashStr)
                        break;
                    }
                }
            }
        }
    }
    
    func requestDetail(hash: String){
        let params = [
            "hash" : hash
        ]
        AICHttpManager.shared.post("/api/upgrade/kline/layout", parameters: params, progress: nil) {[weak self] task, response in
            guard let self = self else { return }
            let json = JSON(response ?? "")
            let layoutDetail = json["data"]["layout_detail"]["extraOptions"]["chart0"]["ai-largeorder"]
            
            // 解析通用配置
            let input = layoutDetail["input"]
            self.smartFilter = input["smartFilter"].bool ?? self.smartFilter
            self.show_cancel = input["show_cancel_order"].int == 1
            self.pendingOrder = input["orderDuration"].int ?? self.pendingOrder
            self.seniorFilter = input["senior_filter"].int ?? 0
            
            // 获取委单金额阈值(单平台模式使用)
            if let amountDownBound = input[self.filter_key].int {
                self.amount_down_bound = amountDownBound
            }
            
            // 获取成交金额阈值(单平台模式使用)
            let tradeTurnover = layoutDetail["trade-turnover"]
            if let tradeDownBound = tradeTurnover[self.filter_key].int {
                self.trade_turnover_down_bound = tradeDownBound
            }
            
            // 检查是否有平台选项配置
            if let platformOptions = input["platform_options"].array, !platformOptions.isEmpty {
                // 多平台模式
                self.isMultiPlatformMode = true
                
                // 获取选中的平台ID，并转换为Int
                let selectedOptionIds = platformOptions.compactMap { $0.int }
                
                // 更新选中状态
                for i in 0..<self.platformOptions.count {
                    let isSelected = selectedOptionIds.contains(self.platformOptions[i].optionId)
                    self.platformOptions[i].selected = isSelected
                }
                
                // 请求多平台数据
                self.requestMultiPlatformData(layoutDetail: layoutDetail)
            } else {
                // 单平台模式（原有逻辑）
                self.isMultiPlatformMode = false
                self.request()
            }
        }
    }
    
    // 添加一个新的工具方法，用于从JSON解析订单数据
    private func parseOrderData(from json: JSON) -> [ChartLargeOrderData] {
        let value = json["data"]["list"].rawValue
        let mapping = json["data"]["mapping"].rawValue as? [String] ?? []
        var _list: [[String: Any]] = []
        
        if let list = value as? [[Any]] {
            for t in list {
                let dictionary = Dictionary(uniqueKeysWithValues: zip(mapping, t))
                _list.append(dictionary)
            }
        }
        var parsedData = [ChartLargeOrderData](from: _list) ?? []
        // 解析后处理已撤单订单
        if show_cancel {
            for i in 0..<parsedData.count {
                if let state = parsedData[i].depthState,(state == .canceled || state == .partialCanceled) {     
                    if parsedData[i].fakePrice == parsedData[i].depthPrice {
                        // 生成一个微小的差异，确保fake_price不等于depth_price
                        let smallDiff = parsedData[i].depthPrice * 0.0001 // 0.01%的偏差
                        let newFakePrice = parsedData[i].depthPrice - smallDiff
                        
                        // 修改数据源中的fake_price
                        parsedData[i].fakePrice = newFakePrice
                    }
                    // 设置显示状态
                    parsedData[i].showState = true
                }
            }
        }
            
        return parsedData
    }

    
    private func subscribeData(with key: String) {
        subscribeToLargeOrders(symbol: key, amountDownBound: amount_down_bound, tradeTurnoverDownBound: trade_turnover_down_bound) { [weak self] filteredOrders in
            guard let self = self, !self.isMultiPlatformMode else { return }
            
            // 处理单平台模式下的订阅数据更新
            for order in filteredOrders {
                if let index = self.datas.firstIndex(of: order) {
                    self.datas[index] = order
                } else {
                    self.datas.append(order)
                }
            }
            
            self.orderDatasDidUpdateBlock?()
        }
    }
    
    private func requestMultiPlatformData(layoutDetail: JSON) {
        // 清空之前的数据
        self.datas.removeAll()
        self.platformOrderDatas.removeAll()
        
        // 获取所有选中的平台
        var selectedPlatforms = self.selectedPlatforms

        // 检查当前K线交易对是否需要单独请求
        let currentSymbol = market?.marketListKey
        let currentSymbolInSelectedPlatforms = selectedPlatforms.contains { $0.bindTpKey == currentSymbol }

        // 如果当前K线交易对不在选中平台中，添加到请求列表
        if let currentSymbol = currentSymbol, !currentSymbolInSelectedPlatforms {
            // 为当前交易对创建一个临时平台选项
            let currentPlatform = PlatformOption(
                optionId: -1, // 特殊ID表示当前交易对
                bindTpKey: currentSymbol,
                filterKey: self.filter_key,
                selected: true
            )
            // 添加到临时请求列表
            selectedPlatforms.append(currentPlatform)
        }

        if selectedPlatforms.isEmpty {
            // 如果没有选中的平台，退回到使用原有逻辑
            self.isMultiPlatformMode = false
            self.request()
            return
        }
        
        // 为每个选中的平台请求数据
        for platform in selectedPlatforms {
            // 获取该平台的过滤阈值
            var amountDownBound = 0
            var tradeTurnoverDownBound = 0
            
            if platform.optionId == -1 {
                // 当前交易对使用单平台模式的阈值
                amountDownBound = self.amount_down_bound
                tradeTurnoverDownBound = self.trade_turnover_down_bound
            } else {
                // 其他平台使用各自的阈值
                if let value = layoutDetail["input"][platform.filterKey].int {
                    amountDownBound = value
                }
                
                if let value = layoutDetail["trade-turnover"][platform.filterKey].int {
                    tradeTurnoverDownBound = value
                }
            }
            // 请求该平台的大单数据
            self.requestLargeOrderForPlatform(platform: platform, amountDownBound: amountDownBound, tradeTurnoverDownBound: tradeTurnoverDownBound)
        }
    }
    
    private func requestLargeOrderForPlatform(platform: PlatformOption, amountDownBound: Int, tradeTurnoverDownBound: Int) {
        requestLargeOrder(symbol: platform.bindTpKey, amountDownBound: amountDownBound, tradeTurnoverDownBound: tradeTurnoverDownBound) { [weak self] symbol, filteredData, config in
            guard let self = self, self.isMultiPlatformMode else { return }
            
            // 更新平台币种配置
            if let config = config {
                self.platformCoinConfigs[symbol] = config
                
                // 如果是主平台，同时更新coinConfig
                if symbol == self.market?.marketListKey {
                    self.coinConfig = config
                }
            }
            
            // 存储该平台的数据
            self.platformOrderDatas[symbol] = filteredData
            
            // 合并所有平台数据
            self.mergePlatformData()
            
            // 订阅该平台的大单更新
            self.subscribePlatformData(platform: platform, amountDownBound: amountDownBound, tradeTurnoverDownBound: tradeTurnoverDownBound)
        }
    }
    
    // MARK: - 平台币种配置方法
    // 获取特定订单的币种配置
    func getCoinConfigForOrder(withId orderId: String) -> CoinConfigModel {
        guard let platformKey = findPlatformKeyForOrder(withId: orderId) else {
            return coinConfig
        }
        return platformCoinConfigs[platformKey] ?? coinConfig
    }

    // 根据订单ID查找所属平台
    func findPlatformKeyForOrder(withId orderId: String) -> String? {
        for (platformKey, orders) in platformOrderDatas {
            if orders.contains(where: { $0.id == orderId }) {
                return platformKey
            }
        }
        return market?.marketListKey // 默认返回主平台
    }

    // MARK: - 数据处理方法

    // 合并所有平台的数据
    private func mergePlatformData() {
        // 合并所有平台的数据
        self.datas = Array(platformOrderDatas.values).flatMap { $0 }
        
        // 通知更新
        self.orderDatasDidUpdateBlock?()
    }

    private func subscribePlatformData(
        platform: PlatformOption, 
        amountDownBound: Int, 
        tradeTurnoverDownBound: Int
    ) {
        subscribeToLargeOrders(symbol: platform.bindTpKey, amountDownBound: amountDownBound, tradeTurnoverDownBound: tradeTurnoverDownBound) { [weak self] filteredOrders in
            guard let self = self, self.isMultiPlatformMode else { return }
            
            // 当前平台的数据
            var currentPlatformData = self.platformOrderDatas[platform.bindTpKey] ?? []
            
            // 处理新订阅的订单
            for order in filteredOrders {
                if let index = currentPlatformData.firstIndex(of: order) {
                    currentPlatformData[index] = order
                } else {
                    currentPlatformData.append(order)
                }
            }
            
            // 更新该平台的数据
            self.platformOrderDatas[platform.bindTpKey] = currentPlatformData
            
            // 合并所有平台数据
            self.mergePlatformData()
        }
    }
}

class CandleChartLargeOrderConfiguration: CandleConfiguration<CandleChartLargeOrderConfigurationStorageModel> {
    
    static let shared = CandleChartLargeOrderConfiguration()
    
    private(set) var showLargeAIOrder: ObservableValue<Bool>! = nil
    
    private init() {
        super.init(name: "CandleChartStrategyConfiguration")
        showLargeAIOrder = ObservableValue(default: starageModel.showLargeAIOrder ?? true)
    }

}

struct CandleChartLargeOrderConfigurationStorageModel: CandleConfigurationModelType {
    
    var showLargeAIOrder: Bool?
}

// MARK: - 内部类定义

class CoinConfigModel {
    var coin = ""
    var coinType = ""
    var platform = ""
    var tradeType = ""
    var subTradeType = ""
    var marketLogo = ""
    var marketName = ""
    var currency = ""
    
    // 工厂方法，从JSON创建币种配置
    static func fromJSON(_ json: JSON) -> CoinConfigModel {
        let config = CoinConfigModel()
        config.coin = json["coin"].string ?? ""
        config.coinType = json["coin_type"].string ?? ""
        config.platform = json["platform"].string ?? ""
        config.tradeType = json["trade_type"].string ?? ""
        config.subTradeType = json["sub_trade_type"].string ?? ""
        config.marketLogo = json["market_logo"].string ?? ""
        config.marketName = json["market_name"].string ?? ""
        config.currency = json["currency"].string ?? ""
        return config
    }
}
