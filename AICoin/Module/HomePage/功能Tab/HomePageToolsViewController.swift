//
//  HomePageToolsViewController.swift
//  AICoin
//
//  Created by CYQ on 2019/12/2.
//  Copyright © 2019 AICoin. All rights reserved.
//  行情工具详情

import UIKit

class HomePageToolsViewController: AICBaseViewController {
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        if #available(iOS 9.0, *) {
            layout.sectionHeadersPinToVisibleBounds = true
        }
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = UIColor.baseTheme.current.cellBgColor
        collectionView.registerClassCell(class: HomePageToolsCollectionViewCell.self)
        collectionView.registerClassCell(class: SpecialToolsCollectionViewCell.self)
        collectionView.registerClassCell(class: TitleCollectionViewCell.self)
        collectionView.registerClassSupplementaryView(class: MenuView.self, supplementaryType: .header)
        collectionView.registerClassSupplementaryView(class: UICollectionReusableView.self, supplementaryType: .footer)
        collectionView.delegate = self
        collectionView.dataSource = self
        return collectionView
    }()
    
    private lazy var toolsController: AllToolsContainerSectionController = {
        let controller = AllToolsContainerSectionController()
        controller.collectionView = self.collectionView
        controller.selectedItemBlock = { [weak self] row in
            guard let self = self else { return }
            
            if let section = self.sectionControllers.firstIndex(of: self.toolsController) {
                let indexPath = IndexPath(row: row, section: section)
                if let attributes = self.collectionView.layoutAttributesForItem(at: indexPath) {
                    let offsetY = attributes.frame.minY - self.toolsController.headerHeight
                    self.collectionView.setContentOffset(CGPoint(x: 0, y: offsetY), animated: true)
                }
            }
        }
        return controller
    }()
    
    private var sectionControllers: [CollectionViewSectionController] = []

    override func viewDidLoad() {
        super.viewDidLoad()
        
        self.view.addSubview(self.collectionView)
        self.collectionView.snp.makeConstraints { make in
            make.edges.equalTo(0)
        }
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.reloadData()
    }
    
    private func reloadData() {
        var controllers = [CollectionViewSectionController]()
        
        if UserManager.share.isLogin {
            /// 取最近使用前8个
            let tools = HomePagePreferenceManager.shared.selectedTickerTools.prefix(8).map { $0 }
            if tools.isEmpty == false {
                controllers.append(ToolsContainerSectionController(title: "最近使用", tools: tools))
            }
        }
        
//        #if AIC_DISABLE_ADV
        ////        商店版
//        controllers.append(
//            ToolsContainerSectionController(
//                title: "特色功能",
//                toolsController: SpecialToolsSectionController(
//                    tools: [
//                        .聊天室, .预警中心, .组合K线, .对比K线, .买卖点记录,
        ////                        .角标行情,
//                        .通知中心行情, .跨期价差, .K线大师
//                    ]
//                )
//            )
//        )
//        #else
//        controllers.append(
//            ToolsContainerSectionController(
//                title: "特色功能",
//                toolsController: SpecialToolsSectionController(
//                    tools: [
//                        .聊天室, .指标胜率, .PRO版K线, .语音预警 , .预警中心, .组合K线, .对比K线,
//                        // .买卖点记录, .无聊,
        ////                        .角标行情,
//                        .通知中心行情, .跨期价差, .K线大师
//                    ]
//                )
//            )
//        )
//        #endif
        controllers.append(
            ToolsContainerSectionController(
                title: "特色功能",
                toolsController: SpecialToolsSectionController(
                    tools: HomePageConstant.defaultSpecialTools()
                ))
        )
        
        let contanierController = CollectionViewContainerSectionController(controllers: controllers)
        contanierController.collectionView = self.collectionView
        
        self.sectionControllers = [contanierController, self.toolsController]
        self.collectionView.reloadData()
    }
}

// MARK: - UICollectionViewDataSource, UICollectionViewDelegateFlowLayout

extension HomePageToolsViewController: UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return self.sectionControllers.count
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return self.sectionControllers[section].numberOfRows
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let controller = self.sectionControllers[indexPath.section]
        let cell = controller.cellForItem(at: indexPath)
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let controller = self.sectionControllers[indexPath.section]
        controller.didSelectItem(at: indexPath.row)
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let controller = self.sectionControllers[indexPath.section]
        return controller.sizeForItem(at: indexPath.row)
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumInteritemSpacingForSectionAt section: Int) -> CGFloat {
        let controller = self.sectionControllers[section]
        return controller.lineSpacing
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        let controller = self.sectionControllers[section]
        return controller.lineSpacing
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, insetForSectionAt section: Int) -> UIEdgeInsets {
        let controller = self.sectionControllers[section]
        return controller.inset
    }
    
    func collectionView(_ collectionView: UICollectionView, viewForSupplementaryElementOfKind kind: String, at indexPath: IndexPath) -> UICollectionReusableView {
        let controller = self.sectionControllers[indexPath.section]
        if kind == UICollectionView.elementKindSectionHeader {
            return controller.header(at: indexPath)
        } else {
            return controller.footer(at: indexPath)
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, referenceSizeForHeaderInSection section: Int) -> CGSize {
        let controller = self.sectionControllers[section]
        return CGSize(width: collectionView.width, height: controller.headerHeight)
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, referenceSizeForFooterInSection section: Int) -> CGSize {
        let controller = self.sectionControllers[section]
        return CGSize(width: collectionView.width, height: controller.footerHeight)
    }
    
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        let offsetY = scrollView.contentOffset.y + self.toolsController.headerHeight
        if let indexPath = collectionView.indexPathForItem(at: CGPoint(x: 0, y: offsetY)) {
            if let section = self.sectionControllers.firstIndex(of: toolsController), indexPath.section == section {
                self.toolsController.didScroll(to: indexPath)
            }
        }
    }
}

private extension HomePageToolsViewController {
    // MARK: - 视图

    class TitleCollectionViewCell: UICollectionViewCell {
        lazy var titleLabel: UILabel = {
            let label = UILabel()
            label.font = UIFont.aic_semiboldFont(withSize: 15)
            label.textColor = UIColor.baseTheme.current.cellTitleColor
            self.contentView.addSubview(label)
            label.snp.makeConstraints { make in
                make.centerY.equalTo(self.contentView.snp.centerY)
                make.left.equalTo(15)
            }
            return label
        }()
    }
    
    class SpecialToolsCollectionViewCell: UICollectionViewCell {
        let collectionView: UICollectionView = {
            let layout = UICollectionViewFlowLayout()
            layout.scrollDirection = .horizontal
            let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
            collectionView.backgroundColor = UIColor.baseTheme.current.cellBgColor
            collectionView.registerClassCell(class: SpecialToolItem.self)
            collectionView.showsHorizontalScrollIndicator = false
            return collectionView
        }()
        
        private let indicatorView: UIView = {
            let view = UIView()
            view.clipsToBounds = true
            view.backgroundColor = UIColor.home.current.changeValueProgressIndicatorNormalColor
            return view
        }()
        
        private let sliderView: UIView = {
            let view = UIView()
            view.backgroundColor = UIColor.home.current.changeValueProgressIndicatorSelectedColor
            return view
        }()
        
        private var observer: NSKeyValueObservation?
        
        override init(frame: CGRect) {
            super.init(frame: frame)
            
            self.backgroundColor = UIColor.baseTheme.current.bgColor
            
            self.contentView.addSubview(self.collectionView)
            self.contentView.addSubview(self.indicatorView)
            self.indicatorView.addSubview(self.sliderView)
            
            self.collectionView.snp.makeConstraints { make in
                make.top.left.right.equalTo(0)
                make.bottom.equalTo(-8)
            }
            
            self.indicatorView.snp.makeConstraints { make in
                make.bottom.equalTo(-18)
                make.centerX.equalTo(self.contentView.snp.centerX)
                make.size.equalTo(CGSize(width: 30, height: 2))
            }
            
            self.observer = self.collectionView.observe(\.contentOffset, options: [.new], changeHandler: { [weak self] scrollView, _ in
                guard let self = self, scrollView.contentSize.width > 0 else { return }
                
                let ratio = self.indicatorView.size.width / scrollView.contentSize.width
                let x = ratio * scrollView.contentOffset.x
                let width = ratio * scrollView.size.width
                
                self.sliderView.frame = CGRect(x: x, y: 0, width: width, height: 2)
            })
        }
        
        @available(*, unavailable)
        required init?(coder: NSCoder) {
            fatalError("init(coder:) has not been implemented")
        }
        
        //  强制初始化sliderView 的frame
        func initSliderViewFrame() {
            if self.sliderView.frame.isEmpty {
                self.layoutIfNeeded()
                let ratio = self.indicatorView.size.width / self.collectionView.contentSize.width
                let width = ratio * self.collectionView.size.width
                if width.isNormal {
                    self.sliderView.frame = CGRectMake(0, 0, width, 2)
                }
            }
        }
    }
    
    class SpecialToolItem: UICollectionViewCell {
        let bgImgView = UIImageView()
        
        let imgView = UIImageView()
        
        let newImageView = UIImageView()
        
        let tabLabel: UILabel = {
            let label = UILabel()
            label.font = .aic_mediumFont(withSize: 9)
            label.textColor = UIColor.baseTheme.current.emptyButtonTitleColor
            label.textAlignment = .center
            return label
        }()
        
        let titleLabel: UILabel = {
            let label = UILabel()
            label.font = UIFont.aic_semiboldFont(withSize: 15)
            label.textColor = UIColor.baseTheme.current.cellTitleColor
            label.textAlignment = .center
            label.numberOfLines = 0
            return label
        }()
        
        let subTitleLabel: UILabel = {
            let label = UILabel()
            label.font = UIFont.systemFont(ofSize: 12)
            label.textColor = UIColor.baseTheme.night.cellSubtitleColor
            label.textAlignment = .center
            return label
        }()
        
        override init(frame: CGRect) {
            super.init(frame: frame)
            
            self.backgroundColor = UIColor.baseTheme.current.cellBgColor
            self.contentView.clipsToBounds = true
            
            self.contentView.addSubview(self.bgImgView)
            
            let containerView = UIView()
            self.contentView.addSubview(containerView)
            containerView.addSubview(self.imgView)
            containerView.addSubview(self.newImageView)
            containerView.addSubview(self.titleLabel)
            containerView.addSubview(self.subTitleLabel)
            containerView.addSubview(self.tabLabel)
            
            self.bgImgView.snp.makeConstraints { make in
                make.left.equalTo(-11)
                make.top.equalTo(-11)
                make.size.equalTo(CGSize(width: 48, height: 48))
            }
            
            containerView.snp.makeConstraints { make in
                make.left.right.equalTo(0)
                make.centerY.equalTo(self.contentView.snp.centerY)
            }
            
            self.imgView.snp.makeConstraints { make in
                make.size.equalTo(CGSize(width: 28, height: 28))
                make.top.equalTo(0)
                make.centerX.equalTo(containerView.snp.centerX)
            }
            
            self.newImageView.snp.makeConstraints { make in
                make.bottom.equalTo(self.imgView.snp.top).offset(6)
                make.left.equalTo(self.imgView.snp.right).offset(-6)
                make.size.equalTo(CGSize(width: 32, height: 13))
            }
            
            self.titleLabel.snp.makeConstraints { make in
                make.top.equalTo(self.imgView.snp.bottom).offset(8)
                make.left.right.equalTo(0)
            }
            
            self.subTitleLabel.snp.makeConstraints { make in
                make.top.equalTo(self.titleLabel.snp.bottom).offset(5)
                make.left.right.bottom.equalTo(0)
            }
            
            self.tabLabel.snp.makeConstraints { make in
                make.centerX.equalTo(self.newImageView.snp.centerX)
                make.centerY.equalTo(self.newImageView.snp.centerY)
            }
            
            self.layer.shadowRadius = 5
            self.layer.shadowOpacity = isNightTheme ? 0.8 : 0.06
            self.layer.shadowColor = UIColor.black.cgColor
            self.layer.shadowOffset = .zero
        }
        
        @available(*, unavailable)
        required init?(coder: NSCoder) {
            fatalError("init(coder:) has not been implemented")
        }
        
        override func layoutSubviews() {
            super.layoutSubviews()
            
            let path = UIBezierPath(rect: self.bounds)
            self.layer.shadowPath = path.cgPath
        }
    }
    
    class MenuView: UICollectionReusableView {
        override class var layerClass: AnyClass {
            return UnderLayer.self
        }
        
        class UnderLayer: CALayer {
            override var zPosition: CGFloat {
                get { return 0 }
                set {}
            }
        }
        
        let collectionView: UICollectionView = {
            let layout = UICollectionViewFlowLayout()
            layout.scrollDirection = .horizontal
            let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
            collectionView.backgroundColor = UIColor.baseTheme.current.cellBgColor
            collectionView.registerClassCell(class: MenuItem.self)
            collectionView.showsHorizontalScrollIndicator = false
            return collectionView
        }()
        
        override init(frame: CGRect) {
            super.init(frame: frame)
            
            self.addSubview(self.collectionView)
            self.collectionView.snp.makeConstraints { make in
                make.edges.equalTo(0)
            }
        }
        
        @available(*, unavailable)
        required init?(coder: NSCoder) {
            fatalError("init(coder:) has not been implemented")
        }
    }
    
    class MenuItem: UICollectionViewCell {
        let titleLabel = UILabel()
        
        let lineView: UIView = {
            let view = UIView()
            view.backgroundColor = UIColor.baseTheme.current.mainColor
            return view
        }()
        
        override init(frame: CGRect) {
            super.init(frame: frame)
            
            self.contentView.addSubview(self.titleLabel)
            self.contentView.addSubview(self.lineView)
            
            self.titleLabel.snp.makeConstraints { make in
                make.center.equalTo(self.contentView.snp.center)
            }
            
            self.lineView.snp.makeConstraints { make in
                make.centerX.equalTo(self.contentView.snp.centerX)
                make.bottom.equalTo(0)
                make.size.equalTo(CGSize(width: 20, height: 2))
            }
        }
        
        @available(*, unavailable)
        required init?(coder: NSCoder) {
            fatalError("init(coder:) has not been implemented")
        }
    }
    
    // MARK: - 数据结构

    class CollectionViewSectionController: NSObject {
        weak var collectionView: UICollectionView?
        
        var inset: UIEdgeInsets {
            return .zero
        }
        
        var lineSpacing: CGFloat {
            return .zero
        }
        
        var numberOfRows: Int {
            return .zero
        }
        
        func cellForItem(at indexPath: IndexPath) -> UICollectionViewCell {
            return UICollectionViewCell()
        }
        
        func sizeForItem(at index: Int) -> CGSize {
            return .zero
        }
        
        func didSelectItem(at index: Int) {}
        
        func header(at indexPath: IndexPath) -> UICollectionReusableView {
            return UICollectionReusableView()
        }
        
        var headerHeight: CGFloat {
            return .zero
        }
        
        func footer(at indexPath: IndexPath) -> UICollectionReusableView {
            return UICollectionReusableView()
        }
        
        var footerHeight: CGFloat {
            return .zero
        }
        
        var sectionHeight: CGFloat {
            return .zero
        }
        
        /// 映射实际下标给cellforItem
        var mapRealIndexPathBlock: ((IndexPath) -> IndexPath)?
        
        func mapRealIndexPath(transform indexPath: IndexPath) -> IndexPath {
            return self.mapRealIndexPathBlock?(indexPath) ?? indexPath
        }
    }
    
    class CollectionViewContainerSectionController: CollectionViewSectionController {
        override var collectionView: UICollectionView? {
            didSet {
                sectionControllers.forEach { $0.collectionView = collectionView }
            }
        }
        
        override var numberOfRows: Int {
            return self.indexPaths.count
        }
        
        override func cellForItem(at indexPath: IndexPath) -> UICollectionViewCell {
            let index = self.mapRealIndexPath(transform: indexPath).section
            let controller = self.sectionControllers[index]
            return controller.cellForItem(at: indexPath)
        }
        
        override func sizeForItem(at index: Int) -> CGSize {
            let indexPath = self.indexPaths[index]
            let controller = self.sectionControllers[indexPath.section]
            return controller.sizeForItem(at: indexPath.row)
        }
        
        override func didSelectItem(at index: Int) {
            let indexPath = self.indexPaths[index]
            let controller = self.sectionControllers[indexPath.section]
            return controller.didSelectItem(at: indexPath.row)
        }
        
        override var sectionHeight: CGFloat {
            return self.sectionControllers.reduce(0.0) { $0 + $1.sectionHeight }
        }
        
        override func mapRealIndexPath(transform indexPath: IndexPath) -> IndexPath {
            let index = super.mapRealIndexPath(transform: indexPath).row
            return self.indexPaths[index]
        }
        
        let sectionControllers: [CollectionViewSectionController]
        
        private let indexPaths: [IndexPath]
        
        init(controllers: [CollectionViewSectionController]) {
            self.sectionControllers = controllers
            
            var indexPaths = [IndexPath]()
            controllers.indices.forEach { section in
                let count = controllers[section].numberOfRows
                (0..<count).forEach { row in
                    indexPaths.append(IndexPath(row: row, section: section))
                }
            }
            self.indexPaths = indexPaths
            
            super.init()
            
            self.sectionControllers.forEach { controller in
                controller.mapRealIndexPathBlock = { [weak self] indexPath in
                    guard let self = self else { return indexPath }
                    
                    return self.mapRealIndexPath(transform: indexPath)
                }
            }
        }
    }
    
    class TitleSectionController: CollectionViewSectionController {
        override var numberOfRows: Int {
            return 1
        }
        
        override func cellForItem(at indexPath: IndexPath) -> UICollectionViewCell {
            guard let collectionView = self.collectionView else {
                return super.cellForItem(at: indexPath)
            }
            
            let cell = collectionView.dequeueCell(class: TitleCollectionViewCell.self, indexPath: indexPath)
            cell.titleLabel.text = self.title.home.localized
            return cell
        }
        
        override func sizeForItem(at index: Int) -> CGSize {
            guard let collectionView = self.collectionView else {
                return super.sizeForItem(at: index)
            }
            
            return CGSize(width: collectionView.width, height: self.sectionHeight)
        }
        
        override var sectionHeight: CGFloat {
            return 40
        }
        
        let title: String
        
        init(title: String) {
            self.title = title
        }
    }
    
    class ToolsSectionController: CollectionViewSectionController {
        override var numberOfRows: Int {
            return self.fakeTools.count
        }
        
        override func cellForItem(at indexPath: IndexPath) -> UICollectionViewCell {
            guard let collectionView = self.collectionView else {
                return super.cellForItem(at: indexPath)
            }
            
            let index = self.mapRealIndexPath(transform: indexPath).row
            let tool = self.fakeTools[index]
            let cell = collectionView.dequeueCell(class: HomePageToolsCollectionViewCell.self, indexPath: indexPath)
            cell.titleLabel.text = tool?.rawValue.home.localized
            cell.imgView.image = tool?.image
//            cell.newImgView.image = tool?.newImage
            
            // 本地写死悬浮窗的标签
            if tool == .悬浮窗 {
                cell.newImgView.image = tool?.newImage
                cell.setData(tags: aic_isChinese() ? ["重磅"] : ["New"])
            } else if tool == .聊天室 {
                cell.newImgView.image = tool?.newImage
                cell.setData(tags: ["NEW"])
            } else if tool == .日历 {
                cell.newImgView.image = tool?.newImage
                cell.setData(tags: ["NEW"])
            } else {
                cell.newImgView.image = nil
                cell.setData(tags: nil)
            }
            
            return cell
        }
        
        override func sizeForItem(at index: Int) -> CGSize {
            guard let collectionView = self.collectionView else {
                return super.sizeForItem(at: index)
            }
            
            let width = floor(collectionView.width / CGFloat(self.rowCount))
            return CGSize(width: width, height: self.rowHeight)
        }
        
        override func didSelectItem(at index: Int) {
            let tool = self.fakeTools[index]
            if tool == .PRO版K线 || tool == .指标胜率 || tool == .VIP数据 {
                AppAnalytics.HomePage.clickHomePage(action: "功能tab", name: tool?.rawValue ?? "")
            }
            tool?.didSelected(from: self.collectionView?.aic_rootViewController())
        }
        
        override var sectionHeight: CGFloat {
            let lineCount = CGFloat((tools.count - 1) / self.rowCount + 1)
            return self.rowHeight * lineCount + lineSpacing * (lineCount - 1)
        }
        
        private let rowHeight: CGFloat = 80
        
        private let rowCount = 4
        
        /// 为保持数据长度为rowCount的整数倍
        private lazy var fakeTools: [HomePageConstant.TickerTool?] = {
            let count = self.tools.count
            let totalCount = ((count - 1) / self.rowCount + 1) * self.rowCount
            var arr = [HomePageConstant.TickerTool?](repeating: nil, count: totalCount)
            for i in 0..<count {
                arr[i] = self.tools[i]
            }
            return arr
        }()
        
        let tools: [HomePageConstant.TickerTool]
        
        init(tools: [HomePageConstant.TickerTool]) {
            self.tools = tools
        }
    }
    
    /// 特色功能
    class SpecialToolsSectionController: ToolsSectionController, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
        override var numberOfRows: Int {
            return 1
        }
        
        override func cellForItem(at indexPath: IndexPath) -> UICollectionViewCell {
            guard let collectionView = self.collectionView else {
                return super.cellForItem(at: indexPath)
            }
            
            let cell = collectionView.dequeueCell(class: SpecialToolsCollectionViewCell.self, indexPath: indexPath)
            cell.collectionView.delegate = self
            cell.collectionView.dataSource = self
            cell.collectionView.reloadData()
            cell.initSliderViewFrame()
            return cell
        }
        
        override func sizeForItem(at index: Int) -> CGSize {
            guard let collectionView = self.collectionView else {
                return super.sizeForItem(at: index)
            }
            
            return CGSize(width: collectionView.width, height: self.sectionHeight)
        }
        
        override func didSelectItem(at index: Int) {}
        
        override var sectionHeight: CGFloat {
            return 136
        }
        
        func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
            return self.tools.count
        }
        
        func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
            let tool = self.tools[indexPath.row]
            let cell = collectionView.dequeueCell(class: SpecialToolItem.self, indexPath: indexPath)
            cell.titleLabel.text = tool.text.home.localized
            cell.subTitleLabel.text = tool.description.home.localized
            cell.imgView.image = tool.image
            cell.bgImgView.image = tool.bgImage
//            cell.newImageView.image = tool.newImage
            if tool == .聊天室 {
                cell.newImageView.image = tool.newImage
                cell.tabLabel.text = "NEW"
            } else {
                cell.newImageView.image = nil
                cell.tabLabel.text = nil
            }
            
            return cell
        }
        
        func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
            let tool = self.tools[indexPath.row]
            tool.didSelected(from: collectionView.aic_rootViewController())
        }
        
        func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
            return CGSize(width: 106, height: 100)
        }
        
        func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumInteritemSpacingForSectionAt section: Int) -> CGFloat {
            return 0
        }
        
        func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
            return 8
        }
        
        func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, insetForSectionAt section: Int) -> UIEdgeInsets {
            return UIEdgeInsets(top: 6, left: 15, bottom: 22, right: 15)
        }
    }
    
    class ToolsContainerSectionController: CollectionViewContainerSectionController {
        var title: String {
            return self.titleController.title
        }
        
        var tools: [HomePageConstant.TickerTool] {
            return self.toolsController.tools
        }
        
        // 1
        private let titleController: TitleSectionController
        
        private let toolsController: ToolsSectionController
        
        init(titleController: TitleSectionController, toolsController: ToolsSectionController) {
            self.titleController = titleController
            self.toolsController = toolsController
            super.init(controllers: [titleController, toolsController])
        }
        
        convenience init(title: String, toolsController: ToolsSectionController) {
            self.init(titleController: TitleSectionController(title: title),
                      toolsController: toolsController)
        }
        
        convenience init(title: String, tools: [HomePageConstant.TickerTool]) {
            self.init(title: title, toolsController: ToolsSectionController(tools: tools))
        }
    }
    
    /// 所有工具
    class AllToolsContainerSectionController: CollectionViewContainerSectionController, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
        private let tools: [ToolsContainerSectionController] = {
            var tools = [
                ToolsContainerSectionController(
                    title: "数据榜单",
                    tools: [
                        .各类指数, .热门排行, .近期上新, .行情异动,
                        .爆仓统计, .跨期价差, .合约持仓, .特色榜单,
                        .涨跌分布, .龙虎榜单, .资金流向
                    ]),
                ToolsContainerSectionController(
                    title: "实用工具",
                    tools: [
                        .币址查询, .挖矿数据, .矿池份额, .BTC富豪榜
                    ]),
                ToolsContainerSectionController(
                    title: "社区资讯",
                    tools: [
                        .VIP数据, .聊天室, .社区广场, .热点排行榜, .精选新闻, .精选快讯, .平台公告, .日历 // , .无聊
                    ])
            ]
            #if AIC_DISABLE_ADV
            // 商店版
            tools.insert(
                ToolsContainerSectionController(
                    title: "盯盘神器",
                    tools: [
                        .预警中心,
//                        .角标行情,
                        .通知中心行情, .AppleWatch, .悬浮窗
                    ]),
                at: 1)
            tools.insert(
                ToolsContainerSectionController(
                    title: "高级K线",
                    tools: [
                        .组合K线, .对比K线, .K线大师
                    ]),
                at: 2)
            #else
            tools.insert(
                ToolsContainerSectionController(
                    title: "盯盘神器",
                    tools: [
                        .指标胜率, .预警中心, .语音预警,
//                            .角标行情,
                        .通知中心行情, .AppleWatch, .悬浮窗
                    ]),
                at: 1)
            tools.insert(
                ToolsContainerSectionController(
                    title: "高级K线",
                    tools: [
                        .PRO版K线, .组合K线, .对比K线, .K线大师
                    ]),
                at: 2)
            #endif
            if aic_isEnableAdv() {
                if MeCostant.stopTrade == false {
                    tools.append(
                        ToolsContainerSectionController(
                            title: "资产管理",
                            tools: [
                                .授权管理, .资产统计 // , .买卖点记录
                            ])
                    )
                }
            }
            return tools
        }()
        
        init() {
            super.init(controllers: self.tools)
        }
        
        var selectedItemBlock: ((Int) -> Void)?
        
        private var selectedIndex = 0 {
            didSet {
                if oldValue == self.selectedIndex { return }
                self.headerView?.collectionView.reloadData()
                self.headerView?.collectionView.scrollToItem(at: IndexPath(row: self.selectedIndex, section: 0),
                                                             at: .centeredHorizontally,
                                                             animated: true)
            }
        }
        
        private weak var headerView: MenuView?
        
        private lazy var menuItems: [String] = self.tools.map { $0.title }
        
        private let fontSize: CGFloat = 15
        
        private lazy var widthDic: [String: CGFloat] = {
            self.menuItems.reduce([:]) { result, item -> [String: CGFloat] in
                var dic = result
                dic[item] = item.home.localized.size(for: UIFont.aic_mediumFont(withSize: self.fontSize),
                                                     size: CGSize(width: .greatestFiniteMagnitude, height: self.headerHeight),
                                                     mode: .byWordWrapping).width
                return dic
            }
        }()
        
        override var headerHeight: CGFloat {
            return 40
        }
        
        override func header(at indexPath: IndexPath) -> UICollectionReusableView {
            guard let collectionView = self.collectionView else {
                return super.header(at: indexPath)
            }
            
            let headerView = collectionView.dequeueReusableSupplementaryView(kind: .header, class: MenuView.self, indexPath: indexPath)
            headerView.collectionView.delegate = self
            headerView.collectionView.dataSource = self
            headerView.collectionView.reloadData()
            headerView.collectionView.scrollToItem(at: IndexPath(row: self.selectedIndex, section: 0),
                                                   at: .centeredHorizontally,
                                                   animated: false)
            
            self.headerView = headerView
            
            return headerView
        }
        
        override func footer(at indexPath: IndexPath) -> UICollectionReusableView {
            guard let collectionView = self.collectionView else {
                return super.footer(at: indexPath)
            }
            
            return collectionView.dequeueReusableSupplementaryView(kind: .footer,
                                                                   class: UICollectionReusableView.self,
                                                                   indexPath: indexPath)
        }
        
        override var footerHeight: CGFloat {
            if let collectionView = self.collectionView, let controller = self.tools.last {
                return collectionView.height - controller.sectionHeight - self.headerHeight
            }
            return super.footerHeight
        }
        
        /// 处理滑动到哪个section
        func didScroll(to indexPath: IndexPath) {
            let index = mapRealIndexPath(transform: indexPath).section
            self.selectedIndex = index
        }
        
        // UICollectionViewDataSource, UICollectionViewDelegateFlowLayout
        func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
            return self.menuItems.count
        }
        
        func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
            let item = self.menuItems[indexPath.row]
            let cell = collectionView.dequeueCell(class: MenuItem.self, indexPath: indexPath)
            cell.titleLabel.text = item.home.localized
            
            if indexPath.row == self.selectedIndex {
                cell.lineView.isHidden = false
                cell.titleLabel.font = UIFont.aic_mediumFont(withSize: self.fontSize)
                cell.titleLabel.textColor = UIColor.baseTheme.current.mainColor
            } else {
                cell.lineView.isHidden = true
                cell.titleLabel.font = UIFont.systemFont(ofSize: self.fontSize)
                cell.titleLabel.textColor = UIColor.baseTheme.current.cellSubtitleColor
            }
            
            return cell
        }
        
        func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
            let section = indexPath.row
            var rows = 0
            for i in 0..<section {
                rows += self.tools[i].numberOfRows
            }
            self.selectedItemBlock?(rows)
        }
        
        func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
            let item = self.menuItems[indexPath.row]
            let width = self.widthDic[item] ?? 60
            return CGSize(width: width, height: collectionView.height)
        }
        
        func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumInteritemSpacingForSectionAt section: Int) -> CGFloat {
            return 22
        }
        
        func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
            return 22
        }
        
        func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, insetForSectionAt section: Int) -> UIEdgeInsets {
            return UIEdgeInsets(top: 0, left: 15, bottom: 0, right: 15)
        }
    }
}
