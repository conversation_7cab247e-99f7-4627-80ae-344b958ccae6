//
//  CalendarEventModel.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-18.
//  Copyright © 2024 AICoin. All rights reserved.
//

import Foundation

/// 日历事件模型
struct CalendarEventModel {
    var id: String = UUID().uuidString
    var title: String
    var date: Date
    var isImportant: Bool
    var type: EventType = .etf
    var content: String?

    /// 事件类型
    enum EventType: Int {
        case etf = 0       // ETF
        case macro = 1     // 宏观数据
        case token = 2     // 代币解锁
        case listing = 3   // 上新下架
    }
}

/// 日历事件请求模型
struct CalendarEventRequestModel {
    var year: Int
    var month: Int
    var onlyImportant: Bool = false

    init(date: Date, onlyImportant: Bool = false) {
        let calendar = Calendar.current
        self.year = calendar.component(.year, from: date)
        self.month = calendar.component(.month, from: date)
        self.onlyImportant = onlyImportant
    }
}

/// 日历事件管理器
class CalendarEventManager {
    static let shared = CalendarEventManager()

    private init() {}

    /// 获取指定月份的事件
    func getMonthEvents(request: CalendarEventRequestModel, completion: @escaping ([CalendarEventModel]?, Error?) -> Void) {
        // 这里可以连接到服务器获取事件数据
        // 目前使用模拟数据代替

        let mockEvents = self.generateMockEvents(for: request)
        completion(mockEvents, nil)
    }

    /// 生成模拟事件数据
    private func generateMockEvents(for request: CalendarEventRequestModel) -> [CalendarEventModel] {
        var events: [CalendarEventModel] = []

        // 为了演示，我们简单地创建一些固定日期的事件
        let calendar = Calendar.current
        var dateComponents = DateComponents()
        dateComponents.year = request.year
        dateComponents.month = request.month

        // 1号有一个普通事件
        dateComponents.day = 1
        if let date = calendar.date(from: dateComponents) {
            events.append(CalendarEventModel(title: "1号财经新闻", date: date, isImportant: false))
        }

        // 2号有一个普通事件
        dateComponents.day = 2
        if let date = calendar.date(from: dateComponents) {
            events.append(CalendarEventModel(title: "2号财务报告", date: date, isImportant: false))
        }

        // 3号有一个普通事件
        dateComponents.day = 3
        if let date = calendar.date(from: dateComponents) {
            events.append(CalendarEventModel(title: "3号市场分析", date: date, isImportant: false))
        }

        // 5号有一个重要事件
        dateComponents.day = 5
        if let date = calendar.date(from: dateComponents) {
            events.append(CalendarEventModel(title: "5号重大政策发布", date: date, isImportant: true, type: .macro))
        }

        // 23号有一个特殊选中事件
        dateComponents.day = 23
        if let date = calendar.date(from: dateComponents) {
            events.append(CalendarEventModel(title: "23号特别会议", date: date, isImportant: true, type: .macro))
        }

        // 26号有一个高亮事件
        dateComponents.day = 26
        if let date = calendar.date(from: dateComponents) {
            events.append(CalendarEventModel(title: "26号平台更新", date: date, isImportant: true, type: .token))
        }

        // 27号有一个重要事件
        dateComponents.day = 27
        if let date = calendar.date(from: dateComponents) {
            events.append(CalendarEventModel(title: "27号市场分析报告", date: date, isImportant: true, type: .listing))
        }

        // 29号有一个重要事件
        dateComponents.day = 29
        if let date = calendar.date(from: dateComponents) {
            events.append(CalendarEventModel(title: "29号产品发布会", date: date, isImportant: true, type: .etf))
        }

        // 如果只需要重要事件，则过滤掉普通事件
        if request.onlyImportant {
            events = events.filter { $0.isImportant }
        }

        return events
    }
}