//
//  CalendarDataSource.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-18.
//  Copyright © 2024 AICoin. All rights reserved.
//

import Foundation

class CalendarDataSource {
    static let shared = CalendarDataSource()

    var events: [CalendarEventModel] = [] {
        didSet {
            // 当事件更新时，重新按日期分组
            updateEventsByDate()
        }
    }
    private(set) var eventsByDate: [String: [CalendarEventModel]] = [:]

    var selectedFilters: [EventTypeFilter] = [.all]
    var selectedDate: Date = Date()

    private init() {}

    func loadEvents(date: Date, filters: [EventTypeFilter], completion: @escaping (Bool) -> Void) {
        // 检查是否选择了"宏观数据"过滤器，如果是，则只显示重要事件
        let onlyImportant = filters.contains { $0.id == "macro" }

        let request = CalendarEventRequestModel(date: date, onlyImportant: onlyImportant)

        CalendarEventManager.shared.getMonthEvents(request: request) { [weak self] (events, error) in
            guard let self = self, let events = events else {
                completion(false)
                return
            }

            self.events = events
            // 事件设置后会自动调用updateEventsByDate方法

            completion(true)
        }
    }

    func filteredEventsForSelectedDate() -> [CalendarEventModel] {
        let dateKey = formatDateToString(date: selectedDate)
        let events = eventsByDate[dateKey] ?? []

        // 如果选择了"全部"过滤器，则返回所有事件
        if selectedFilters.contains(where: { $0.id == EventTypeFilter.all.id }) {
            return events
        }

        // 根据所选过滤器的ID过滤事件
        // 这里我们使用一个映射关系来确定事件类型
        let filterId = selectedFilters.first?.id ?? "all"

        // 如果是全部，返回所有事件
        if filterId == "all" {
            return events
        }

        // 根据过滤器ID过滤事件
        return events.filter { event in
            switch filterId {
            case "etf":
                return event.type == .etf
            case "macro":
                return event.type == .macro
            case "token":
                return event.type == .token
            case "listing":
                return event.type == .listing
            default:
                return true
            }
        }
    }

    func hasEvents(for date: Date) -> Bool {
        let dateKey = formatDateToString(date: date)
        return eventsByDate[dateKey]?.isEmpty == false
    }

    func hasEventsByType(for date: Date, type: CalendarEventModel.EventType) -> Bool {
        let dateKey = formatDateToString(date: date)
        return eventsByDate[dateKey]?.contains(where: { $0.type == type }) == true
    }

    func hasImportantEvents(for date: Date) -> Bool {
        let dateKey = formatDateToString(date: date)
        return eventsByDate[dateKey]?.contains(where: { $0.isImportant }) == true
    }

    private func formatDateToString(date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: date)
    }

    // 更新按日期分组的事件
    private func updateEventsByDate() {
        // 清空现有的分组
        eventsByDate.removeAll()

        // 重新按日期分组事件
        for event in events {
            let dateKey = formatDateToString(date: event.date)
            if eventsByDate[dateKey] == nil {
                eventsByDate[dateKey] = []
            }
            eventsByDate[dateKey]?.append(event)
        }
    }
}
