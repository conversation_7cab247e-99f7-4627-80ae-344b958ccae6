//
//  EventTypeFilter.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-18.
//  Copyright © 2024 AICoin. All rights reserved.
//

import Foundation
import UIKit

struct EventTypeFilter {
    let id: String
    let title: String
    let isActive: Bool

    static let all = EventTypeFilter(id: "all", title: "全部", isActive: true)
    static let etf = EventTypeFilter(id: "etf", title: "ETF", isActive: false)
    static let macro = EventTypeFilter(id: "macro", title: "宏观数据", isActive: false)
    static let token = EventTypeFilter(id: "token", title: "代币解锁", isActive: false)
    static let listing = EventTypeFilter(id: "listing", title: "上新下架", isActive: false)

    static let allFilters = [all, etf, macro, token, listing]

    func withActiveState(_ active: Bool) -> EventTypeFilter {
        return EventTypeFilter(id: id, title: title, isActive: active)
    }
}