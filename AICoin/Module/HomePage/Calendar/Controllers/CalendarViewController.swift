//
//  CalendarViewController.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-18.
//  Copyright © 2024 AICoin. All rights reserved.
//

import UIKit
import SnapKit

class CalendarViewController: AICBaseViewController {

    // MARK: - 属性

    private let viewModel = CalendarViewModel()
    private let foldableCalendarView = CalendarFoldableView()

    // 事件列表相关视图
    private let eventTypeTabView = EventTypeTabView()
    private let eventListView = EventListView()

    // MARK: - 生命周期

    override func viewDidLoad() {
        super.viewDidLoad()
        self.title = "日历".base.localized

        setupViews()
        bindViewModel()
    }

    deinit {
        CalendarEventManager.shared.clearSessionCache()
    }

    // MARK: - 设置

    private func setupViews() {
        view.backgroundColor = UIColor.baseTheme.current.bgColor

        // 添加日历视图
        view.addSubview(foldableCalendarView)

        // 添加事件类型选择视图和事件列表视图
        view.addSubview(eventTypeTabView)
        view.addSubview(eventListView)

        // 设置约束
        foldableCalendarView.snp.makeConstraints { make in
            make.top.equalTo(view)
            make.left.right.equalTo(view)
        }

        eventTypeTabView.snp.makeConstraints { make in
            make.top.equalTo(foldableCalendarView.snp.bottom).offset(8)
            make.left.right.equalTo(view)
            make.height.equalTo(35)
        }

        eventListView.snp.makeConstraints { make in
            make.top.equalTo(eventTypeTabView.snp.bottom)
            make.left.right.equalTo(view)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom)
        }
    }

    private func bindViewModel() {
        // 配置日历视图
        foldableCalendarView.configure(with: viewModel)
        foldableCalendarView.delegate = self

        // 配置事件列表视图
        eventTypeTabView.configure(with: viewModel.availableFilters)
        eventListView.configure(with: viewModel)

        // 设置事件提醒弹窗点击回调
        eventListView.onEventSelected = { [weak self] event in
            let reminderVC = EventReminderViewController(event: event)
            self?.present(reminderVC, animated: true, completion: nil)
        }

        // 设置标签点击回调
        eventListView.onTagTapped = { [weak self] entrance in
            if entrance.entranceType == 1 {
                // K线入口，跳转K线详情页
                UIViewController.aic_getRootNavigationController()?.jumpToTickerDetailVC(firstKey: entrance.key, listKeys: nil)
            } else {
                // 网页入口，跳转H5页面
                UIViewController.aic_getRootNavigationController()?.jumpToWebViewController(entrance.link)
            }
        }
        // 设置事件类型选择回调
        eventTypeTabView.onFilterSelected = { [weak self] filter in
            self?.viewModel.applyFilter(filter)
        }

        // 设置视图模型回调
        viewModel.onFilterChanged = { [weak self] in
            self?.eventTypeTabView.configure(with: self?.viewModel.availableFilters ?? [])
        }

        viewModel.onEventsUpdated = { [weak self] in
            self?.eventListView.reloadData()
        }
        
        // 新增：错误处理
        viewModel.onErrorOccurred = { [weak self] error in
            DispatchQueue.main.async {
                self?.showErrorAlert(error: error)
            }
        }
        
        // 新增：words加载完成处理
        viewModel.onWordsLoadingCompleted = { [weak self] success in
            DispatchQueue.main.async {
                if success {
                    // words加载成功，初始化UI并加载事件
                    self?.viewModel.moveToToday()
                } else {
                    // words加载失败，显示错误提示但仍然初始化UI
                    self?.showWordsLoadingError()
                    self?.viewModel.moveToToday()
                }
            }
        }

        // 首先初始化words，然后再加载事件
        viewModel.initializeEventWords()
    }
    
    /// 显示words加载失败提示
    private func showWordsLoadingError() {
        let alert = UIAlertController(title: "提示", message: "加载事件分类失败，将使用默认分类", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    

    // MARK: - 错误处理
    
    /// 显示错误提示弹窗
    private func showErrorAlert(error: Error) {
        let alert = UIAlertController(title: "提示", message: error.localizedDescription, preferredStyle: .alert)
        
        // 重试按钮
        alert.addAction(UIAlertAction(title: "重试", style: .default) { [weak self] _ in
            self?.viewModel.loadEvents()
        })
        
        // 取消按钮
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        present(alert, animated: true)
    }
}

// MARK: - CalendarFoldableViewDelegate
extension CalendarViewController: CalendarFoldableViewDelegate {

    func calendarFoldableView(_ view: CalendarFoldableView, didSelectDate date: Date) {
        // 日期选择逻辑已在CalendarView中通过ViewModel处理
        // 此处可以处理UI更新或其他视图控制器级别的逻辑
    }

    func calendarFoldableView(_ view: CalendarFoldableView, didChangeMonth date: Date) {
        // 月份变更由ViewModel处理
    }

    func calendarFoldableView(_ view: CalendarFoldableView, didChangeFoldState isCollapsed: Bool) {
        // 调整事件列表的位置
//        UIView.animate(withDuration: 0.3) {
//            self.eventTypeTabView.snp.updateConstraints { make in
//                make.top.equalTo(self.foldableCalendarView.snp.bottom).offset(16)
//            }
//            self.view.layoutIfNeeded()
//        }
    }

    func calendarFoldableView(_ view: CalendarFoldableView, didChangeImportantOnly isImportantOnly: Bool) {
        // 在ViewModel中已处理，这里不需要额外处理
    }

    func calendarFoldableViewDidTapEventReminder(_ view: CalendarFoldableView) {
        // 跳转到事件提醒页面
        let reminderVC = CalendarEventReminderViewController()
        reminderVC.hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(reminderVC, animated: true)
    }
}
