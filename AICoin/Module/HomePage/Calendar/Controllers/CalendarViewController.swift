//
//  CalendarViewController.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-18.
//  Copyright © 2024 AICoin. All rights reserved.
//

import UIKit
import SnapKit

class CalendarViewController: AICBaseViewController {

    // MARK: - 属性

    private let viewModel = CalendarViewModel()
    private let foldableCalendarView = CalendarFoldableView()

    // 事件列表相关视图
    private let eventTypeTabView = EventTypeTabView()
    private let eventListView = EventListView()

    // MARK: - 生命周期

    override func viewDidLoad() {
        super.viewDidLoad()
        self.title = "日历".base.localized

        // 启用模拟数据（仅用于开发和测试）
        UserDefaults.standard.set(true, forKey: "UseCalendarMockData")

        setupViews()
        bindViewModel()
    }

    // MARK: - 设置

    private func setupViews() {
        view.backgroundColor = UIColor.baseTheme.current.bgColor

        // 添加日历视图
        view.addSubview(foldableCalendarView)

        // 添加事件类型选择视图和事件列表视图
        view.addSubview(eventTypeTabView)
        view.addSubview(eventListView)

        // 设置约束
        foldableCalendarView.snp.makeConstraints { make in
            make.top.equalTo(view)
            make.left.right.equalTo(view)
        }

        eventTypeTabView.snp.makeConstraints { make in
            make.top.equalTo(foldableCalendarView.snp.bottom).offset(8)
            make.left.right.equalTo(view)
            make.height.equalTo(35)
        }

        eventListView.snp.makeConstraints { make in
            make.top.equalTo(eventTypeTabView.snp.bottom)
            make.left.right.equalTo(view)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom)
        }
    }

    private func bindViewModel() {
        // 配置日历视图
        foldableCalendarView.configure(with: viewModel)
        foldableCalendarView.delegate = self

        // 配置事件列表视图
        eventTypeTabView.configure(with: viewModel.availableFilters)
        eventListView.configure(with: viewModel)

        // 设置事件点击回调
        eventListView.onEventSelected = { [weak self] event in
            self?.showEventReminder(for: event)
        }

        // 设置事件类型选择回调
        eventTypeTabView.onFilterSelected = { [weak self] filter in
            self?.viewModel.applyFilter(filter)
        }

        // 设置视图模型回调
        viewModel.onFilterChanged = { [weak self] in
            self?.eventTypeTabView.configure(with: self?.viewModel.availableFilters ?? [])
        }

        viewModel.onEventsUpdated = { [weak self] in
            self?.eventListView.reloadData()
        }

        // 初始化时移动到今天并加载事件
        viewModel.moveToToday()
    }
    
    // MARK: - 事件处理
    
    /// 显示事件提醒设置弹窗
    private func showEventReminder(for event: CalendarEventModel) {
        let reminderVC = EventReminderViewController(event: event)
        self.present(reminderVC, animated: true, completion: nil)
    }
}

// MARK: - CalendarFoldableViewDelegate
extension CalendarViewController: CalendarFoldableViewDelegate {

    func calendarFoldableView(_ view: CalendarFoldableView, didSelectDate date: Date) {
        viewModel.selectDate(date)
    }

    func calendarFoldableView(_ view: CalendarFoldableView, didChangeMonth date: Date) {
        // 月份变更由ViewModel处理
    }

    func calendarFoldableView(_ view: CalendarFoldableView, didChangeFoldState isCollapsed: Bool) {
        // 调整事件列表的位置
//        UIView.animate(withDuration: 0.3) {
//            self.eventTypeTabView.snp.updateConstraints { make in
//                make.top.equalTo(self.foldableCalendarView.snp.bottom).offset(16)
//            }
//            self.view.layoutIfNeeded()
//        }
    }

    func calendarFoldableView(_ view: CalendarFoldableView, didChangeImportantOnly isImportantOnly: Bool) {
        // 在ViewModel中已处理，这里不需要额外处理
    }

    func calendarFoldableViewDidTapEventReminder(_ view: CalendarFoldableView) {
        // 跳转到事件提醒页面
        let reminderVC = CalendarEventReminderViewController()
        reminderVC.hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(reminderVC, animated: true)
    }
}
