//
//  CalendarEventListViewController.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-18.
//  Copyright © 2024 AICoin. All rights reserved.
//

import UIKit
import SnapKit

class CalendarEventListViewController: AICBaseViewController {

    // MARK: - 属性

    private let eventListViewModel = EventListViewModel()
    private let eventTypeTabView = EventTypeTabView()
    private let eventListView = EventListView()

    // MARK: - 生命周期

    override func viewDidLoad() {
        super.viewDidLoad()
        setupViews()
        bindViewModel()
    }

    // MARK: - 设置

    private func setupViews() {
        view.backgroundColor = UIColor.baseTheme.current.bgColor

        view.addSubview(eventTypeTabView)
        view.addSubview(eventListView)

        eventTypeTabView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(44)
        }

        eventListView.snp.makeConstraints { make in
            make.top.equalTo(eventTypeTabView.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
    }

    private func bindViewModel() {
        eventTypeTabView.configure(with: eventListViewModel.availableFilters)
        eventListView.configure(with: eventListViewModel)

        eventTypeTabView.onFilterSelected = { [weak self] filter in
            self?.eventListViewModel.applyFilter(filter)
        }

        eventListViewModel.onFilterChanged = { [weak self] in
            self?.eventTypeTabView.configure(with: self?.eventListViewModel.availableFilters ?? [])
        }

        eventListViewModel.reloadEvents()
    }

    // MARK: - 公共方法

    func updateSelectedDate(_ date: Date) {
        // 更新数据源中的选中日期并重新加载事件列表
        eventListViewModel.dataSource.selectedDate = date
        eventListViewModel.reloadEvents()
    }

    func updateImportantOnly(_ isImportantOnly: Bool) {
        // 更新过滤器
        if isImportantOnly {
            eventListViewModel.applyFilter(.macro)
        } else {
            eventListViewModel.applyFilter(.all)
        }
    }
}