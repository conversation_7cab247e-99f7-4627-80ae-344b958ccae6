//
//  CalendarEventReminderViewController.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-19.
//  Copyright © 2024 AICoin. All rights reserved.
//

import UIKit
import SnapKit

/// 日历事件提醒设置页面
class CalendarEventReminderViewController: AICBaseViewController {
    
    // MARK: - 属性
    
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .grouped)
        tableView.backgroundColor = UIColor.baseTheme.current.bgColor
        tableView.separatorStyle = .none
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(CalendarEventReminderCell.self, forCellReuseIdentifier: "CalendarEventReminderCell")
        tableView.register(CalendarEventReminderHeaderView.self, forHeaderFooterViewReuseIdentifier: "CalendarEventReminderHeaderView")
        return tableView
    }()
    
    private var reminderSettings: [ReminderSectionModel] = []
    
    // MARK: - 生命周期
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupNavigation()
        setupViews()
        loadReminderSettings()
    }
    
    // MARK: - 设置
    
    private func setupNavigation() {
        title = "事件提醒".base.localized
    }
    
    private func setupViews() {
        view.backgroundColor = UIColor.baseTheme.current.bgColor
        
        view.addSubview(tableView)
        
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    // MARK: - 数据加载
    
    private func loadReminderSettings() {
        // 模拟从用户设置或服务器获取提醒设置
        let importantEvents = ReminderSectionModel(
            title: "重要事件".base.localized,
            items: [
                ReminderItemModel(title: "推送提醒".base.localized, isOn: true),
                ReminderItemModel(title: "日历提醒".base.localized, isOn: false)
            ]
        )
        
        let normalEvents = ReminderSectionModel(
            title: "普通事件".base.localized,
            items: [
                ReminderItemModel(title: "推送提醒".base.localized, isOn: false),
                ReminderItemModel(title: "日历提醒".base.localized, isOn: false)
            ]
        )
        
        reminderSettings = [importantEvents, normalEvents]
        tableView.reloadData()
    }
}

// MARK: - UITableViewDataSource, UITableViewDelegate
extension CalendarEventReminderViewController: UITableViewDataSource, UITableViewDelegate {
    
    func numberOfSections(in tableView: UITableView) -> Int {
        return reminderSettings.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return reminderSettings[section].items.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "CalendarEventReminderCell", for: indexPath) as! CalendarEventReminderCell
        
        let reminderItem = reminderSettings[indexPath.section].items[indexPath.row]
        cell.configure(with: reminderItem)
        
        cell.switchValueChanged = { [weak self] isOn in
            self?.reminderSettings[indexPath.section].items[indexPath.row].isOn = isOn
            // 这里可以添加保存设置的逻辑
        }
        
        return cell
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let headerView = tableView.dequeueReusableHeaderFooterView(withIdentifier: "CalendarEventReminderHeaderView") as! CalendarEventReminderHeaderView
        headerView.configure(with: reminderSettings[section].title)
        return headerView
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 50
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 60
    }
}

// MARK: - 模型
struct ReminderSectionModel {
    var title: String
    var items: [ReminderItemModel]
}

struct ReminderItemModel {
    var title: String
    var isOn: Bool
}

// MARK: - 自定义单元格
class CalendarEventReminderCell: UITableViewCell {
    
    // MARK: - 属性
    
    var switchValueChanged: ((Bool) -> Void)?
    
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16)
        label.textColor = UIColor.baseTheme.current.cellTitleColor
        return label
    }()
    
    private lazy var switchControl: UISwitch = {
        let switchControl = UISwitch()
        switchControl.onTintColor = DynamicHelper.themeColor(day: 0x1478FA, night: 0x1478FA)
        switchControl.addTarget(self, action: #selector(switchChanged(_:)), for: .valueChanged)
        return switchControl
    }()
    
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.baseTheme.current.cellBgColor
        view.layer.cornerRadius = 12
        return view
    }()
    
    // MARK: - 初始化
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupViews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - 设置UI
    
    private func setupViews() {
        selectionStyle = .none
        backgroundColor = .clear
        
        contentView.addSubview(containerView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(switchControl)
        
        containerView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(16)
            make.top.bottom.equalToSuperview().inset(4)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
        }
        
        switchControl.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
        }
    }
    
    // MARK: - 动作
    
    @objc private func switchChanged(_ sender: UISwitch) {
        switchValueChanged?(sender.isOn)
    }
    
    // MARK: - 公共方法
    
    func configure(with model: ReminderItemModel) {
        titleLabel.text = model.title
        switchControl.isOn = model.isOn
    }
}

// MARK: - 自定义头视图
class CalendarEventReminderHeaderView: UITableViewHeaderFooterView {
    
    // MARK: - 属性
    
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textColor = UIColor.baseTheme.current.cellTitleColor
        return label
    }()
    
    // MARK: - 初始化
    
    override init(reuseIdentifier: String?) {
        super.init(reuseIdentifier: reuseIdentifier)
        setupViews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - 设置UI
    
    private func setupViews() {
        contentView.backgroundColor = UIColor.baseTheme.current.bgColor
        
        addSubview(titleLabel)
        
        titleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
        }
    }
    
    // MARK: - 公共方法
    
    func configure(with title: String) {
        titleLabel.text = title
    }
} 