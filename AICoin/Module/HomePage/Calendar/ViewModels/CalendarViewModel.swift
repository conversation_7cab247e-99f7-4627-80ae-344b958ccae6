//
//  CalendarViewModel.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-18.
//  Copyright © 2024 AICoin. All rights reserved.
//

import Foundation

class CalendarViewModel {
    let dataSource: CalendarDataSource

    // 日历相关属性
    private(set) var currentDate: Date = Date()
    private(set) var selectedDate: Date = Date()
    private(set) var isImportantOnly: Bool = false

    // 事件列表相关属性
    private(set) var availableFilters: [EventTypeFilter] = EventTypeFilter.allFilters

    // 绑定属性，用于通知视图更新
    var onMonthChanged: ((Date) -> Void)?
    var onDateSelected: ((Date) -> Void)?
    var onEventsLoaded: (() -> Void)?
    var onImportantOnlyChanged: ((Bool) -> Void)?
    var onFilterChanged: (() -> Void)?
    var onEventsUpdated: (() -> Void)?

    init(dataSource: CalendarDataSource = .shared) {
        self.dataSource = dataSource
    }

    // MARK: - 日历相关方法

    func selectDate(_ date: Date) {
        selectedDate = date
        dataSource.selectedDate = date
        onDateSelected?(date)

        // 如果是使用模拟数据，则生成当天的模拟事件
        if UserDefaults.standard.bool(forKey: "UseCalendarMockData") {
            generateMockEventsForSelectedDate()
        }
    }

    // 生成当前选中日期的模拟事件
    private func generateMockEventsForSelectedDate() {
        var mockEvents: [CalendarEventModel] = []

        // 创建日期组件
        let calendar = Calendar.current
        var dateComponents = calendar.dateComponents([.year, .month, .day], from: selectedDate)

        // 添加ETF事件（普通）
        let etfEvent = CalendarEventModel(
            title: "比特币现货ETF净流入3.1亿美元，创下单日历史新高",
            date: createTimeFromComponents(dateComponents, hour: 9, minute: 30),
            isImportant: false,
            type: .etf
        )
        mockEvents.append(etfEvent)

        // 添加宏观数据事件（重要）
        let macroEvent = CalendarEventModel(
            title: "美联储主席鲍威尔在杰克逊霍尔年会上就经济前景发表讲话",
            date: createTimeFromComponents(dateComponents, hour: 10, minute: 0),
            isImportant: true,
            type: .macro
        )
        mockEvents.append(macroEvent)

        // 添加代币解锁事件（普通）
        let tokenEvent = CalendarEventModel(
            title: "Solana生态项目Jito解锁1000万枚代币，占总供应量的5%",
            date: createTimeFromComponents(dateComponents, hour: 14, minute: 0),
            isImportant: false,
            type: .token
        )
        mockEvents.append(tokenEvent)

        // 添加上新下架事件（重要）
        let listingEvent = CalendarEventModel(
            title: "币安将上线Celestia (TIA) 永续合约，提供高达20倍杠杆",
            date: createTimeFromComponents(dateComponents, hour: 16, minute: 0),
            isImportant: true,
            type: .listing
        )
        mockEvents.append(listingEvent)

        // 更新数据源中的事件
        dataSource.events = mockEvents

        // 通知事件已更新
        onEventsUpdated?()
    }

    // 辅助方法：从日期组件创建带有指定时间的日期
    private func createTimeFromComponents(_ components: DateComponents, hour: Int, minute: Int) -> Date {
        var newComponents = components
        newComponents.hour = hour
        newComponents.minute = minute
        newComponents.second = 0
        return Calendar.current.date(from: newComponents) ?? Date()
    }

    func moveToMonth(date: Date) {
        currentDate = date
        onMonthChanged?(date)
        loadEvents()
    }

    func moveToToday() {
        let today = Date()
        currentDate = today
        selectedDate = today
        dataSource.selectedDate = today
        onDateSelected?(today)
        onMonthChanged?(today)
        loadEvents()
    }

    func hasEvents(for date: Date) -> Bool {
        return dataSource.hasEvents(for: date)
    }

    func hasImportantEvents(for date: Date) -> Bool {
        return dataSource.hasImportantEvents(for: date)
    }

    func hasEventsByType(for date: Date, filterId: String) -> Bool {
        // 根据过滤器ID映射到对应的事件类型
        let type: CalendarEventModel.EventType
        switch filterId {
        case "etf":
            type = .etf
        case "macro":
            type = .macro
        case "token":
            type = .token
        case "listing":
            type = .listing
        default:
            return dataSource.hasEvents(for: date)
        }
        return dataSource.hasEventsByType(for: date, type: type)
    }

    // MARK: - 事件列表相关方法

    var events: [CalendarEventModel] {
        return dataSource.filteredEventsForSelectedDate()
    }

    var selectedFilters: [EventTypeFilter] {
        return dataSource.selectedFilters
    }

    var hasEvents: Bool {
        return !events.isEmpty
    }

    func applyFilter(_ filter: EventTypeFilter) {
        // 更新过滤条件并刷新数据
        var newFilters = availableFilters
        for i in 0..<newFilters.count {
            if newFilters[i].id == filter.id {
                newFilters[i] = EventTypeFilter(id: filter.id, title: filter.title, isActive: true)
            } else {
                newFilters[i] = EventTypeFilter(id: newFilters[i].id, title: newFilters[i].title, isActive: false)
            }
        }

        availableFilters = newFilters
        dataSource.selectedFilters = [filter]

        // 更新重要事件标志
        isImportantOnly = filter.id == "macro"

        onFilterChanged?()
        onImportantOnlyChanged?(isImportantOnly)
        loadEvents()
    }

    // MARK: - 共用方法

    func loadEvents() {
        dataSource.loadEvents(date: currentDate, filters: dataSource.selectedFilters) { [weak self] success in
            self?.onEventsLoaded?()
            self?.onEventsUpdated?()
        }
    }

    func filterImportantOnly(_ isImportantOnly: Bool) {
        self.isImportantOnly = isImportantOnly

        // 更新数据源中的过滤器和可用过滤器
        // 使用宏观数据过滤器代替重要事件过滤器
        let filter = isImportantOnly ? EventTypeFilter.macro : EventTypeFilter.all
        applyFilter(filter)
    }

    func formatMonthYear(date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月"
        return formatter.string(from: date)
    }

    func formatDateToString(date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: date)
    }
}