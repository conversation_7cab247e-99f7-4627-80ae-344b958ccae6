//
//  CalendarViewModel.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-18.
//  Copyright © 2024 AICoin. All rights reserved.
//

import Foundation

class CalendarViewModel {
    let dataSource: CalendarDataSource

    // 日历相关属性
    private(set) var currentDate: Date = Date()
    private(set) var selectedDate: Date = Date()
    private(set) var isImportantOnly: Bool = false
    private(set) var isWordsLoaded: Bool = false

    // 事件列表相关属性
    private(set) var availableFilters: [EventTypeFilter] = [EventTypeFilter.all]

    // 绑定属性，用于通知视图更新
    var onMonthChanged: ((Date) -> Void)?
    var onDateSelected: ((Date) -> Void)?
    var onEventsLoaded: (() -> Void)?
    var onImportantOnlyChanged: ((Bool) -> Void)?
    var onFilterChanged: (() -> Void)?
    var onEventsUpdated: (() -> Void)?
    var onErrorOccurred: ((Error) -> Void)?
    var onWordsLoadingCompleted: ((Bool) -> Void)?
    var onMarksUpdated: (() -> Void)?

    init(dataSource: CalendarDataSource = .shared) {
        self.dataSource = dataSource
        setupDataSourceCallbacks()
    }

    private func setupDataSourceCallbacks() {
        dataSource.onWordsLoaded = { [weak self] words in
            self?.handleWordsLoaded(words)
        }
    }

    // MARK: - Words初始化

    /// 初始化事件分类words
    func initializeEventWords() {
        dataSource.loadEventWords { [weak self] success in
            DispatchQueue.main.async {
                self?.onWordsLoadingCompleted?(success)
            }
        }
    }

    private func handleWordsLoaded(_ words: [String]) {
        isWordsLoaded = true
        availableFilters = dataSource.createFiltersFromWords()
        onFilterChanged?()
    }

    // MARK: - 日历相关方法

    func selectDate(_ date: Date) {
        selectedDate = date
        dataSource.selectedDate = date
        onDateSelected?(date)

        loadEventsForSelectedDate()
    }

    func moveToMonth(date: Date) {
        currentDate = date
        onMonthChanged?(date)

        // 确保加载标记数据
        dataSource.ensureMarksLoaded(forMonth: date) { [weak self] success in
            guard let self = self else { return }
            // 通知UI刷新标记数据
            DispatchQueue.main.async {
                // 调用标记更新回调，让UI能够刷新标记
                self.onMarksUpdated?()
            }
        }
    }

    func moveToToday() {
        let today = Date()
        currentDate = today
        selectedDate = today
        dataSource.selectedDate = today
        onDateSelected?(today)
        onMonthChanged?(today)

        // 初始化加载标记数据
//        dataSource.loadCalendarMarks(forMonth: today) { [weak self] success in
//            guard let self = self else { return }
//            // 通知UI刷新标记数据
//            DispatchQueue.main.async {
//                // 先调用标记更新回调，让UI能够刷新标记
//                self.onMarksUpdated?()
//                // 然后加载事件数据
//                // self.loadEvents()
//            }
//        }
    }

    func hasEvents(for date: Date) -> Bool {
        return dataSource.hasEvents(for: date)
    }

    func hasImportantEvents(for date: Date) -> Bool {
        return dataSource.hasImportantEvents(for: date)
    }

    func hasExpiredImportantEvents(for date: Date) -> Bool {
        return dataSource.hasExpiredImportantEvents(for: date)
    }

    // MARK: - 事件列表相关方法

    var events: [CalendarEventModel] {
        return dataSource.filteredEventsForSelectedDate()
    }

    var selectedFilters: [EventTypeFilter] {
        return dataSource.selectedFilters
    }

    var hasEvents: Bool {
        return !events.isEmpty
    }

    func applyFilter(_ filter: EventTypeFilter) {
        // 更新过滤条件并刷新数据
        var newFilters = availableFilters
        for i in 0..<newFilters.count {
            if newFilters[i].id == filter.id {
                newFilters[i] = EventTypeFilter(id: filter.id, title: filter.title, isActive: true)
            } else {
                newFilters[i] = EventTypeFilter(
                    id: newFilters[i].id, title: newFilters[i].title, isActive: false)
            }
        }

        availableFilters = newFilters
        dataSource.selectedFilters = [filter]

        onFilterChanged?()

        // 重新加载当前选中日期的事件数据
        loadEventsForSelectedDate()
    }

    // MARK: - 新增方法：加载选中日期的事件
    private func loadEventsForSelectedDate() {
        dataSource.loadEvents(date: selectedDate, filters: dataSource.selectedFilters) {
            [weak self] success in
            DispatchQueue.main.async {
                guard let self = self else { return }

                if success {
                    self.onEventsLoaded?()
                    self.onEventsUpdated?()
                } else {
                    // 处理加载失败的情况
                    let error = NSError(
                        domain: "CalendarError", code: -1,
                        userInfo: [NSLocalizedDescriptionKey: "加载事件数据失败"])
                    print("加载事件数据失败: \(error)")
                }
            }
        }
    }

    // MARK: - 共用方法

    func loadEvents() {
        dataSource.loadEvents(date: currentDate, filters: dataSource.selectedFilters) {
            [weak self] success in
            self?.onEventsLoaded?()
            self?.onEventsUpdated?()
        }
    }

    func filterImportantOnly(_ isImportantOnly: Bool) {
        self.isImportantOnly = isImportantOnly

        dataSource.filterImportantOnly = isImportantOnly
        self.onEventsUpdated?()
        // onImportantOnlyChanged?(isImportantOnly)
    }

    func formatDateToString(date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: date)
    }

    // MARK: - 刷新功能

    /// 刷新当前选中日期的事件数据（清除缓存）
    func refreshEvents() {
        // 清除页面级缓存
        CalendarEventManager.shared.clearSessionCache()

        // 重新加载标记数据
        dataSource.loadCalendarMarks(forMonth: currentDate) { [weak self] success in
            guard let self = self else { return }
            // 通知UI刷新标记数据
            DispatchQueue.main.async {
                // 先调用标记更新回调，让UI能够刷新标记
                self.onMarksUpdated?()
                // 重新加载事件数据
                self.loadEventsForSelectedDate()
            }
        }
    }
}
