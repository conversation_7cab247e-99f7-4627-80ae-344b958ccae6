//
//  EventTypeTabView.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-18.
//  Copyright © 2024 AICoin. All rights reserved.
//

import UIKit
import SnapKit

class EventTypeTabView: UIView {

    // MARK: - 属性

    var onFilterSelected: ((EventTypeFilter) -> Void)?
    private var filters: [EventTypeFilter] = []
    private var filterButtons: [UIButton] = []

    // MARK: - UI组件

    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsHorizontalScrollIndicator = false
        return scrollView
    }()

    private lazy var stackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 4  // 增加间距，符合设计图
        stackView.alignment = .center
        stackView.distribution = .fillProportionally  // 使按钮根据内容宽度分布
        return stackView
    }()

    // MARK: - 初始化

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupViews()
    }

    // MARK: - 设置

    private func setupViews() {
        backgroundColor = .white


        addSubview(scrollView)
        scrollView.addSubview(stackView)

        // 设置滚动视图约束
        scrollView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }

        // 设置堆栈视图约束
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(scrollView)
        }
    }

    // MARK: - 公共方法

    func configure(with filters: [EventTypeFilter]) {
        self.filters = filters

        // 清除现有的按钮
        stackView.arrangedSubviews.forEach { $0.removeFromSuperview() }
        filterButtons.removeAll()

        // 添加新的过滤按钮
        for filter in filters {
            let button = createFilterButton(filter)
            stackView.addArrangedSubview(button)
            filterButtons.append(button)
        }

        layoutIfNeeded()
    }

    // MARK: - 私有方法

    private func createFilterButton(_ filter: EventTypeFilter) -> UIButton {
        let button = UIButton(type: .custom)
        button.setTitle(filter.title, for: .normal)

        if filter.isActive {
            // 选中状态：字体颜色为 #1478FA，字体加粗
            button.setTitleColor(UIColor(hexString: "#1478FA"), for: .normal)
            button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        } else {
            // 未选中状态：字体颜色为 #7A8899，字体正常
            button.setTitleColor(UIColor(hexString: "#7A8899"), for: .normal)
            button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        }

        // 设置按钮背景为透明
        button.backgroundColor = .clear

        // 设置内边距
        button.contentEdgeInsets = UIEdgeInsets(top: 8, left: 16, bottom: 8, right: 16)
        button.addTarget(self, action: #selector(filterButtonTapped(_:)), for: .touchUpInside)
        button.tag = filters.firstIndex(where: { $0.id == filter.id }) ?? 0

        return button
    }



    @objc private func filterButtonTapped(_ sender: UIButton) {
        guard sender.tag < filters.count else { return }

        let filter = filters[sender.tag]
        onFilterSelected?(filter)
    }

    // 更新按钮UI状态
    private func updateButtons() {
        for (index, button) in filterButtons.enumerated() {
            let filter = filters[index]

            if filter.isActive {
                // 选中状态：字体颜色为 #1478FA，字体加粗
                button.setTitleColor(UIColor(hexString: "#1478FA"), for: .normal)
                button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
            } else {
                // 未选中状态：字体颜色为 #7A8899，字体正常
                button.setTitleColor(UIColor(hexString: "#7A8899"), for: .normal)
                button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
            }

            // 设置按钮背景为透明
            button.backgroundColor = .clear
        }
    }
}