//
//  CalendarFoldableView.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-18.
//  Copyright © 2024 AICoin. All rights reserved.
//

import UIKit
import SnapKit

protocol CalendarFoldableViewDelegate: AnyObject {
    func calendarFoldableView(_ view: CalendarFoldableView, didSelectDate date: Date)
    func calendarFoldableView(_ view: CalendarFoldableView, didChangeMonth date: Date)
    func calendarFoldableView(_ view: CalendarFoldableView, didChangeFoldState isCollapsed: Bool)
    func calendarFoldableView(_ view: CalendarFoldableView, didChangeImportantOnly isImportantOnly: Bool)
    func calendarFoldableViewDidTapEventReminder(_ view: CalendarFoldableView)
}

class CalendarFoldableView: UIView {

    // MARK: - 属性

    weak var delegate: CalendarFoldableViewDelegate?
    private var viewModel: CalendarViewModel!

    private(set) var isCollapsed = false
    private var calendarHeight: Constraint?

    // 日历视图高度常量
    private let expandedHeight: CGFloat = 240  // 展开状态高度（月视图）
    private let collapsedHeight: CGFloat = 60  // 折叠状态高度（周视图）

    // 视图模式：月视图或周视图
    enum CalendarViewMode {
        case month // 月视图，显示6行
        case week  // 周视图，显示1行
    }

    private var currentViewMode: CalendarViewMode = .month

    // MARK: - UI组件

    private let foldButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage.home.image(name: "icon_calendar_up"), for: .normal)
        button.setImage(UIImage.home.image(name: "icon_calendar_down"), for: .selected)
        return button
    }()

    // 添加白色栏来放置折叠按钮
    private let buttonBar: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.baseTheme.current.cellBgColor // 使用与日历相同的背景色
        return view
    }()

    private let headerView = CalendarHeaderView()
    private let calendarView = CalendarView()

    // MARK: - 初始化

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupViews()
    }

    // MARK: - 设置

    private func setupViews() {
        backgroundColor = UIColor.baseTheme.current.bgColor
        clipsToBounds = true

        // 添加子视图
        addSubview(headerView)
        addSubview(calendarView)
        addSubview(buttonBar)
        buttonBar.addSubview(foldButton)

        // 设置headerView约束
        headerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
        }

        // 设置calendarView约束
        calendarView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom)
            make.left.right.equalToSuperview()
            calendarHeight = make.height.equalTo(expandedHeight).constraint
            make.bottom.equalTo(buttonBar.snp.top)
        }

        // 设置buttonBar约束
        buttonBar.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(20) // 设置白色栏的高度
        }

        // 设置foldButton约束
        foldButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.height.equalTo(20)
        }

        foldButton.addTarget(self, action: #selector(foldButtonTapped), for: .touchUpInside)
    }

    // MARK: - 公共方法

    func configure(with viewModel: CalendarViewModel) {
        self.viewModel = viewModel
        calendarView.configure(with: viewModel)
        setupBindings()

        // 初始设置
        let today = Date()
        let isToday = Calendar.current.isDateInToday(viewModel.selectedDate)
        headerView.updateDate(viewModel.selectedDate, isToday: isToday)

        // 检查当前月份是否包含今天
        let isCurrentMonthContainsToday = self.isMonthContainsToday(viewModel.currentDate)

        // 只有当前月份不包含今天，或者当前选中的日期不是今天时，才显示今天按钮
        let shouldShowTodayButton = !isCurrentMonthContainsToday || !isToday

        // 更新今天按钮的显示状态
        headerView.updateTodayButtonVisibility(shouldShowTodayButton)
    }

    @objc private func foldButtonTapped() {
        // 切换视图模式：月视图 <-> 周视图
        let newMode: CalendarViewMode = currentViewMode == .month ? .week : .month
        switchViewMode(to: newMode, animated: false)
    }

    func switchViewMode(to mode: CalendarViewMode, animated: Bool = true) {
        guard currentViewMode != mode else { return }

        // 更新当前视图模式
        currentViewMode = mode
        isCollapsed = mode == .week
        foldButton.isSelected = isCollapsed

        // 更新日历视图高度约束
        calendarHeight?.update(offset: mode == .week ? collapsedHeight : expandedHeight)
        self.calendarView.switchViewMode(to: mode)

        // 根据当前视图模式和日期更新今天按钮的显示状态
        updateTodayButtonVisibility()

//        if animated {
//            UIView.animate(withDuration: 0.3) {
//                self.layoutIfNeeded()
//            } completion: { _ in
//                self.delegate?.calendarFoldableView(self, didChangeFoldState: self.isCollapsed)
//            }
//        } else {
//            layoutIfNeeded()
//            delegate?.calendarFoldableView(self, didChangeFoldState: isCollapsed)
//        }
    }

    // 根据当前视图模式和日期更新今天按钮的显示状态
    private func updateTodayButtonVisibility() {
        // 根据当前视图模式决定如何检查是否包含今天
        let containsToday: Bool
        if currentViewMode == .week {
            // 周视图：检查当前周是否包含今天
            containsToday = isWeekContainsToday(viewModel.currentDate)
        } else {
            // 月视图：检查当前月份是否包含今天
            containsToday = isMonthContainsToday(viewModel.currentDate)
        }

        // 检查当前选中的日期是否是今天
        let isSelectedDateToday = Calendar.current.isDateInToday(viewModel.selectedDate)

        // 只有当前视图不包含今天，或者当前选中的日期不是今天时，才显示今天按钮
        let shouldShowTodayButton = !containsToday || !isSelectedDateToday

        // 更新今天按钮的显示状态
        headerView.updateTodayButtonVisibility(shouldShowTodayButton)
    }

    // MARK: - 私有方法

    private func setupBindings() {
        calendarView.delegate = self
        headerView.delegate = self

        // // 保存之前的回调
        // let previousOnMonthChanged = viewModel.onMonthChanged
        // let previousOnDateSelected = viewModel.onDateSelected

        viewModel.onMonthChanged = { [weak self] date in
            guard let self = self else { return }

            // 更新今天按钮的显示状态
            self.updateTodayButtonVisibility()

            // 通知外部
            self.delegate?.calendarFoldableView(self, didChangeMonth: date)
        }

        viewModel.onDateSelected = { [weak self] date in
            guard let self = self else { return }

            // 检查是否是今天
            let isToday = Calendar.current.isDateInToday(date)

            // 更新头部视图
            self.headerView.updateDate(date, isToday: isToday)

            // 更新今天按钮的显示状态
            self.updateTodayButtonVisibility()
        }
    }


    // 检查给定日期的月份是否包含今天
    private func isMonthContainsToday(_ date: Date) -> Bool {
        let calendar = Calendar.current
        let today = Date()

        // 获取date和today的年月
        let dateComponents = calendar.dateComponents([.year, .month], from: date)
        let todayComponents = calendar.dateComponents([.year, .month], from: today)

        // 如果年月相同，则当前月份包含今天
        return dateComponents.year == todayComponents.year && dateComponents.month == todayComponents.month
    }

    // 检查给定日期所在的周是否包含今天
    private func isWeekContainsToday(_ date: Date) -> Bool {
        let calendar = Calendar.current
        let today = Date()

        // 获取date所在周的起始日期和结束日期
        let dateWeekday = calendar.component(.weekday, from: date)
        let daysToSubtract = dateWeekday - calendar.firstWeekday

        guard let startOfWeek = calendar.date(byAdding: .day, value: -daysToSubtract, to: date) else {
            return false
        }

        guard let endOfWeek = calendar.date(byAdding: .day, value: 6, to: startOfWeek) else {
            return false
        }

        // 检查今天是否在这个周范围内
        return (startOfWeek...endOfWeek).contains(calendar.startOfDay(for: today))
    }
}

// MARK: - CalendarViewDelegate
extension CalendarFoldableView: CalendarViewDelegate {
    func calendarView(_ calendarView: CalendarView, didSelectDate date: Date) {
        // 选择日期时更新头部
        let isToday = Calendar.current.isDateInToday(date)
        headerView.updateDate(date, isToday: isToday)

        // 更新今天按钮的显示状态
        updateTodayButtonVisibility()

        delegate?.calendarFoldableView(self, didSelectDate: date)
    }
}

// MARK: - CalendarHeaderViewDelegate
extension CalendarFoldableView: CalendarHeaderViewDelegate {
    func calendarHeaderViewDidTapToday(_ headerView: CalendarHeaderView) {
        viewModel.moveToToday()

        // 直接调用日历视图的方法
        let today = Date()
        calendarView.scrollToDate(today, animated: true)
        calendarView.selectDates([today])

        // 更新头部视图
        let isToday = true
        headerView.updateDate(today, isToday: isToday)

        // 隐藏今天按钮，因为已经选中了今天
        headerView.updateTodayButtonVisibility(false)
    }

    func calendarHeaderViewDidTapEventReminder(_ headerView: CalendarHeaderView) {
        // 跳转到事件提醒页面
        delegate?.calendarFoldableViewDidTapEventReminder(self)
    }

    func calendarHeaderViewDidChangeImportantOnly(_ headerView: CalendarHeaderView, isImportantOnly: Bool) {
        // 更新ViewModel中的重要事件过滤
        viewModel.filterImportantOnly(isImportantOnly)

        // 通知外部处理
        delegate?.calendarFoldableView(self, didChangeImportantOnly: isImportantOnly)
    }
}
