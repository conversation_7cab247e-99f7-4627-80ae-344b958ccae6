//
//  CalendarDateCell.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-18.
//  Copyright © 2024 AICoin. All rights reserved.
//

import UIKit
import JTAppleCalendar
import SnapKit

class CalendarDateCell: JTACDayCell {

    // 背景视图，覆盖整个单元格，增加可点击区域
    let bgView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    let dateLabel: UILabel = {
        let label = UILabel()
        label.textAlignment = .center
        label.font = UIFont.systemFont(ofSize: 16)
        return label
    }()

    let selectedView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hexString: "#1478FA")
        view.layer.cornerRadius = 4
        view.isHidden = true
        return view
    }()

    let todayView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hexString: "#1478FA").withAlphaComponent(0.1)
        view.layer.cornerRadius = 4
        view.isHidden = true
        return view
    }()

    let eventIndicator: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hexString: "#1478FA")
        view.layer.cornerRadius = 2
        view.isHidden = true
        return view
    }()

    override init(frame: CGRect) {
        super.init(frame: frame)

        // 添加视图
        addSubview(bgView)
        addSubview(selectedView)
        addSubview(todayView)
        addSubview(dateLabel)
        addSubview(eventIndicator)

        // 设置约束
        bgView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        selectedView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(24) // 增加选中视图的大小
        }

        todayView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(24) // 增加今天视图的大小
        }

        dateLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }

        eventIndicator.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview()
            make.width.height.equalTo(4)
        }
    }

    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}
