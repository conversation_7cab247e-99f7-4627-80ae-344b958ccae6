//
//  CalendarHeaderView.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-18.
//  Copyright © 2024 AICoin. All rights reserved.
//

import UIKit
import SnapKit

protocol CalendarHeaderViewDelegate: AnyObject {
    func calendarHeaderViewDidTapToday(_ headerView: CalendarHeaderView)
    func calendarHeaderViewDidTapEventReminder(_ headerView: CalendarHeaderView)
    func calendarHeaderViewDidChangeImportantOnly(_ headerView: CalendarHeaderView, isImportantOnly: Bool)
}

class CalendarHeaderView: UIView {

    // MARK: - 属性

    weak var delegate: CalendarHeaderViewDelegate?

    var onPreviousMonth: (() -> Void)?
    var onNextMonth: (() -> Void)?

    private var isToday: Bool = true {
        didSet {
            todayButton.isHidden = isToday
        }
    }

    private var isImportantOnly: Bool = false {
        didSet {
            importantCheckbox.isSelected = isImportantOnly
        }
    }

    // MARK: - UI组件

    private lazy var dateLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textColor = UIColor.baseTheme.current.cellTitleColor
        return label
    }()

    private lazy var todayButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("Now".base.localized, for: .normal)
        button.setTitleColor(UIColor(hexString: "#1478FA"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 10)
        button.backgroundColor = UIColor(hexString: "#1478FA").withAlphaComponent(0.1)
        button.layer.cornerRadius = 13
        button.isHidden = true
        button.addTarget(self, action: #selector(todayButtonTapped), for: .touchUpInside)
        return button
    }()

    private lazy var eventReminderButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage.home.image(name: "icon_event_reminder_entry"), for: .normal)
        button.addTarget(self, action: #selector(eventReminderButtonTapped), for: .touchUpInside)
        return button
    }()

    private lazy var importantCheckbox: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage.ticker.image(name: "Ticker_Prealert_Switch_Off_Normal"), for: .normal)
        button.setImage(UIImage.ticker.image(name: "Ticker_Prealert_Switch_On_Normal"), for: .selected)
        button.addTarget(self, action: #selector(importantCheckboxTapped), for: .touchUpInside)
        return button
    }()

    private lazy var importantLabel: UILabel = {
        let label = UILabel()
        label.text = "只看重要".base.localized
        label.textColor = UIColor.baseTheme.current.cellSubtitleColor
        label.font = UIFont.systemFont(ofSize: 12)
        return label
    }()

    // MARK: - 初始化

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupViews()
    }

    // MARK: - 设置

    private func setupViews() {
        backgroundColor = .designKit.secondaryBackgroundNew

        addSubview(dateLabel)
        addSubview(todayButton)
        addSubview(eventReminderButton)
        addSubview(importantCheckbox)
        addSubview(importantLabel)

        dateLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
        }

        let rightStackView = UIStackView()
        rightStackView.axis = .horizontal
        rightStackView.spacing = 12
        rightStackView.alignment = .center

        addSubview(rightStackView)
        rightStackView.addArrangedSubview(todayButton)
        rightStackView.addArrangedSubview(eventReminderButton)

        rightStackView.snp.makeConstraints { make in
            make.right.equalTo(importantLabel.snp.left).offset(-12)
            make.centerY.equalToSuperview()
        }

        todayButton.snp.makeConstraints { make in
            make.width.height.equalTo(26)
        }

        eventReminderButton.snp.makeConstraints { make in
            make.width.height.equalTo(16)
        }

        importantLabel.snp.makeConstraints { make in
            make.right.equalTo(importantCheckbox.snp.left).offset(-8)
            make.centerY.equalToSuperview()
        }

        importantCheckbox.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.equalTo(51)
            make.height.equalTo(31)
        }
    }

    // MARK: - 公共方法

    func updateDate(_ date: Date, isToday: Bool) {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        dateLabel.text = formatter.string(from: date)
        self.isToday = isToday
    }

    func setImportantOnly(_ isImportantOnly: Bool) {
        self.isImportantOnly = isImportantOnly
    }

    // 直接更新今天按钮的可见性
    func updateTodayButtonVisibility(_ isVisible: Bool) {
        todayButton.isHidden = !isVisible
    }

    // MARK: - 动作

    @objc private func todayButtonTapped() {
        delegate?.calendarHeaderViewDidTapToday(self)
        self.isToday = true
    }

    @objc private func eventReminderButtonTapped() {
        delegate?.calendarHeaderViewDidTapEventReminder(self)
    }

    @objc private func importantCheckboxTapped() {
        isImportantOnly.toggle()
        delegate?.calendarHeaderViewDidChangeImportantOnly(self, isImportantOnly: isImportantOnly)
    }
}
