//
//  CalendarViewController.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-18.
//  Copyright © 2024 AICoin. All rights reserved.
//

import UIKit
import JTAppleCalendar
import SnapKit

class CalendarViewController: AICBaseViewController {
    
    // MARK: - 属性
    
    private var selectedDate: Date = Date()
    private var currentDate: Date = Date()
    private var onlyShowImportant: Bool = false
    private var eventsByDate: [String: [CalendarEventModel]] = [:]
    
    // MARK: - UI组件
    
    // 导航栏
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16)
        label.textColor = UIColor.baseTheme.current.cellTitleColor
        label.text = self.formatDateToString(date: self.currentDate)
        return label
    }()
    
    private lazy var todayButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("今", for: .normal)
        button.setTitleColor(UIColor(hexString: "#1478FA"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.backgroundColor = UIColor(hexString: "#1478FA").withAlphaComponent(0.1)
        button.layer.cornerRadius = 20
        button.addTarget(self, action: #selector(todayButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var optionsButton: UIButton = {
        let button = UIButton(type: .custom)
        let image = UIImage(named: "icon_calendar_options")
        button.setImage(image, for: .normal)
        button.addTarget(self, action: #selector(optionsButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var importantSwitch: UISwitch = {
        let switchControl = UISwitch()
        switchControl.onTintColor = UIColor(hexString: "#1478FA")
        switchControl.addTarget(self, action: #selector(importantSwitchChanged), for: .valueChanged)
        return switchControl
    }()
    
    private lazy var importantLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12)
        label.textColor = UIColor(hexString: "#7A8899")
        label.text = "只看重要"
        return label
    }()
    
    // 日历
    private lazy var calendarView: JTACMonthView = {
        let calendar = JTACMonthView()
        calendar.backgroundColor = .white
        calendar.scrollingMode = .stopAtEachCalendarFrame
        calendar.showsHorizontalScrollIndicator = false
        calendar.allowsMultipleSelection = false
//        calendar.isRangeSelectionUsed = false
        calendar.scrollDirection = .horizontal
        calendar.calendarDelegate = self
        calendar.calendarDataSource = self
        calendar.register(CalendarDateCell.self, forCellWithReuseIdentifier: "CalendarDateCell")
        return calendar
    }()
    
    private lazy var weekdayStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.alignment = .center
        
        let weekdays = ["日", "一", "二", "三", "四", "五", "六"]
        for weekday in weekdays {
            let label = UILabel()
            label.text = weekday
            label.textAlignment = .center
            label.font = UIFont.systemFont(ofSize: 12)
            label.textColor = UIColor(hexString: "#7A8899")
            stackView.addArrangedSubview(label)
        }
        
        return stackView
    }()
    
    private lazy var headerMonthLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textColor = UIColor.baseTheme.current.cellTitleColor
        label.textAlignment = .center
        return label
    }()
    
    private lazy var previousButton: UIButton = {
        let button = UIButton(type: .custom)
        let image = UIImage(named: "icon_calendar_previous")
        button.setImage(image, for: .normal)
        button.addTarget(self, action: #selector(previousMonthButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var nextButton: UIButton = {
        let button = UIButton(type: .custom)
        let image = UIImage(named: "icon_calendar_next")
        button.setImage(image, for: .normal)
        button.addTarget(self, action: #selector(nextMonthButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 事件列表视图
    private lazy var eventTableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .plain)
        tableView.backgroundColor = UIColor.baseTheme.current.bgColor
        tableView.separatorStyle = .none
        tableView.register(CalendarEventCell.self, forCellReuseIdentifier: "CalendarEventCell")
        tableView.delegate = self
        tableView.dataSource = self
        tableView.isHidden = true
        return tableView
    }()
    
    private lazy var noEventLabel: UILabel = {
        let label = UILabel()
        label.text = "当前日期暂无事件"
        label.textAlignment = .center
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = UIColor.baseTheme.current.cellSubtitleColor
        label.isHidden = true
        return label
    }()
    
    // MARK: - 生命周期
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupViews()
        setupCalendarView()
        configureToolbar()
        loadEvents(for: Date())
    }
    
    // MARK: - 私有方法
    
    private func setupViews() {
        self.title = "日历"
        view.backgroundColor = UIColor.baseTheme.current.bgColor
        
        // 添加顶部日期显示
        view.addSubview(titleLabel)
        view.addSubview(todayButton)
        view.addSubview(optionsButton)
        view.addSubview(importantLabel)
        view.addSubview(importantSwitch)
        
        // 添加星期标题栏
        view.addSubview(weekdayStackView)
        
        // 添加日历视图
        view.addSubview(calendarView)
        
        // 添加事件表格视图
        view.addSubview(eventTableView)
        view.addSubview(noEventLabel)
        
        // 设置约束
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(16)
            make.left.equalTo(view).offset(32)
            make.height.equalTo(20)
        }
        
        let rightStackView = UIStackView(arrangedSubviews: [importantLabel, importantSwitch, optionsButton, todayButton])
        rightStackView.axis = .horizontal
        rightStackView.spacing = 16
        rightStackView.alignment = .center
        view.addSubview(rightStackView)
        
        rightStackView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(16)
            make.right.equalTo(view).offset(-32)
            make.height.equalTo(42)
        }
        
        todayButton.snp.makeConstraints { make in
            make.width.height.equalTo(42)
        }
        
        weekdayStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(24)
            make.left.right.equalTo(view).inset(32)
            make.height.equalTo(48)
        }
        
        // 调整日历视图，使其高度为固定值，以便在下方显示事件列表
        calendarView.snp.makeConstraints { make in
            make.top.equalTo(weekdayStackView.snp.bottom).offset(8)
            make.left.right.equalTo(view)
            make.height.equalTo(340) // 适合显示6周的高度
        }
        
        // 事件列表视图
        eventTableView.snp.makeConstraints { make in
            make.top.equalTo(calendarView.snp.bottom).offset(8)
            make.left.right.equalTo(view)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom)
        }
        
        noEventLabel.snp.makeConstraints { make in
            make.center.equalTo(eventTableView)
            make.left.right.equalTo(eventTableView).inset(20)
        }
    }
    
    private func setupCalendarView() {
        calendarView.scrollToDate(Date(), animateScroll: false)
        calendarView.selectDates([Date()])
        updateHeaderMonthLabel()
    }
    
    private func configureToolbar() {
        let flexSpace = UIBarButtonItem(barButtonSystemItem: .flexibleSpace, target: nil, action: nil)
        
        let prevButton = UIBarButtonItem(image: UIImage(named: "icon_calendar_previous"), style: .plain, target: self, action: #selector(previousMonthButtonTapped))
        
        headerMonthLabel.text = formatMonthYear(date: Date())
        let titleItem = UIBarButtonItem(customView: headerMonthLabel)
        
        let nextButton = UIBarButtonItem(image: UIImage(named: "icon_calendar_next"), style: .plain, target: self, action: #selector(nextMonthButtonTapped))
        
        self.toolbarItems = [flexSpace, prevButton, flexSpace, titleItem, flexSpace, nextButton, flexSpace]
        self.navigationController?.setToolbarHidden(false, animated: false)
    }
    
    private func updateHeaderMonthLabel() {
        let visibleDates = calendarView.visibleDates()
        if let dateSegment = visibleDates.monthDates.first?.date {
            headerMonthLabel.text = formatMonthYear(date: dateSegment)
        }
    }
    
    private func formatDateToString(date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: date)
    }
    
    private func formatMonthYear(date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月"
        return formatter.string(from: date)
    }
    
    private func loadEvents(for date: Date) {
        let request = CalendarEventRequestModel(date: date, onlyImportant: onlyShowImportant)
        
        CalendarEventManager.shared.getMonthEvents(request: request) { [weak self] (events, error) in
            guard let self = self else { return }
            
            if let events = events {
                // 按日期分组事件
                self.eventsByDate.removeAll()
                
                for event in events {
                    let dateKey = self.formatDateToString(date: event.date)
                    if self.eventsByDate[dateKey] == nil {
                        self.eventsByDate[dateKey] = []
                    }
                    self.eventsByDate[dateKey]?.append(event)
                }
                
                // 刷新日历显示
                self.calendarView.reloadData()
                
                // 刷新当前选中日期的事件列表
                self.updateEventList()
            }
        }
    }
    
    private func updateEventList() {
        let dateKey = formatDateToString(date: selectedDate)
        let events = eventsByDate[dateKey] ?? []
        
        if events.isEmpty {
            eventTableView.isHidden = true
            noEventLabel.isHidden = false
        } else {
            eventTableView.isHidden = false
            noEventLabel.isHidden = true
            eventTableView.reloadData()
        }
    }
    
    private func hasEvents(for date: Date) -> Bool {
        let dateKey = formatDateToString(date: date)
        return eventsByDate[dateKey]?.isEmpty == false
    }
    
    private func hasImportantEvents(for date: Date) -> Bool {
        let dateKey = formatDateToString(date: date)
        return eventsByDate[dateKey]?.contains(where: { $0.isImportant }) == true
    }
    
    // MARK: - 动作事件
    
    @objc private func todayButtonTapped() {
        calendarView.scrollToDate(Date(), animateScroll: true)
        calendarView.selectDates([Date()])
        selectedDate = Date()
        titleLabel.text = formatDateToString(date: selectedDate)
        updateEventList()
    }
    
    @objc private func optionsButtonTapped() {
        // 显示日历选项菜单
        let alertController = UIAlertController(title: nil, message: nil, preferredStyle: .actionSheet)
        
        alertController.addAction(UIAlertAction(title: "查看全部事件", style: .default) { _ in
            // 处理查看全部事件
            self.showAllEvents()
        })
        
        alertController.addAction(UIAlertAction(title: "添加新事件", style: .default) { _ in
            // 处理添加新事件
            self.addNewEvent()
        })
        
        alertController.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        // iPad 需要特殊处理
        if let popoverController = alertController.popoverPresentationController {
            popoverController.sourceView = optionsButton
            popoverController.sourceRect = optionsButton.bounds
        }
        
        present(alertController, animated: true)
    }
    
    @objc private func importantSwitchChanged(_ sender: UISwitch) {
        onlyShowImportant = sender.isOn
        loadEvents(for: currentDate)
    }
    
    @objc private func previousMonthButtonTapped() {
        calendarView.scrollToSegment(.previous)
    }
    
    @objc private func nextMonthButtonTapped() {
        calendarView.scrollToSegment(.next)
    }
    
    private func showAllEvents() {
        // 显示所有事件的页面
        let alertController = UIAlertController(title: "所有事件", message: "这里将显示所有日历事件", preferredStyle: .alert)
        alertController.addAction(UIAlertAction(title: "确定", style: .default))
        present(alertController, animated: true)
    }
    
    private func addNewEvent() {
        // 添加新事件的页面
        let alertController = UIAlertController(title: "添加事件", message: "这里将显示添加事件的表单", preferredStyle: .alert)
        alertController.addAction(UIAlertAction(title: "确定", style: .default))
        present(alertController, animated: true)
    }
}

// MARK: - JTACMonthViewDataSource
extension CalendarViewController: JTACMonthViewDataSource {
    func configureCalendar(_ calendar: JTACMonthView) -> ConfigurationParameters {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy MM dd"
        
        let startDate = formatter.date(from: "2020 01 01")!
        let endDate = formatter.date(from: "2030 12 31")!
        
        return ConfigurationParameters(
            startDate: startDate,
            endDate: endDate,
            generateInDates: .forAllMonths,
            generateOutDates: .tillEndOfRow,
            firstDayOfWeek: .sunday,
            hasStrictBoundaries: true
        )
    }
}

// MARK: - JTACMonthViewDelegate
extension CalendarViewController: JTACMonthViewDelegate {
    func calendar(_ calendar: JTACMonthView, cellForItemAt date: Date, cellState: CellState, indexPath: IndexPath) -> JTACDayCell {
        let cell = calendar.dequeueReusableCell(withReuseIdentifier: "CalendarDateCell", for: indexPath) as! CalendarDateCell
        self.calendar(calendar, willDisplay: cell, forItemAt: date, cellState: cellState, indexPath: indexPath)
        return cell
    }
    
    func calendar(_ calendar: JTACMonthView, willDisplay cell: JTACDayCell, forItemAt date: Date, cellState: CellState, indexPath: IndexPath) {
        let calendarCell = cell as! CalendarDateCell
        
        // 设置日期文本
        calendarCell.dateLabel.text = cellState.text
        
        // 设置日期颜色
        if cellState.dateBelongsTo == .thisMonth {
            calendarCell.dateLabel.textColor = UIColor.baseTheme.current.cellTitleColor
        } else {
            calendarCell.dateLabel.textColor = UIColor(hexString: "#D9D9D9")
        }
        
        // 选中状态
        if cellState.isSelected {
            calendarCell.dateLabel.textColor = .white
            calendarCell.selectedView.isHidden = false
            calendarCell.selectedView.backgroundColor = UIColor(hexString: "#1478FA")
        } else {
            calendarCell.selectedView.isHidden = true
        }
        
        // 当前日期特殊标记
        let today = Calendar.current.isDateInToday(date)
        if today {
            if !cellState.isSelected {
                calendarCell.dateLabel.textColor = UIColor(hexString: "#1478FA")
                calendarCell.todayView.isHidden = false
                calendarCell.todayView.backgroundColor = UIColor(hexString: "#1478FA").withAlphaComponent(0.1)
            } else {
                calendarCell.todayView.isHidden = true
            }
        } else {
            calendarCell.todayView.isHidden = true
        }
        
        // 事件标记
        calendarCell.eventIndicator.isHidden = true
        
        if cellState.dateBelongsTo == .thisMonth {
            if hasEvents(for: date) {
                calendarCell.eventIndicator.isHidden = false
                
                if hasImportantEvents(for: date) {
                    calendarCell.eventIndicator.backgroundColor = UIColor(hexString: "#E54829")
                } else {
                    calendarCell.eventIndicator.backgroundColor = UIColor(hexString: "#1478FA")
                }
            }
        }
    }
    
    func calendar(_ calendar: JTACMonthView, didSelectDate date: Date, cell: JTACDayCell?, cellState: CellState, indexPath: IndexPath) {
        selectedDate = date
        titleLabel.text = formatDateToString(date: date)
        guard let calendarCell = cell as? CalendarDateCell else { return }
        
        calendarCell.dateLabel.textColor = .white
        calendarCell.selectedView.isHidden = false
        calendarCell.todayView.isHidden = true
        
        // 更新事件列表
        updateEventList()
    }
    
    func calendar(_ calendar: JTACMonthView, didDeselectDate date: Date, cell: JTACDayCell?, cellState: CellState, indexPath: IndexPath) {
        guard let calendarCell = cell as? CalendarDateCell else { return }
        
        // 恢复非选中状态
        if cellState.dateBelongsTo == .thisMonth {
            calendarCell.dateLabel.textColor = UIColor.baseTheme.current.cellTitleColor
        } else {
            calendarCell.dateLabel.textColor = UIColor(hexString: "#D9D9D9")
        }
        
        calendarCell.selectedView.isHidden = true
        
        // 如果是今天，恢复今天的样式
        if Calendar.current.isDateInToday(date) {
            calendarCell.dateLabel.textColor = UIColor(hexString: "#1478FA")
            calendarCell.todayView.isHidden = false
            calendarCell.todayView.backgroundColor = UIColor(hexString: "#1478FA").withAlphaComponent(0.1)
        }
    }
    
    func calendar(_ calendar: JTACMonthView, didScrollToDateSegmentWith visibleDates: DateSegmentInfo) {
        if let date = visibleDates.monthDates.first?.date {
            currentDate = date
            updateHeaderMonthLabel()
            loadEvents(for: date)
        }
    }
    
    // 添加单元格大小设置方法
    func calendar(_ calendar: JTACMonthView, sizeForItemAt date: Date, position: CellState, indexPath: IndexPath) -> CGSize {
        return CGSize(width: 48, height: 48)
    }
}

// MARK: - UITableViewDataSource, UITableViewDelegate
extension CalendarViewController: UITableViewDataSource, UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        let dateKey = formatDateToString(date: selectedDate)
        return eventsByDate[dateKey]?.count ?? 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "CalendarEventCell", for: indexPath) as! CalendarEventCell
        
        let dateKey = formatDateToString(date: selectedDate)
        if let events = eventsByDate[dateKey], indexPath.row < events.count {
            let event = events[indexPath.row]
            cell.configure(with: event)
        }
        
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 70
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let dateKey = formatDateToString(date: selectedDate)
        if let events = eventsByDate[dateKey], indexPath.row < events.count {
            let event = events[indexPath.row]
            // 处理事件点击
            let alertController = UIAlertController(title: event.title, message: event.content ?? "暂无详细内容", preferredStyle: .alert)
            alertController.addAction(UIAlertAction(title: "确定", style: .default))
            present(alertController, animated: true)
        }
    }
}

// MARK: - 自定义日历单元格
class CalendarDateCell: JTACDayCell {
    
    let dateLabel: UILabel = {
        let label = UILabel()
        label.textAlignment = .center
        label.font = UIFont.systemFont(ofSize: 16)
        return label
    }()
    
    let selectedView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hexString: "#1478FA")
        view.layer.cornerRadius = 4
        view.isHidden = true
        return view
    }()
    
    let todayView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hexString: "#1478FA").withAlphaComponent(0.1)
        view.layer.cornerRadius = 4
        view.isHidden = true
        return view
    }()
    
    let eventIndicator: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hexString: "#1478FA")
        view.layer.cornerRadius = 2
        view.isHidden = true
        return view
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        // 添加视图
        addSubview(selectedView)
        addSubview(todayView)
        addSubview(dateLabel)
        addSubview(eventIndicator)
        
        // 设置约束
        selectedView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(36)
        }
        
        todayView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(36)
        }
        
        dateLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        eventIndicator.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().offset(-8)
            make.width.height.equalTo(4)
        }
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}


// MARK: - 自定义事件单元格
class CalendarEventCell: UITableViewCell {
    
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textColor = UIColor.baseTheme.current.cellTitleColor
        return label
    }()
    
    private let typeLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12)
        label.textColor = .white
        label.textAlignment = .center
        label.layer.cornerRadius = 2
        label.clipsToBounds = true
        return label
    }()
    
    private let timeLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12)
        label.textColor = UIColor.baseTheme.current.cellSubtitleColor
        return label
    }()
    
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.baseTheme.current.cellBgColor
        view.layer.cornerRadius = 8
        return view
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupViews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupViews() {
        selectionStyle = .none
        backgroundColor = .clear
        
        contentView.addSubview(containerView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(typeLabel)
        containerView.addSubview(timeLabel)
        
        containerView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(16)
            make.top.bottom.equalToSuperview().inset(4)
        }
        
        typeLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.top.equalToSuperview().offset(12)
            make.width.equalTo(40)
            make.height.equalTo(20)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(typeLabel.snp.right).offset(8)
            make.right.equalToSuperview().offset(-12)
            make.top.equalToSuperview().offset(12)
        }
        
        timeLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.bottom.equalToSuperview().offset(-12)
        }
    }
    
    func configure(with event: CalendarEventModel) {
        titleLabel.text = event.title
        
        // 设置时间
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        timeLabel.text = formatter.string(from: event.date)
        
        // 设置事件类型
        switch event.type {
        case .normal:
            typeLabel.text = "普通"
            typeLabel.backgroundColor = UIColor(hexString: "#1478FA")
        case .important:
            typeLabel.text = "重要"
            typeLabel.backgroundColor = UIColor(hexString: "#E54829")
        case .announce:
            typeLabel.text = "公告"
            typeLabel.backgroundColor = UIColor(hexString: "#FF9500")
        case .news:
            typeLabel.text = "新闻"
            typeLabel.backgroundColor = UIColor(hexString: "#34C759")
        }
    }
}

// MARK: - UIColor扩展
extension UIColor {
    convenience init(hexString: String) {
        let hex = hexString.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int = UInt64()
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (255, 0, 0, 0)
        }
        self.init(red: CGFloat(r) / 255, green: CGFloat(g) / 255, blue: CGFloat(b) / 255, alpha: CGFloat(a) / 255)
    }
} 
