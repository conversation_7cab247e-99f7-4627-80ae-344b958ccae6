//
//  HomePageConstant.swift
//  AICoin
//
//  Created by <PERSON><PERSON><PERSON> on 2019/1/8.
//  Copyright © 2019 AICoin. All rights reserved.
//

import Foundation
import UIKit
import CandleChart

// 填充价格协议对象
protocol HomePageFillPriceProtocol: AnyObject {
    var cnyPrice: String { get }
    var usdPrice: String { get }
    var rawPrice: String? { get }
    
    // 项目对对象需要实现
    var marketListKey: String { get }
    // 根据定价获取主价格
    var getMainPrice: String { get }
}

// 支持填充价格视图协议
protocol HomePageFillPriceViewProtocol: AnyObject {
    var mainTitleLabel: UILabel { get }
    var subtitleLabel: UILabel { get }
    func updateLineStyle(isMultiply: Bool)
    
    func fillPrice(model: HomePageFillPriceProtocol & BaseCanCacheProtocol)
    func fillPrice(model: HomePageFillPriceProtocol)
}

extension HomePageFillPriceViewProtocol {
    /// 填充价格和自动着色
    ///
    /// - Parameter model: 模型
    func fillPrice(model: HomePageFillPriceProtocol & BaseCanCacheProtocol) {
        let obj = model as HomePageFillPriceProtocol
        self.fillPrice(model: obj)
        
        self.mainTitleLabel.textColor = UIColor.baseTheme.comparePriceColor(model.valueCompareResult)
    }
    
    /// 填充价格
    ///
    /// - Parameter model: 模型
    func fillPrice(model: HomePageFillPriceProtocol) {
        let rawPrice = TickerDataFormatHelper.getShortDigit(model.rawPrice ?? "")
        let cnyPrice = TickerDataFormatHelper.getShortDigit(model.cnyPrice)
        let usdPrice = TickerDataFormatHelper.getShortDigit(model.usdPrice)
        let marketListKey = model.marketListKey
        
        let placeHolder = BaseSwiftMethod.placeHolder()
        
        // 项目对包含有原平台价格
        if !rawPrice.isEmpty {
            let mainPrice = TickerTargetKey.price.currentPricing
            
            switch mainPrice.price {
            case .cny:
                self.mainTitleLabel.text = cnyPrice
            case .usd:
                self.mainTitleLabel.text = usdPrice
            case .default:
                self.mainTitleLabel.text = rawPrice
            }
            
            if TickerPreferenceManager.shared.pricingType.isMultiplyStyle {
                let marketListKey = marketListKey
                let subPrice = TickerTargetKey.price.currentSubPricing(marketListKey: marketListKey)
                let model = TickerTempDataPool.sharedInstance.fetchMarketListModelWithMarketListKey(marketListKey)
                
                switch subPrice.price {
                case .cny:
                    if cnyPrice == placeHolder {
                        self.subtitleLabel.text = cnyPrice
                    }
                    else {
                        self.subtitleLabel.text = subPrice.price.symbol() + cnyPrice
                    }
                case .usd:
                    if usdPrice == placeHolder {
                        self.subtitleLabel.text = usdPrice
                    }
                    else {
                        self.subtitleLabel.text = subPrice.price.symbol() + usdPrice
                    }
                case .default:
                    if rawPrice == placeHolder {
                        self.subtitleLabel.text = rawPrice
                    }
                    else {
                        let symbol = subPrice.price.symbol(fill: false, currency: model?.currencyString)
                        self.subtitleLabel.text = symbol + rawPrice
                    }
                }
                
                self.updateLineStyle(isMultiply: true)
            }
            else {
                self.updateLineStyle(isMultiply: false)
                self.subtitleLabel.text = nil
            }
        }
        // 项目没有原平台的价格
        else {
            let mainPrice = aic_appropriatePrice()
            
            if mainPrice == .CNY {
                self.mainTitleLabel.text = cnyPrice
            }
            else {
                self.mainTitleLabel.text = usdPrice
            }
            
            if TickerPreferenceManager.shared.pricingType.isMultiplyStyle {
                if mainPrice == .CNY {
                    if usdPrice == placeHolder {
                        self.subtitleLabel.text = usdPrice
                    }
                    else {
                        self.subtitleLabel.text = aic_signWithPrice(.USD) + usdPrice
                    }
                }
                else {
                    if cnyPrice == placeHolder {
                        self.subtitleLabel.text = usdPrice
                    }
                    else {
                        self.subtitleLabel.text = aic_signWithPrice(.CNY) + cnyPrice
                    }
                }
                
                self.updateLineStyle(isMultiply: true)
            }
            else {
                self.updateLineStyle(isMultiply: false)
                self.subtitleLabel.text = nil
            }
        }
    }
}

extension HomePageFillPriceProtocol {
    var getMainPrice: String {
        if let rawPrice = rawPrice {
            let priceConfig = TickerTargetKey.price.currentPricing
            switch priceConfig.price {
            case .cny:
                return self.cnyPrice
            case .usd:
                return self.usdPrice
            case .default:
                return rawPrice
            }
        }
        else {
            let priceConfig = aic_appropriatePrice()
            if priceConfig == .CNY {
                return self.cnyPrice
            }
            else {
                return self.usdPrice
            }
        }
    }
}

@objc public class HomePageConstant: NSObject {
    /// 特色功能
    static func defaultSpecialTools() -> [HomePageConstant.TickerTool] {
#if AIC_DISABLE_ADV
        //        商店版
        //        let tools: [HomePageConstant.TickerTool] = [
        //            .AI行情分析,
        //            .预警中心, .组合K线, .对比K线, .买卖点记录,
        ////                        .角标行情,
        //            .通知中心行情, .跨期价差, .K线大师
        //        ]
        
        let tools: [HomePageConstant.TickerTool] = [
            .AI行情分析,.PRO版K线, .大额成交,
            .指标胜率, .语音预警, .悬浮窗, .聊天室, .预警中心, .组合K线, .对比K线,
            .通知中心行情, .跨期价差, .K线大师
        ]
        return tools
#else
        let tools: [HomePageConstant.TickerTool] = [
            .AI行情分析,.PRO版K线,.大额成交,
            .指标胜率, .语音预警, .悬浮窗, .聊天室, .预警中心, .组合K线, .对比K线,
            .通知中心行情, .跨期价差, .K线大师
        ]
        return tools
#endif
    }
    
    /// 首页导航Tab
    enum SubTabType: String {
        case 市场
        case 功能
        case 榜单
    }
    
    enum TickerTool: String, CaseIterable {
        case 更多
        // 数据榜单
        case 各类指数
        case 热门排行
        case 近期上新
        case 行情异动
        case 爆仓统计
        case 跨期价差
        case 合约持仓
        case 特色榜单
        case 涨跌分布
        case 龙虎榜单
        case 资金流向
        // 盯盘神器
        case 预警中心
        case 语音预警
        case 指标胜率
        //        case 角标行情
        case 通知中心行情
        case AppleWatch
        // 高级K线
        case PRO版K线
        case 组合K线
        case 对比K线
        case K线大师
        case 大额成交
        // 实用工具
        case 币址查询
        case 挖矿数据
        case 矿池份额
        case BTC富豪榜
        // 社区资讯
        case 社区广场
        case 精选新闻
        case 精选快讯
        case 平台公告
        case VIP数据
        case 聊天室
        case 热点排行榜
        
        // 交易资产
        case 授权管理
        case 资产统计
        case 买卖点记录
        
        case DeFi
        case 实时盯盘
        case 悬浮窗
        case AI行情分析
        case 日历
        
        // 更多比较特殊 不能包含进枚举中
        static var allValidCases: [TickerTool] {
            return self.allCases.filter { $0 != .更多 }
        }
        
        private var imageName: String {
            switch self {
            case .聊天室: return "icon_tools_chat"
            case .VIP数据: return "icon_tools_vip"
            case .热门排行: return "icon_change_sign_hot"
            case .龙虎榜单: return "icon_change_sign_list"
            case .资金流向: return "icon_change_sign_money"
            case .近期上新: return "icon_change_sign_new"
            case .PRO版K线: return "icon_tools_ProKLine_gold"
            case .指标胜率: return "icon_tools_odds_gold"
            case .组合K线: return "icon_change_sign_comb_kline"
            case .对比K线: return "icon_change_sign_compare_kline"
            case .币址查询: return "icon_change_sign_search"
            case .挖矿数据: return "icon_change_sign_data"
            case .矿池份额: return "icon_change_sign_share"
            case .BTC富豪榜: return"icon_change_sign_btc"
            case .K线大师: return "icon_change_sign_kline_train"
            case .行情异动: return "icon_change_enter"
            case .更多: return "icon_change_sign_more"
            case .各类指数: return "icon_tools_index"
            case .跨期价差: return "icon_tools_price"
            case .合约持仓: return "icon_tools_position"
            case .特色榜单: return "icon_tools_special"
            case .涨跌分布: return "icon_tools_distribution"
            case .预警中心: return "icon_tools_warning"
            case .语音预警: return "icon_tools_voice"
                //            case .角标行情: return "icon_tools_corner"
            case .通知中心行情: return "icon_tools_notification"
            case .AppleWatch: return "icon_tools_watch"
            case .社区广场: return "icon_tools_square"
            case .精选新闻: return "icon_tools_news"
            case .精选快讯: return "icon_tools_hotnews"
            case .平台公告: return "icon_tools_announcement"
            case .授权管理: return "icon_tools_authorization"
            case .资产统计: return "icon_tools_assets"
            case .买卖点记录: return "icon_tools_record"
            case .爆仓统计: return "icon_tools_liq"
            case .DeFi: return "icon_tools_defi"
            case .实时盯盘: return "icon_tools_staringTool"
            case .悬浮窗: return "pip_icon"
            case .AI行情分析: return "icon_tools_ai"
            case .大额成交: return "icon_tools_bigtrades"
            case .热点排行榜: return "icon_tools_hot_point"
            case .日历: return "icon_tools_calendar"
            }
        }
        
        var image: UIImage? {
            if self == .悬浮窗 {
                return UIImage.home.imageForCommon(name: self.imageName)?.withColor(darkColor: UIColor(hex: "1060C8"))
            }
            else {
                return UIImage.home.image(name: self.imageName)
            }
        }
        
        /// 标签背景色
        var newImage: UIImage? {
            //            if self == .PRO版K线 {
            //                return UIImage.home.image(name: "icon_tools_red_bg")?.byTintColor(UIColor.init(hex: "#ED9F38"))
            //            }
            
            return UIImage.home.image(name: "icon_tools_red_bg")
        }
        
        var bgImage: UIImage? {
            return UIImage.home.image(name: self.imageName + "_bg")
        }
        
        var key: String {
            switch self {
            case .对比K线: return "compare_kline"
            case .各类指数: return "various_indices"
            case .预警中心: return "alert_center"
            case .PRO版K线: return "kline_pro"
            case .指标胜率: return "win_rate"
            case .行情异动: return "ticker_change"
            case .资金流向: return "fund_flow"
            case .热门排行: return "hot_rank"
            case .DeFi: return "h5_entry"
            case .特色榜单: return "feature_list"
            case .悬浮窗: return "pip_window"
            case .大额成交: return "large_trades"
            case .日历: return "calendar"
            default: return ""
            }
        }
        
        var description: String {
            switch self {
            case .预警中心: return "安心盯盘必备"
            case .组合K线: return "专业操盘神器"
            case .对比K线: return "走势一目了然"
            case .买卖点记录: return "带你复盘历史"
                //            case .角标行情: return "快速查看价格"
            case .通知中心行情: return "迅速获取行情"
            case .跨期价差: return "跨期套利机会"
            case .K线大师: return "趣味训练盘感"
            case .PRO版K线: return "主力大单跟踪"
            case .指标胜率: return "实时买卖信号"
            case .聊天室: return "高质交流"
            case .AI行情分析: return "一键技术分析"
            case .大额成交: return "大额成交"
            case .日历: return "重要事件提醒"    
                
            case .热门排行, .龙虎榜单, .资金流向, .近期上新, .币址查询, .挖矿数据, .矿池份额, .BTC富豪榜, .行情异动, .更多, .各类指数, .合约持仓, .特色榜单, .涨跌分布, .AppleWatch, .社区广场, .精选新闻, .精选快讯, .平台公告, .授权管理, .资产统计, .爆仓统计, .语音预警, .DeFi, .VIP数据, .实时盯盘, .悬浮窗, .热点排行榜:
                return ""
            }
        }
        
        var text: String {
            switch self {
            case .PRO版K线: return "主力大单"
                
            case .热门排行, .龙虎榜单, .资金流向, .近期上新, .币址查询, .挖矿数据, .矿池份额, .BTC富豪榜, .行情异动, .更多, .各类指数, .合约持仓, .特色榜单, .涨跌分布, .AppleWatch, .社区广场, .精选新闻, .精选快讯, .平台公告, .授权管理, .资产统计, .爆仓统计, .语音预警, .DeFi, .VIP数据, .实时盯盘, .悬浮窗, .AI行情分析,.预警中心,.组合K线,.对比K线,.买卖点记录,.通知中心行情,.跨期价差,.K线大师,.指标胜率,.聊天室, .大额成交, .热点排行榜, .日历:
                return rawValue
            }
        }
        
        func didSelected(from viewController: UIViewController?) {
            if self != .更多 && UserManager.share.isLogin {
                var tools = HomePagePreferenceManager.shared.selectedTickerTools
                tools.remove(self)
                tools.insert(self, at: 0)
                HomePagePreferenceManager.shared.selectedTickerTools = tools
            }
            
            var navVC: AICBaseNavigationController? {
                return viewController?.tabBarController?.selectedViewController as? AICBaseNavigationController
            }
            
            var tabBarVC: AICTabBarController? {
                return viewController?.tabBarController as? AICTabBarController
            }
            
            switch self {
            case .实时盯盘:
                let vc = MeStaringToolViewController()
                viewController?._push(to: vc)
            case .聊天室:
                MomentsConstant.showFlutterChatRoom(from: viewController)
            case .VIP数据:
                AppAnalytics.shared.track(eventWithCategory: "首页", action: "功能", name: "VIP数据")
                tabBarVC?.selected(vcType: .dynamic) {
                    NotificationCenter.default.post(name: Notification.Name("MomentsMoveToHotNewsPage"), object: 16)
                }
            case .DeFi:
                return
            case .指标胜率:
#if !AIC_DISABLE_ADV
                AppAnalytics.shared.track(eventWithCategory: "首页", action: "指标胜率点击", name: nil)
                let vc = StrategyHomeViewController()
                viewController?._push(to: vc)
#else
                MeCostant.showStrategyAlertIntroduction()
#endif
            case .PRO版K线:
                AppAnalytics.Ticker.clickGoToBigOrder(action: "主力大单页_入口", name: "主力大单页_入口_首页_金刚区_主力大单_点击")
#if !AIC_DISABLE_ADV
                //                let vc = BigOrderContainerViewController()
                let vc = StrategyAndProKLineViewController()
                vc.changePage(to: 0, subkey: "bitcoin")
                viewController?._push(to: vc)
#else
                MeCostant.showProKLineIntroduction()
#endif
                
            case .大额成交:
                AppAnalytics.Ticker.clickGoToBigTrades(action: "大额成交页_入口", name: "大额成交页_入口_首页_金刚区_大额成交_点击")
                let vc = StrategyAndProKLineViewController()
                vc.changePage(to: 1)
                viewController?._push(to: vc)
            case .语音预警:
                MeCostant.showVoiceAlertIntroduction()
            case .热门排行:
                viewController?._push(to: HomePageHotListViewController())
            case .行情异动:
                viewController?._push(to: HomePageChangeSignDetailViewController())
            case .龙虎榜单:
                viewController?._push(to: HomePageMainDataViewController())
            case .资金流向:
                viewController?._push(to: HomePageFundsflowViewController())
            case .近期上新:
                viewController?._push(to: HomePageTradingNewlyViewController())
            case .组合K线:
                viewController?.autoLogin { [weak viewController] in
                    viewController?._push(to: CombinationChartEntryViewController())
                }
            case .对比K线:
                viewController?._push(to: TickerComparisonKLineSearchViewController())
            case .币址查询:
                viewController?._push(to: BlockInfoVC())
            case .挖矿数据:
                viewController?._push(to: HomePageMiningDataPageViewController())
            case .矿池份额:
                viewController?._push(to: MiningPoolViewController())
            case .BTC富豪榜:
                viewController?._push(to: HomePageRichRankingListViewController())
            case .K线大师:
                viewController?.autoLogin { [weak viewController] in
                    viewController?._push(to: TrainingCampEntryViewController())
                }
            case .更多:
                let homePageVC = navVC?.aic_topViewController as? HomePageNewViewController
                homePageVC?.selected(tabType: .功能)
                
                // MARK: - 通过tabKey找相应tab(硬编码)，如果业务改了会跳转失败
                
            case .各类指数:
                tabBarVC?.selected(vcType: .ticker) {
                    let tickerVC = navVC?.aic_topViewController as? TickerNewViewController
                    tickerVC?.selected(segmentType: .mix, subTabKey: "mix")
                }
            case .跨期价差:
                tabBarVC?.selected(vcType: .ticker) {
                    let tickerVC = navVC?.aic_topViewController as? TickerNewViewController
                    tickerVC?.selected(segmentType: .futures, subTabKey: "spread")
                }
            case .合约持仓:
                tabBarVC?.selected(vcType: .ticker) {
                    let tickerVC = navVC?.aic_topViewController as? TickerNewViewController
                    tickerVC?.selected(segmentType: .futures, subTabKey: "position")
                }
            case .特色榜单:
                viewController?._push(to: HomePageListDetailViewController())
            case .涨跌分布:
                viewController?._push(to: HomePageIncreaseDetailViewController())
            case .预警中心:
                viewController?._push(to: MeWarningCenterViewController())
                //            case .角标行情:
                //                viewController?._push(to: IconPriceCenterVC())
            case .通知中心行情:
                if let vc = WidgetDataSelectedViewController(type: .widget) {
                    let nav = AICBaseNavigationController(rootViewController: vc)
                    viewController?.present(nav, animated: true, completion: nil)
                }
            case .AppleWatch:
                if #available(iOS 9.0, *) {
                    if let vc = WidgetDataSelectedViewController(type: .appleWatch) {
                        let nav = AICBaseNavigationController(rootViewController: vc)
                        viewController?.present(nav, animated: true, completion: nil)
                    }
                }
            case .社区广场:
                tabBarVC?.selected(vcType: .dynamic) {
                    let momentVC = navVC?.aic_topViewController as? MomentViewController
                    momentVC?.selected(childVCType: .moment)
                }
            case .精选新闻:
                tabBarVC?.selected(vcType: .dynamic) {
                    let momentVC = navVC?.aic_topViewController as? MomentViewController
                    momentVC?.selected(childVCType: .news)
                }
            case .精选快讯:
                tabBarVC?.selected(vcType: .dynamic) {
                    NotificationCenter.default.post(name: Notification.Name("kHotNewsReachWithScrollTop"), object: nil)
                }
            case .平台公告:
                tabBarVC?.selected(vcType: .dynamic) {
                    NotificationCenter.default.post(name: Notification.Name("MomentsMoveToHotNewsPage"), object: 2)
                }
            case .授权管理:
#if !AIC_DISABLE_ADV
                viewController?.autoLogin { [weak viewController] in
                    let vc = AICMultiAccountViewController()
                    vc.hidesBottomBarWhenPushed = true
                    viewController?.p_pushToVC(vc)
                    vc.changeToAuthorize()  //  切换到授权页面
                }
#endif
            case .资产统计:
#if !AIC_DISABLE_ADV
                viewController?.autoLogin { [weak viewController] in
                    let vc = AICMultiAccountViewController()
                    vc.hidesBottomBarWhenPushed = true
                    viewController?.p_pushToVC(vc)
                }
#endif
            case .买卖点记录:
                viewController?._push(to: TradingRecordCenterViewController())
            case .爆仓统计:
                tabBarVC?.selected(vcType: .ticker) {
                    let tickerVC = navVC?.aic_topViewController as? TickerNewViewController
                    tickerVC?.selected(segmentType: .futures, subTabKey: "liq")
                }
            case .悬浮窗:
                let vc = TickerPipSettingViewController.shared
                vc.hidesBottomBarWhenPushed = true
                AppAnalytics.PipWindow.enterFrom(pageName: "首页")
                viewController?._push(to: vc)
            case .AI行情分析:
                MomentsConstant.showFlutterChatRoom(from: viewController, to: .AIAnalysis)
            case .热点排行榜:
                let vc = HotPointRankIistViewController()
                viewController?._push(to: vc)
            case .日历:
                AppAnalytics.shared.track(eventWithCategory: "首页", action: "功能", name: "日历")
                let vc = CalendarViewController()
                viewController?._push(to: vc)
            }
        }
    }
    
    /// 项目对/项目榜单标签
    enum BillboardTag: String, CaseIterable {
        case 涨幅榜
        case 跌幅榜
        case 净流入
        case 换手榜
        case 量比榜
        case 涨速榜
        case 振幅榜
        case 成交额
        
        fileprivate var key: String {
            switch self {
            case .涨幅榜:
                return "day_degree_up"
            case .跌幅榜:
                return "day_degree_down"
            case .净流入:
                return "net_in"
            case .换手榜:
                return "turnover"
            case .量比榜:
                return "count_ratio"
            case .涨速榜:
                return "speed_5min"
            case .振幅榜:
                return "amp_5min"
            case .成交额:
                return "trade"
            }
        }
    }
    
    /// 数据类型
    ///
    /// - trading: 项目对
    /// - coin: 项目
    enum DataType: String, CaseIterable {
        case trading
        case coin
        
        var title: String {
            switch self {
            case .coin:
                return "项目"
            case .trading:
                return "项目对"
            }
        }
    }
    
    /// 资金展示周期
    enum FundsflowPeriod: CaseIterable {
        case today
        case hour24
        case day2
        case day3
        case day4
        case day7
        case day30
        
        var title: String {
            switch self {
            case .today:
                return "今日".home.localized
            case .hour24:
                return "24" + "时".home.localized
            case .day2:
                return "2" + "日".home.localized
            case .day3:
                return "3" + "日".home.localized
            case .day4:
                return "4" + "日".home.localized
            case .day7:
                return "7" + "日".home.localized
            case .day30:
                return "30" + "日".home.localized
            }
        }
    }
    
    /// 最近上新类型
    enum TradingNewlyType {
        case today
        case day7
    }
    
    /// 市值排名
    enum DegreeScatterRank: String, CaseIterable {
        case top10 = "10"
        case top20 = "20"
        case top50 = "50"
        case top100 = "100"
        case top200 = "200"
        case top500 = "500"
        case top1000 = "1000"
        case top2000 = "2000"
        case all
        
        var title: String {
            switch self {
            case .all:
                return "全部".home.localized
            case .top100, .top200, .top500, .top1000, .top2000, .top10, .top20, .top50:
                return self.rawValue
            }
        }
        
        var filterTitle: String {
            switch self {
            case .all:
                return "全部".home.localized
            case .top100, .top200, .top500, .top1000, .top2000, .top10, .top20, .top50:
                return "市值前".home.localized + " " + self.rawValue
            }
        }
    }
    
    /// 最小选择的项目指数数量
    static let minSelectedIndexsCount = 3
    
    /// 至少选择的行情工具
    static let minSelectedTickerToolsCount = 4
    
    /// 至少选择的指标榜单
    static let minSelectedTargetCount = 3
    //    static func gotoStrategyCloud(){}
    @objc static func gotoStrategyCloud() {
        gotoStrategyCloud(trackFrom: "", tabIndex: nil, squareTabIndex: nil, runStrategyType: nil)
    }

    static func gotoStrategyCloud(trackFrom: String = "", tabIndex: Int? = nil, squareTabIndex: Int? = nil, runStrategyType: String? = nil){
        UIViewController.aic_getRootNavigationController()?.autoLogin(loginSuccessCompletion: {
            //            guard let svModel = MeCostant.checkUserService(serviceID: UserServiceType.策略云.rawValue), svModel.openStatus == 1  else {
            //                let svModel = MeCostant.checkUserService(serviceID: UserServiceType.策略云.rawValue)
            //                if let serviceID = svModel?.serviceID {
            //                    MeCostant.showAnotherIntroduction(id:serviceID ,linkID: 1025)
            //                    //                    HomePageRequestOperation.share.getStrategyCloudRecommned { model, error in
            //                    //                        if let openService = model?.openService,let linkID = model?.linkId {
            //                    //                            MeCostant.showAnotherIntroduction(id:serviceID ,linkID: linkID)
            //                    //                        }
            //                    //                    }
            //                }
            //                return
            //            }
//            func getDefaultAccountModelID(marketType: TradeMarketType) -> String? {
//                if let defaultModelID = AICMultiAssetsManager.shared.getDefaultAccountModelID(marketType: marketType) {
//                    if let _ = AICMultiAssetsManager.shared.tradeApiArray.first(where: { $0.isValid && $0.localModelID == defaultModelID }) {
//                        return defaultModelID
//                    }
//                }
//                if let firstApiModel = AICMultiAssetsManager.shared.firstApiModel(marketType: marketType) {
//                    return firstApiModel.localModelID
//                }
//                return nil
//            }
            
            
            func openStrategyCloud(){
                if let appDelegate = UIApplication.shared.delegate as? SetupAppDelegate, let flutterEngine = appDelegate.tradeFlutterEngine {
                    let fvc = FlutterViewController(engine: flutterEngine, nibName: nil, bundle: nil)
                    
                    fvc.modalPresentationStyle = .fullScreen
                    fvc.hidesBottomBarWhenPushed = true
                    // 隐藏导航栏返回按钮
                    fvc.aic_navigationBarHidden = true
                    // 在flutter页面禁用ios原生手势
                    fvc.aic_fullScreenPopGestureEnabled = false
                    fvc.aic_interactivePopMaxAllowedInitialDistanceToLeftEdge = 0.01
                    
                    
                    // viewController?.navigationController?.pushViewController(fvc, animated: true)
                    //            self.pushTo(vc:fvc)
                    UIViewController.aic_getRootNavigationController()?.p_pushToVC(fvc)
                    
                    let channel = FlutterMethodChannel(name: "trade_remote_channel", binaryMessenger: fvc.binaryMessenger)
                    
                    let httpUrl = UserDefaults.standard.url(forKey: "currentHost") ?? AICHttpManager.defaultURL
                    var args : [String: Any] = [
                        "userid":UserManager.share.safeUserID,
                        //                    "okxApi": okxApi,
                        //                    "okxSecret":okxSecret,
                        //                    "okxPassphrase":passphrase,
                        //                    "binanceApi":binanceApi,
                        //                    "binanceSecret":binanceSecret,
                        "tradeRemoteUrl": UserDefaults.standard.string(forKey: "aic_cloudHost") ?? "",
                        "aicoinUrl" : httpUrl.absoluteString,
                        "aicoinId" : UserManager.share.safeIdentifier,
                        "isGuRd" : CandleChartThemeManager.shared.udcType == .gurd,
                    ]
                    if let tabIndex = tabIndex {
                        args["tabIndex"] = tabIndex
                    }
                    if let squareTabIndex = squareTabIndex {
                        args["squareTabIndex"] = squareTabIndex
                    }
                    // 回调成功后才可执行下一次 invokeMethod ，否则上一次的 invokeMethod 会不生效
                    channel.invokeMethod("TradeRemote.setAuthToken", arguments: args, result: {_ in
                        
                    })
                    
                    if runStrategyType != nil {
                        let runStrategyArgs: [String: Any] = [
                            "type" : runStrategyType!
                        ]
                        channel.invokeMethod("TradeRemote.runStrategy", arguments: runStrategyArgs)
                    }
                    
                    channel.setMethodCallHandler { (call, result) in
                        if call.method == "gotoService" {
                            let nav = UIViewController.aic_getRootNavigationController()
                            MomentsConstant.showFlutterChatRoom(from: nav, to: .Friend, arguments: ["uid": "b602aa2f6c", "type" : "user"])
                            result(nil)
                        } else if call.method == "openMarketAccount" {
#if !AIC_DISABLE_ADV
                            UIApplication.shared.mainWindow?.showLoadingView()
                            if let args = call.arguments as? [String : Any] {
                                var marketKey = args["marketType"] as! String
                                
                                if let lastUpdateTime = TradeMarketListModel.sharedModel.lastUpdateMarketInfo, let marketInfo = TradeMarketListModel.sharedModel.marketInfoList[marketKey] {
                                    let now = Date().timeIntervalSince1970
                                    guard abs(now - lastUpdateTime) > 30 * 60 else {
                                        //  如果距离上次更新小于 30 分钟
                                        UIApplication.shared.mainWindow?.hideLoadingView()
                                        let url = marketInfo.registerLink
                                        UIViewController.aic_getRootNavigationController()?.jumpToWebViewController(url)
                                        return
                                    }
                                }

                                AICHttpManager.shared.post("/api/v7/market/info", parameters: ["markets": [marketKey]], progress: nil, success: { (task, data) in
                                    guard let data = data else {
                                        return
                                    }
                                    UIApplication.shared.mainWindow?.hideLoadingView()
                                    let json = JSON(data)
                                    
                                    let value = json["data"]["info"][marketKey].rawValue
                                    var marketInfo = TradeMarketInfo(from: value)
                                    if let url = marketInfo?.registerLink {
                                        UIViewController.aic_getRootNavigationController()?.jumpToWebViewController(url)
                                    }
                                }) { _, _ in
                                    UIApplication.shared.mainWindow?.hideLoadingView()
                                }
                            }
                            
                            result(nil)
#endif
                        } else if call.method == "authMarket" {
#if !AIC_DISABLE_ADV
                            if let args = call.arguments as? [String : Any] {
                                var marketKey = args["marketType"] as! String
                                func authorizedAction(_ type: TradeAuthorizedMarketType, baseVC: UIViewController?) {
                                    if AICTradeCacheManager.share.agreeAuthorization(type) == false {
                                        let avc = AICTradeAuthorizedPromptViewController.init(type: type)
                                        avc.touchAgreeCallBack = {
                                            let vc = TradeAuthorizedInputViewController(type)
                                            vc.authorizedCompleted = {
                                                print("授权完成")
                                                AICMultiAssetsManager.shared.loadApisData {
                                                    AICMultiAssetsManager.shared.reloadMultiAccountDistribution()
                                                    print("获取新列表完成")
                                                    result(nil)
                                                }
                                            }
                                            vc.hidesBottomBarWhenPushed = true
                                            baseVC?.p_pushToVC(vc)
                                        }
                                        let nav = AICBaseNavigationController(rootViewController: avc)
                                        baseVC?.present(nav, animated: true, completion: nil)
                                    } else {
                                        let vc = TradeAuthorizedInputViewController(type)
                                        vc.authorizedCompleted = {
                                            print("授权完成")
                                            AICMultiAssetsManager.shared.loadApisData {
                                                AICMultiAssetsManager.shared.reloadMultiAccountDistribution()
                                                print("获取新列表完成")
                                                result(nil)
                                            }
                                        }
                                        vc.hidesBottomBarWhenPushed = true
                                        baseVC?.p_pushToVC(vc)
                                    }
                                }
                                if let type = TradeFutureMarketType(rawValue: marketKey)?.market {
                                    let vc = UIViewController.aic_getRootNavigationController()
                                    authorizedAction(type, baseVC: vc)
                                    
                                } else if let type = TradeMarketType(rawValue: marketKey) {
                                    UIApplication.shared.mainWindow?.hideLoadingView()
                                    let vc = UIViewController.aic_getRootNavigationController()
                                    authorizedAction(type, baseVC: vc)
                                    
                                } else {
                                    UIApplication.shared.mainWindow?.showLoadingView()
                                    let vc = UIViewController.aic_getRootNavigationController()
                                    
                                    if let lastUpdateTime = TradeMarketListModel.sharedModel.lastUpdateMarketInfo, let marketInfo = TradeMarketListModel.sharedModel.marketInfoList[marketKey] {
                                        let now = Date().timeIntervalSince1970
                                        guard abs(now - lastUpdateTime) > 30 * 60 else {
                                            //  如果距离上次更新小于 30 分钟
                                            UIApplication.shared.mainWindow?.hideLoadingView()
                                            let url = marketInfo.directUrl
                                            vc?.jumpToWebViewController(url)
                                            return
                                        }
                                    }
                                    
                                    if TickerManager.openMarketApp(marketKey) == false {
                                        AICHttpManager.shared.post("/api/v7/market/info", parameters: ["markets": [marketKey]], progress: nil, success: { (task, data) in
                                            guard let data = data else {
                                                return
                                            }
                                            UIApplication.shared.mainWindow?.hideLoadingView()
                                            let json = JSON(data)
                                            
                                            let value = json["data"]["info"][marketKey].rawValue
                                            var marketInfo = TradeMarketInfo(from: value)
                                            let url = marketInfo?.directUrl
                                            if let urlStr = url {
                                                vc?.jumpToWebViewController(urlStr)
                                                result(nil)
                                            }
                                        }) { _, _ in
                                            UIApplication.shared.mainWindow?.hideLoadingView()
                                            result(nil)
                                        }
                                    }
                                }
                                
                            }
                            
#endif
                        } else if call.method == "renewStrategyCloud" {
                            MeCostant.showAnotherIntroduction(id: 10014)
                            result(nil)
                        } else if call.method == "tradeApi" {
#if !AIC_DISABLE_ADV
                            var okxApi = "", okxSecret = "", passphrase = ""
                            var binanceApi = "", binanceSecret = ""
                            var huobiApi = "", huobiSecret = ""
                            
                            if let modelID = getDefaultAccountModelID(marketType: .OKEx) {
                                okxApi = OKExMemberInfoModel.apiKey(modelID: modelID)
                                okxSecret = OKExMemberInfoModel.apiSecretKey(modelID: modelID)
                                passphrase = OKExMemberInfoModel.passphrase(modelID: modelID)
                            }
                            
                            if let modelID = getDefaultAccountModelID(marketType: .Binance) {
                                binanceApi = BinanceMemberInfoModel.apiKey(modelID: modelID)
                                binanceSecret = BinanceMemberInfoModel.apiSecretKey(modelID: modelID)
                            }
                            
                            if let modelID = getDefaultAccountModelID(marketType: .HuobiPro) {
                                huobiApi = HuobiMemberInfoModel.apiKey(modelID: modelID)
                                huobiSecret = HuobiMemberInfoModel.apiSecretKey(modelID: modelID)
                            }

                            if let args = call.arguments as? [String : Any] {
                                var marketKey = args["marketType"] as! String
                                if(marketKey == "okex") {
                                    let dataMap : [String : Any] = [
                                        "api" : okxApi,
                                        "secret" : okxSecret,
                                        "passphrase" : passphrase,
                                    ]
                                    result(dataMap)
                                }else if (marketKey == "binance") {
                                    let dataMap : [String : Any] = [
                                        "api" : binanceApi,
                                        "secret" : binanceSecret,
                                    ]
                                    result(dataMap)
                                }else if (marketKey == "huobipro") {
                                    let dataMap : [String : Any] = [
                                        "api" : huobiApi,
                                        "secret" : huobiSecret,
                                    ]
                                    result(dataMap)
                                }
                            }
#endif
                        }else if call.method == "marketAccount" {
#if !AIC_DISABLE_ADV
                            if let args = call.arguments as? [String : Any] {
                                let marketKey = args["marketType"] as! String
                                let tradeType = args["tradeType"] as! String
                                
                                var okxApi = "", okxSecret = "", passphrase = ""
                                var binanceApi = "", binanceSecret = ""
                                var huobiApi = "", huobiSecret = ""
                                if let modelID = getDefaultAccountModelID(marketType: .OKEx) {
                                    okxApi = OKExMemberInfoModel.apiKey(modelID: modelID)
                                    okxSecret = OKExMemberInfoModel.apiSecretKey(modelID: modelID)
                                    passphrase = OKExMemberInfoModel.passphrase(modelID: modelID)
                                }
                                
                                if let modelID = getDefaultAccountModelID(marketType: .Binance) {
                                    binanceApi = BinanceMemberInfoModel.apiKey(modelID: modelID)
                                    binanceSecret = BinanceMemberInfoModel.apiSecretKey(modelID: modelID)
                                }
                                
                                if let modelID = getDefaultAccountModelID(marketType: .HuobiPro) {
                                    huobiApi = HuobiMemberInfoModel.apiKey(modelID: modelID)
                                    huobiSecret = HuobiMemberInfoModel.apiSecretKey(modelID: modelID)
                                }
                                //  先判断有没有授权
                                if(marketKey == "okex") {
                                    if okxApi.isEmpty || okxSecret.isEmpty || passphrase.isEmpty {
                                        //  未授权，返回 0
                                        let dataMap : [String : Any] = [
                                            "account" : 0
                                        ]
                                        result(dataMap)
                                    }
                                }else if (marketKey == "binance") {
                                    if binanceApi.isEmpty || binanceSecret.isEmpty {
                                        //  未授权，返回 0
                                        let dataMap : [String : Any] = [
                                            "account" : 0
                                        ]
                                        result(dataMap)
                                    }
                                }else if (marketKey == "huobipro") {
                                    if huobiApi.isEmpty || huobiSecret.isEmpty {
                                        //  未授权，返回 0
                                        let dataMap : [String : Any] = [
                                            "account" : 0
                                        ]
                                        result(dataMap)
                                    }
                                }
                                if tradeType == "futures" {
                                    let markets = TradeFutureMarketType.authorizedMarkets
                                    var account = "0"
                                    if marketKey == "okex" {
                                        //  OKX 的现货和合约可用获取的轮询时间不一，可能会出现显示偏差，因此直接使用现货的可用
                                        if let modelID = getDefaultAccountModelID(marketType: .OKEx), let marketAsset = AICTradeCacheManager.share.marketAssetsDict[modelID], let asset = marketAsset.allAssets["usdt"] {
                                            account = "\(asset.free)"
                                        }
                                    }else {
                                        var marketType = TradeFutureMarketType.Binance
                                        if marketKey == "binance" {
                                            marketType = TradeFutureMarketType.Binance
                                        }else if marketKey == "huobipro" {
                                            marketType = TradeFutureMarketType.HuobiPro
                                        }
                                        if let item = markets.first(where: {$0.marketType == marketType}), let tradeMarketType = TradeMarketType(rawValue: item.rawValue), let modelID = getDefaultAccountModelID(marketType: tradeMarketType) , let assets = item.assets[modelID]?["usdt"] {
                                            account = "\(assets.free)"
                                        }
                                    }
                                    let dataMap : [String : Any] = [
                                        "account" : account
                                    ]
                                    result(dataMap)
                                }else if tradeType == "spot" {
                                    let markets = TradeFutureMarketType.authorizedMarkets
                                    var account = "0"
                                    var marketType = TradeMarketType.Binance
                                    if marketKey == "binance" {
                                        marketType = TradeMarketType.Binance
                                    }else if marketKey == "okex" {
                                        marketType = TradeMarketType.OKEx
                                    }else if marketKey == "huobipro" {
                                        marketType = TradeMarketType.HuobiPro
                                    }
                                    if let modelID = getDefaultAccountModelID(marketType: marketType), let marketAsset = AICTradeCacheManager.share.marketAssetsDict[modelID], let asset = marketAsset.allAssets["usdt"] {
                                        account = "\(asset.free)"
                                    }                                    
                                    let dataMap : [String : Any] = [
                                        "account" : account
                                    ]
                                    result(dataMap)
                                }else{
                                    result(nil)
                                }
                            }else{
                                result(nil)
                            }
#endif
                        }else if call.method == "tickerMarketTabs" {
#if !AIC_DISABLE_ADV
                            var dataArray: [[String: Any]] = []
                            var tabs = [TickerTabModel]()
                            let supportKeys = ["binance", "okex", "huobipro"]
                            tabs += TickerPreferenceManager.shared.optinalSet.tabs      //  添加自选部分
                            //  添加市场部分
                            tabs += AICTickerDataBase.share.fetchAllTabModelsWithTab(.marketList).filter({ tab in
                                return supportKeys.contains { item in
                                    item == tab.key
                                }
                            })
                            
                            dataArray = tabs.map { item in
                                return [
                                    "key": "\(item.key)",
                                    "title_cn": "\(item.title_cn)",
                                    "title_en": "\(item.title_en)",
                                    "id": "\(item.id)",
                                    "tabType": "\(item.tabType)",
                                    "supTabType": "\(item.supTab.rawValue)"
                                ]
                            }
                            
                            result(["data" : dataArray])
#endif
                        }else if call.method == "tickerMarketTabsDataList" {
#if !AIC_DISABLE_ADV
                            if let args = call.arguments as? [String : Any] {
                                let key = args["key"] as! String
                                let id = args["id"] as! String
                                let tabType = args["tabType"] as! Int
                                let supTabType = args["supTabType"] as! String
                                
                                if key == "optional" || tabType == 99 {     //  自选
                                    TickerCustomTabData.shareInstance.getMarketsForCustomTab(id: id) { (models, _) in
                                        if let models = models {
                                            let keys = models.filter({ $0.customType == .tradingPair }).compactMap({ $0.key })
                                            let data = AICTickerDataBase.share.fetchTickerMarketListModelWithMarketListKeys(keys)
                                            let dataArray = data.map { item in
                                                return [
                                                    "symbol": "\(item.marketListKey)",
                                                    "coin_cn": "\(item.coin_cn)",
                                                    "coin_en": "\(item.coin_en)",
                                                    "market_cn": "\(item.market_cn)",
                                                    "market_en": "\(item.market_en)",
                                                    "market_key": "\(item.marketType)",
                                                    "coinShow": "\(item.coin_show)",
                                                    "currency": "\(item.currencyString)",
                                                    "isFutures" : item.isFutures
                                                ]
                                            }
                                            result(["data" : dataArray])
                                        }
                                    }
                                }else{
                                    if let supTab = TickerConstant.SupTabType.init(rawValue: supTabType) {
                                        let data = AICTickerDataBase.share.fetchTickerMarketListWithTab(supTab, tab: key)
                                        let dataArray = data.map { item in
                                            return [
                                                "symbol": "\(item.marketListKey)",
                                                "coin_cn": "\(item.coin_cn)",
                                                "coin_en": "\(item.coin_en)",
                                                "market_cn": "\(item.market_cn)",
                                                "market_en": "\(item.market_en)",
                                                "market_key": "\(item.marketType)",
                                                "coinShow": "\(item.coin_show)",
                                                "currency": "\(item.currencyString)",
                                                "isFutures" : item.isFutures
                                            ]
                                        }
                                        result(["data" : dataArray])
                                    }
                                }
                            }
#endif
                        }else if call.method == "searchTickerMarketTabsDataList" {
#if !AIC_DISABLE_ADV
                            if let args = call.arguments as? [String : Any] {
                                let searchText = args["searchText"] as? String ?? ""
                                let tokens = searchText.lowercased().searchTokenize
                                
                                let data = AICTickerDataBase.share.tickerFuzzySearch(keyword: nil, tokenize: tokens)?.sortByTradingPairStyle(tokenize: tokens) ?? []
                                
                                let dataArray = data.map { item in
                                    return [
                                        "symbol": "\(item.marketListKey)",
                                        "coin_cn": "\(item.coin_cn)",
                                        "coin_en": "\(item.coin_en)",
                                        "market_cn": "\(item.market_cn)",
                                        "market_en": "\(item.market_en)",
                                        "market_key": "\(item.marketType)",
                                        "coinShow": "\(item.coin_show)",
                                        "currency": "\(item.currencyString)",
                                        "isFutures" : item.isFutures
                                    ]
                                }
                                result(["data" : dataArray])
                            }
#endif
                        }else if call.method == "getPositionSide" {
#if !AIC_DISABLE_ADV
                            //  获取平台持仓方向
                            if let args = call.arguments as? [String : Any] {
                                let marketKey = args["marketType"] as! String
                                let dbKey = args["dbKey"] as! String
                                let market = AICTickerDataBase.share.fetchMarketListModel(withKey: dbKey)
                                if let market = market {
                                    if marketKey == "binance" {
                                        if let modelID = getDefaultAccountModelID(marketType: .Binance) {
                                            if market.currencyString == "usdt" {
                                                BinanceFutureAPI.shared.positionMode(modelID:modelID, marketList: market) { position in
                                                    result("\(position)")
                                                }
                                            }else{
                                                BinanceCoinFutureAPI.shared.positionMode(modelID:modelID, marketList: market) { position in
                                                    result("\(position)")
                                                }
                                            }
                                        }
                                    }else if marketKey == "okex" {
                                        if let modelID = getDefaultAccountModelID(marketType: .OKEx) {
                                            OKExFutureAPI.shared.positionMode(modelID: modelID) {
                                                if OKExFutureAPI.shared.posMode[modelID] == "net_mode" {
                                                    result("false")
                                                }else if OKExFutureAPI.shared.posMode[modelID] == "long_short_mode" {
                                                    result("true")
                                                }
                                            }
                                        }
                                    }else if marketKey == "huobipro" {
                                        if let modelID = getDefaultAccountModelID(marketType: .HuobiPro) {
                                            if market.currencyString == "usdt" {
                                                HuobiProUsdtFutureAPI.shared.positionMode(.crossed(""), modelID: modelID, marketList: market) { position in
                                                    result("\(position)")
                                                }
                                            }else if market.subTradeType == .swap{
                                                result(nil)
                                            }else if market.subTradeType == .delivery {
                                                result(nil)
                                            }
                                        }
                                    }
                                }
                            }
#endif
                        }else if call.method == "getAccountPosition" {
#if !AIC_DISABLE_ADV
                            //  获取保证金模式和杠杆倍数
                            if let args = call.arguments as? [String : Any] {
                                let marketKey = args["marketType"] as! String
                                let dbKey = args["dbKey"] as! String
                                let market = AICTickerDataBase.share.fetchMarketListModel(withKey: dbKey)
                                if let market = market {
                                    if marketKey == "binance" {
                                        if let modelID = getDefaultAccountModelID(marketType: .Binance) {
                                            if market.currencyString == "usdt" {
                                                BinanceFutureAPI.shared.requestLeverage(modelID: modelID, marketList: market) { accountMode in
                                                    switch accountMode {
                                                    case .crossed(let leverage):
                                                        result([
                                                            "isolated": "false",
                                                            "leverage": "\(leverage)",
                                                        ])
                                                    case .fixed(let leverage, _):
                                                        result([
                                                            "isolated": "true",
                                                            "leverage": "\(leverage)",
                                                        ])
                                                    }
                                                }
                                            }else {
                                                BinanceCoinFutureAPI.shared.requestLeverage(modelID: modelID, marketList: market)  { accountMode in
                                                    switch accountMode {
                                                    case .crossed(let leverage):
                                                        result([
                                                            "isolated": "false",
                                                            "leverage": "\(leverage)",
                                                        ])
                                                    case .fixed(let leverage, _):
                                                        result([
                                                            "isolated": "true",
                                                            "leverage": "\(leverage)",
                                                        ])
                                                    }
                                                }
                                            }
                                        }
                                    }else if marketKey == "okex" {
                                        if let modelID = getDefaultAccountModelID(marketType: .OKEx) {
                                            let isolatedStr = args["isolated"] as? String ?? "false"
                                            let isolated = isolatedStr == "true"
                                            var accoumtMode = TradeFutureAccountMode.crossed("0")
                                            if isolated {
                                                accoumtMode = TradeFutureAccountMode.fixed("0", "0")
                                            }
                                            OKExFutureAPI.shared.requestLeverage(modelID: modelID, accountMode: accoumtMode, marketList: market) { accountMode in
                                                switch accountMode {
                                                case .crossed(let leverage):
                                                    result([
                                                        "isolated": "false",
                                                        "leverage": "\(leverage)",
                                                    ])
                                                case .fixed(let leverage, _):
                                                    result([
                                                        "isolated": "true",
                                                        "leverage": "\(leverage)",
                                                    ])
                                                }
                                            }
                                        }
                                    }else if marketKey == "huobipro" {
                                        if let modelID = getDefaultAccountModelID(marketType: .HuobiPro) {
                                            if market.currencyString == "usdt" {
                                                
                                                HuobiProFutureAPI.shared.requestLeverage(modelID: modelID, marketList: market) { accountMode in
                                                    switch accountMode {
                                                    case .crossed(let leverage):
                                                        result([
                                                            "isolated": "false",
                                                            "leverage": "\(leverage)",
                                                        ])
                                                    case .fixed(let leverage, _):
                                                        result([
                                                            "isolated": "true",
                                                            "leverage": "\(leverage)",
                                                        ])
                                                    }
                                                }
                                            }else if market.subTradeType == .swap{
                                                HuobiProCoinSwapAPI.shared.requestLeverage(modelID:modelID, marketList: market) { accountMode in
                                                    switch accountMode {
                                                    case .crossed(let leverage):
                                                        result([
                                                            "isolated": "false",
                                                            "leverage": "\(leverage)",
                                                        ])
                                                    case .fixed(let leverage, _):
                                                        result([
                                                            "isolated": "true",
                                                            "leverage": "\(leverage)",
                                                        ])
                                                    }
                                                }
                                            }else if market.subTradeType == .delivery {
                                                HuobiProCoinFuturesAPI.shared.requestLeverage(modelID:modelID, marketList: market) { accountMode in
                                                    switch accountMode {
                                                    case .crossed(let leverage):
                                                        result([
                                                            "isolated": "false",
                                                            "leverage": "\(leverage)",
                                                        ])
                                                    case .fixed(let leverage, _):
                                                        result([
                                                            "isolated": "true",
                                                            "leverage": "\(leverage)",
                                                        ])
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
#endif
                        }else if call.method == "setPositionSide" {
#if !AIC_DISABLE_ADV
                            if let args = call.arguments as? [String : Any] {
                                //  更新持仓模式
                                let marketKey = args["marketType"] as? String ?? ""
                                let dbKey = args["dbKey"] as? String ?? ""
                                let positionMode = args["positionMode"] as? String ?? ""
                                let isDualSideStr = args["isDualSide"] as? String ?? "false"
                                let isolatedStr = args["isolated"] as? String ?? "false"
                                var isDualSide = false
                                var isolated = false
                                
                                if isDualSideStr == "false" {
                                    isDualSide = false
                                }else if isDualSideStr == "true" {
                                    isDualSide = true
                                }
                                
                                if isolatedStr == "false" {
                                    isolated = false
                                }else if isolatedStr == "true" {
                                    isolated = true
                                }
                                
                                let market = AICTickerDataBase.share.fetchMarketListModel(withKey: dbKey)
                                if let market = market {
                                    if marketKey == "binance" {
                                        if let modelID = getDefaultAccountModelID(marketType: .Binance) {
                                            if market.currencyString == "usdt" {
                                                BinanceFutureAPI.shared.changPositionMode(modelID: modelID, marketList: market) {
                                                    result(true)
                                                }
                                            }else {
                                                BinanceCoinFutureAPI.shared.changPositionMode(modelID: modelID, dualSidePosition: isDualSide) {
                                                    result(true)
                                                }
                                            }
                                        }
                                    }else if marketKey == "okex" {
                                        if let modelID = getDefaultAccountModelID(marketType: .OKEx) {
                                            OKExFutureAPI.shared.changPositionMode(modelID: modelID, posMode: positionMode) {
                                                result(true)
                                            }
                                        }
                                    }else if marketKey == "huobipro" {
                                        if let modelID = getDefaultAccountModelID(marketType: .HuobiPro) {
                                            if market.currencyString == "usdt" {
                                                let positionMode = isDualSide ? "dual_side" : "single_side"
                                                let accountMode = isolated ? TradeFutureAccountMode.fixed("", "") : TradeFutureAccountMode.crossed("")
                                                HuobiProUsdtFutureAPI.shared.changPositionMode(modelID: modelID, positionMode: positionMode, mode: accountMode, marketList: market) {
                                                    result(true)
                                                }
                                            }else if market.subTradeType == .swap {     //  火币币本位没有持仓模式
                                                result(false)
                                            }else if market.subTradeType == .delivery { //  火币币本位没有持仓模式
                                                result(false)
                                            }
                                        }
                                    }
                                }
                            }
#endif
                        }else if call.method == "setAccountPosition" {
#if !AIC_DISABLE_ADV
                            if let args = call.arguments as? [String : Any] {
                                let marketKey = args["marketType"] as? String ?? ""
                                let dbKey = args["dbKey"] as? String ?? ""
                                let isolatedStr = args["isolated"] as? String ?? "false"
                             
                                let leverage = args["leverage"] as? String ?? ""
                                var accountMode = TradeFutureAccountMode.crossed("20")
                                
                                var isolated = false
                                if isolatedStr == "false" {
                                    isolated = false
                                }else if isolatedStr == "true" {
                                    isolated = true
                                }
                                
                                if isolated {
                                    accountMode = .fixed(leverage, leverage)
                                }else {
                                    accountMode = .crossed(leverage)
                                }
                                let market = AICTickerDataBase.share.fetchMarketListModel(withKey: dbKey)
                                if let market = market {
                                    if marketKey == "binance" {
                                        if let modelID = getDefaultAccountModelID(marketType: .Binance) {
                                            if market.currencyString == "usdt" {     //  U 本位
                                                BinanceFutureAPI.shared.setAccountMode(accountMode, modelID: modelID, marketList: market) {
                                                    result(true)
                                                }
                                            }else if market.isSwap {    //  币本位永续
                                                BinanceCoinFutureAPI.shared.setAccountMode(accountMode, modelID: modelID, marketList: market) {
                                                    result(true)
                                                }
                                            }
                                        }
                                    }else if marketKey == "okex" {
                                        if let modelID = getDefaultAccountModelID(marketType: .OKEx) {
                                            OKExFutureAPI.shared.setLeverage(side: "", accountMode, modelID: modelID, marketList: market) { _ in
                                                result(true)
                                            }
                                        }
                                    }else if marketKey == "huobipro" {
                                        if let modelID = getDefaultAccountModelID(marketType: .OKEx) {
                                            if market.currencyString  == "usdt" {     //  U 本位
                                                HuobiProUsdtFutureAPI.shared.setAccountMode(accountMode, modelID: modelID, marketList: market) {
                                                    result(true)
                                                }
                                            }else if market.subTradeType == .swap {   //  币本位永续
                                                HuobiProCoinSwapAPI.shared.setAccountMode(accountMode, modelID: modelID, marketList: market) {
                                                    result(true)
                                                }
                                            }else if market.subTradeType == .delivery { //  币本位交割
                                                HuobiProCoinFuturesAPI.shared.setAccountMode(accountMode, modelID: modelID, marketList: market) {
                                                    result(true)
                                                }
                                            }
                                        }
                                    }
                                }
                            }
#endif
                        } else if call.method == "originTickerSelect" {
                            if let args = call.arguments as? [String : Any] {
                                let maxSelect = args["maxSelect"] as? Int ?? 1
                                let selectedSymbols = args["selectedSymbols"] as? [String] ?? []
                                
                                showTickerSelector(maxSelect: maxSelect, selectedSymbols: selectedSymbols) { marketModels in
                                    
                                    var dataArray : [[String: Any]] = []
                                    for item in marketModels {
                                        dataArray.append([
                                            "symbol": "\(item?.marketListKey ?? "")" ,
                                            "coin_cn": "\(item?.coin_cn ?? "")",
                                            "coin_en": "\(item?.coin_en ?? "")",
                                            "market_cn": "\(item?.market_cn ?? "")",
                                            "market_en": "\(item?.market_en ?? "")",
                                            "market_key": "\(item?.marketType ?? "")",
                                            "coinShow": "\(item?.coin_show ?? "")",
                                            "currency": "\(item?.currencyString ?? "")",
                                            "isFutures" : item?.isFutures ?? false
                                        ])
                                    }
                                    result(["data" : dataArray])
                                }
                            }
                        } else if call.method == "toTradePage" {
                            if let args = call.arguments as? [String : Any] {
                                let dbKey = args["dbKey"] as? String ?? ""
                                let market = AICTickerDataBase.share.fetchMarketListModel(withKey: dbKey)
                                market?.newjumpToDealVC()
                            }
                        } else if call.method == "toNewsDetail" {
                            if let args = call.arguments as? [String: Any] {
                                let id = "\(String(describing: args["id"] ?? ""))"
                                if let _ = Double(id) {
                                    // 跳转到新闻详情页
                                    DispatchQueue.main.async {
                                        UIViewController.aic_getRootNavigationController()?.jumpToNewsDetailVC(articelId: "\(id)")
                                    }
                                    result(true)
                                }
                                result(false)
                            } else {
                                result(false)
                            }
                        }
                        else {
                            result(FlutterMethodNotImplemented)
                        }
                    }
                    
                    let matomoChannel = FlutterMethodChannel(name: "channel_matomo", binaryMessenger: fvc.binaryMessenger)
                    
                    matomoChannel.setMethodCallHandler { (call, result) in
                        if call.method == "upload_event" {
                            if let args = call.arguments as? [String : Any] {
                                let type = args["type"] as? String ?? ""
                                let action = args["action"] as? String ?? ""
                                let name = args["name"] as? String ?? ""
                                
                                ServiceAnalytics.shared.track(eventWithCategory: type, action: action, name: name)
                            }
                        }
                    }
                    
                }
            }
            
            // let svModel = MeCostant.checkUserService(serviceID: UserServiceType.策略云.rawValue)
            // if svModel?.openStatus != 1 {
            //     MeRequestOperation.share.requestUserServices { (datas, error) in        //  增加一层接口判断
            //         let newModel = MeCostant.checkUserService(serviceID: UserServiceType.策略云.rawValue)
            //         if newModel?.openStatus != 1 {
            //             if let serviceID = svModel?.serviceID {
            //                 MeCostant.showAnotherIntroduction(id:serviceID ,linkID: 1025, trackFrom: trackFrom)
            //             }
            //             return
            //         }else{
            //             openStrategyCloud()
            //         }
            //     }
            // }else{
                openStrategyCloud()
            // }
            
            
            
        })
        return
        
        
        
        
    }
    
    private static func showTickerSelector(maxSelect: Int, selectedSymbols: [String], completed: (([TickerMarketListModel?])->())?){
        let vc = TickerSelectedMarketListViewController(selectedMarketListKeys: selectedSymbols, limitSelected: maxSelect)
        vc.hidesBottomBarWhenPushed = true
        vc.saveCallBack = { keys in
            let marketModels = keys.map { key in
                return AICTickerDataBase.share.fetchMarketListModel(withKey: key)
            }
            completed?(marketModels)
        }
        vc.exceedLimitCallback = { limit in
            let tipStr =  String(format: "最多支持【%d】个交易对".home.localized, limit)
            let window = UIApplication.shared.mainWindow
            window?.makeToast(tipStr)
        }
        let nav = UIViewController.aic_getRootNavigationController()
        nav?.pushViewController(vc, animated: true)
    }
    
    
}

private extension UIViewController {
    func _push(to vc: UIViewController) {
        vc.hidesBottomBarWhenPushed = true
        if let nav = self as? UINavigationController {
            nav.pushViewController(vc, animated: true)
        }else {
            self.navigationController?.pushViewController(vc, animated: true)
        }
    }
}
