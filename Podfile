#project 'AICoin'
require "fileutils"
#require_relative 'patch_static_framework'
platform :ios, '13.0'

#install! 'cocoapods', generate_multiple_pod_projects: true

use_frameworks! :linkage => :static
#use_modular_headers!

source 'https://github.com/CocoaPods/Specs.git'
source 'https://github.com/aliyun/aliyun-specs.git'
##source 'http://team.aicoin.net.cn/lab/Huangjian/private-space.git'

inhibit_all_warnings!

project 'AICoin.xcodeproj'

def third_login_pods
 #  如果需要使用模拟器运行，指定 mob_sharesdk 为 4.4.17， MOBFundation 的版本为3.2.51(在podfile.lock 里修改)
  pod 'mob_sharesdk', '4.4.17'
  pod 'mob_sharesdk/ShareSDKPlatforms/GooglePlus'
  pod 'mob_sharesdk/ShareSDKPlatforms/Apple'
  pod 'mob_sharesdk/ShareSDKPlatforms/Twitter'
  pod 'mob_sharesdk/ShareSDKPlatforms/Telegram'
  
#  pod 'AppAuth'
end


target '<PERSON><PERSON>oin' do
  #  use_frameworks!
  # Pods for Flutter
  flutter_application_path = '../aicoin_flutter_modules'
  load File.join(flutter_application_path, '.ios', 'Flutter', 'podhelper.rb')
  install_all_flutter_pods(flutter_application_path)

pod 'YYKit_modify', :path => 'module/YYKit_modify'
pod 'InternalToolKit', :path => 'module/InternalToolKit'
pod 'AICNetworking', :path => 'module/Networking'
pod 'SocketRocket', :git => 'https://github.com/facebookarchive/SocketRocket.git' #, :path => 'module/SocketRocket'
pod 'MatomoTracker', :path => 'module/MatomoTracker'
pod 'SharePlatforms', :path => 'module/SharePlatforms'
pod 'LDNetDiagnoService',:path => 'module/LDNetDiagnoService'
#pod 'ReactiveObjC'
#pod 'Bees', '2.0'

pod 'SwiftDate'
pod "dsBridge"
pod 'tingyunApp'
pod 'UMengAnalytics'
pod 'Masonry'
pod 'BuglyHotfix'
pod 'TZImagePickerController'
pod 'MLeaksFinder', :configurations => ['Debug']
# pod 'JPush'     3.0.56（2024/10/24）移除
pod 'MJRefresh'
pod 'RTRootNavigationController', '0.6.7'
pod 'WMPageController'
pod 'IQKeyboardManager'
pod 'MarqueeLabel'
pod 'SDWebImage' 
pod 'TwitterSignIn', :path => 'module/TwitterSignIn'
pod 'TPNS-iOS', '1.3.9.5'

#pod 'AlicloudHTTPDNS', '2.0.6'
#pod 'JWT'

pod 'SnapKit'
pod 'WCDB.swift', '= 1.1.0'

pod 'Protobuf', '= 3.22.1'
pod 'SVGAPlayer'
pod 'UITableView+FDTemplateLayoutCell' 
pod 'Down'		# 用于解析 MARKDown
pod 'MessageKit', :path => 'module/MessageKit'
pod 'ZMarkupParser'
pod 'JTAppleCalendar'

third_login_pods

#pod 'Sentry'

dynamic_framework = ['isar_flutter_libs']
             
pre_install do |installer|
  Pod::Installer::Xcode::TargetValidator.send(:define_method, :verify_no_static_framework_transitive_dependencies) {}
  installer.pod_targets.each {| pod |
    if dynamic_framework.include?(pod.name)
      puts "turn to dynamic framework name:" + pod.name
      def pod.dynamic_framework;
        true
      end
      def pod.build_type;
        Pod::BuildType.dynamic_framework
      end
    end
  }
end

target 'AICoin_App' do
  install_all_flutter_pods(flutter_application_path)
end

target 'AICoin_Test' do
  install_all_flutter_pods(flutter_application_path)
end

end


target 'AICoinWidgetNews' do
  platform :ios, '13.0'
  use_frameworks! :linkage => :static

  pod 'AICNetworking', :path => 'module/Networking'

  target 'AICoinWidgetNews_App'
  target 'AICoinWidgetNews_Test'
  
end

target 'AICoinWidget' do
  platform :ios, '13.0'
  use_frameworks! :linkage => :static
  
  pod 'AICNetworking', :path => 'module/Networking'

  target 'AICoinWidget_App'
  target 'AICoinWidget_Test'
  
end

target 'AICoinWigetKitExtension' do
  platform :ios, '14.0'
  use_frameworks! :linkage => :static
  pod 'SwiftyJSON', '~> 4.0'
  
  target 'AICoinWigetKitExtension_Test'
  
end

target 'watchkitapp Extension' do
  platform :watchos, '3.0'
  pod 'AFNetworking'
  
  target 'watchkitapp Extension_App'
  target 'watchkitapp Extension_Test'
  
end


post_install do |installer|
  # Flutter post
  flutter_post_install(installer) if defined?(flutter_post_install)
  ## Fix for XCode 12.5
  find_and_replace("Pods/WCDB.swift/swift/source/core/base/Database.swift","guard pages > subthreadCheckpointPages else {", "guard false else {")
  find_and_replace("Pods/FBRetainCycleDetector/FBRetainCycleDetector/Layout/Classes/FBClassStrongLayout.mm","layoutCache[currentClass] = ivars;", "layoutCache[(id<NSCopying>)currentClass] = ivars;")
  find_and_replace("Pods/FBRetainCycleDetector/fishhook/fishhook.c","indirect_symbol_bindings[i] = cur->rebindings[j].replacement;", "if(i < (sizeof(indirect_symbol_bindings) /sizeof(indirect_symbol_bindings[0]))) {indirect_symbol_bindings[i] = cur->rebindings[j].replacement;}")
  installer.pods_project.targets.each do |target|
    #    flutter_additional_ios_build_settings(target)
    target.build_configurations.each do |config|
      config.build_settings['ONLY_ACTIVE_ARCH'] = 'NO'
      config.build_settings['GCC_WARN_INHIBIT_ALL_WARNINGS'] = "YES"
    end
    if target.name == 'WCDB'
          target.build_configurations.each do |config|
            config.build_settings['OTHER_LDFLAGS'] ||= []
            config.build_settings['OTHER_LDFLAGS'] << '-Wl,-ld_classic'
          end
    end
    if target.name == 'FBRetainCycleDetector'
          target.build_configurations.each do |config|
            config.build_settings['OTHER_CFLAGS'] ||= ['$(inherited)']
            config.build_settings['OTHER_CFLAGS'] << '-fbracket-depth=512'
            config.build_settings['OTHER_CPLUSPLUSFLAGS'] ||= ['$(inherited)']
            config.build_settings['OTHER_CPLUSPLUSFLAGS'] << '-fbracket-depth=512'
          end
    end
  end
  installer.generated_projects.each do |project|
    project.build_configurations.each do |config|
      if config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'].to_f < 11.0
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.0'
      end
    end
    project.targets.each do |target|
      target.build_configurations.each do |config|
        if config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'].to_f < 11.0
          config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.0'
        end
      end
    end
  end
end

def find_and_replace(dir, findstr, replacestr)
  Dir[dir].each do |name|
    FileUtils.chmod("+w", name) #add
    text = File.read(name)
    replace = text.gsub(findstr,replacestr)
    if text != replace
      puts "Fix: " + name
      File.open(name, "w") { |file| file.puts replace }
      STDOUT.flush
    end
  end
  Dir[dir + '*/'].each(&method(:find_and_replace))
end

